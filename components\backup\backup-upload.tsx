"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Loader2, Upload } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export function BackupUpload() {
  const { toast } = useToast()
  const [file, setFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0])
    }
  }

  // Upload and restore backup
  const uploadBackup = async () => {
    if (!file) return

    try {
      setUploading(true)

      // Create form data
      const formData = new FormData()
      formData.append("file", file)

      // Upload the file
      const response = await fetch("/api/backups/upload", {
        method: "POST",
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message || "Backup uploaded and restored successfully",
        })
        setFile(null)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to upload and restore backup",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upload and restore backup",
        variant: "destructive",
      })
    } finally {
      setUploading(false)
      setShowConfirmDialog(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Backup</CardTitle>
        <CardDescription>Upload a backup file to restore your data</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="backup-file">Backup File</Label>
            <Input id="backup-file" type="file" accept=".json,.gz" onChange={handleFileChange} disabled={uploading} />
            <p className="text-sm text-muted-foreground">Select a backup file to upload (.json or .gz format)</p>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={() => setShowConfirmDialog(true)} disabled={!file || uploading}>
          {uploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload & Restore
            </>
          )}
        </Button>
      </CardFooter>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Restore from Uploaded Backup</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to restore from this backup file? This will replace all current data with the data
              from the backup. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={uploadBackup} className="bg-primary">
              {uploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Restoring...
                </>
              ) : (
                "Restore"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}

