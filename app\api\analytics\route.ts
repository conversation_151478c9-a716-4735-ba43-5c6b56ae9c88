import { type NextRequest, NextResponse } from "next/server"
import { Redis } from "@upstash/redis"

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
})

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate the data
    if (!data.deviceId || !data.session) {
      return NextResponse.json({ error: "Invalid analytics data" }, { status: 400 })
    }

    // Store session data
    const sessionKey = `analytics:session:${data.session.sessionId}`
    await redis.set(sessionKey, JSON.stringify(data.session))

    // Add session ID to device sessions list
    const deviceSessionsKey = `analytics:device:${data.deviceId}:sessions`
    await redis.sadd(deviceSessionsKey, data.session.sessionId)

    // Store events in a time series
    if (data.session.events && data.session.events.length > 0) {
      const pipeline = redis.pipeline()

      for (const event of data.session.events) {
        // Store event
        const eventKey = `analytics:event:${data.session.sessionId}:${event.timestamp}`
        pipeline.set(eventKey, JSON.stringify(event))

        // Add to event type index
        const eventTypeKey = `analytics:eventType:${event.eventName}`
        pipeline.sadd(eventTypeKey, eventKey)

        // Add to time series
        pipeline.zadd("analytics:events:timeline", {
          score: event.timestamp,
          member: eventKey,
        })
      }

      await pipeline.exec()
    }

    // Store performance metrics
    if (data.performanceMetrics && data.performanceMetrics.length > 0) {
      const pipeline = redis.pipeline()

      for (const metric of data.performanceMetrics) {
        // Store metric
        const metricKey = `analytics:metric:${data.deviceId}:${metric.name}:${metric.timestamp}`
        pipeline.set(metricKey, JSON.stringify(metric))

        // Add to metric type index
        const metricTypeKey = `analytics:metricType:${metric.name}`
        pipeline.sadd(metricTypeKey, metricKey)

        // Add to time series
        pipeline.zadd(`analytics:metrics:${metric.name}:timeline`, {
          score: metric.timestamp,
          member: metricKey,
        })
      }

      await pipeline.exec()
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error processing analytics data:", error)

    return NextResponse.json({ error: "Failed to process analytics data" }, { status: 500 })
  }
}

