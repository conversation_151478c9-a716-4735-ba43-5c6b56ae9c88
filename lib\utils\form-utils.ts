import { ZodError } from "zod"

export function formatZodError(error: ZodError) {
  const formattedErrors: Record<string, string> = {}

  error.errors.forEach((err) => {
    if (err.path.length > 0) {
      const path = err.path.join(".")
      formattedErrors[path] = err.message
    }
  })

  return formattedErrors
}

export function handleServerValidationError(error: unknown) {
  if (error instanceof ZodError) {
    return formatZodError(error)
  }

  return null
}

