import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const layaway = await prisma.layaway.findUnique({
      where: { id: params.id },
      include: {
        customer: true,
        items: {
          include: {
            product: true,
            variant: true,
          },
        },
        payments: {
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    })

    if (!layaway) {
      return NextResponse.json({ error: "Layaway not found" }, { status: 404 })
    }

    return NextResponse.json(layaway)
  } catch (error) {
    console.error("Error fetching layaway:", error)
    return NextResponse.json({ error: "Failed to fetch layaway" }, { status: 500 })
  }
}

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const { status, notes, expiryDate } = body

    // Validate the layaway exists
    const layaway = await prisma.layaway.findUnique({
      where: { id: params.id },
      include: {
        items: true,
      },
    })

    if (!layaway) {
      return NextResponse.json({ error: "Layaway not found" }, { status: 404 })
    }

    // Update the layaway in a transaction
    const updatedLayaway = await prisma.$transaction(async (tx) => {
      // Update the layaway
      const layawayRecord = await tx.layaway.update({
        where: { id: params.id },
        data: {
          status: status || layaway.status,
          notes: notes !== undefined ? notes : layaway.notes,
          expiryDate: expiryDate ? new Date(expiryDate) : layaway.expiryDate,
        },
      })

      // If status is CANCELLED, return inventory
      if (status === "CANCELLED" && layaway.status !== "CANCELLED") {
        for (const item of layaway.items) {
          if (item.variantId) {
            await tx.productVariant.update({
              where: { id: item.variantId },
              data: {
                inventoryLevel: {
                  increment: item.quantity,
                },
              },
            })
          } else {
            await tx.product.update({
              where: { id: item.productId },
              data: {
                inventoryLevel: {
                  increment: item.quantity,
                },
              },
            })
          }

          // Add inventory history record
          await tx.inventoryHistory.create({
            data: {
              productId: item.productId,
              quantity: item.quantity,
              type: "ADJUSTMENT",
              reference: params.id,
              notes: `Layaway #${layaway.layawayNumber} cancelled`,
            },
          })
        }

        // Create notification for the cancellation
        await tx.notification.create({
          data: {
            title: "Layaway Cancelled",
            message: `Layaway #${layaway.layawayNumber} has been cancelled and inventory has been returned to stock.`,
            type: "INFO",
            userId: session.user.id,
          },
        })
      }

      // If status is COMPLETED, create an order
      if (status === "COMPLETED" && layaway.status !== "COMPLETED") {
        // Generate order number
        const orderNumber = await generateOrderNumber()

        // Create order
        const order = await tx.order.create({
          data: {
            orderNumber,
            status: "COMPLETED",
            paymentStatus: "PAID",
            subtotal: layaway.totalAmount,
            tax: 0, // Assuming tax is included in the layaway total
            total: layaway.totalAmount,
            customerId: layaway.customerId,
            userId: session.user.id,
            notes: `Converted from Layaway #${layaway.layawayNumber}`,
          },
        })

        // Create order items
        for (const item of layaway.items) {
          await tx.orderItem.create({
            data: {
              orderId: order.id,
              productId: item.productId,
              variantId: item.variantId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              subtotal: item.total,
              tax: 0, // Assuming tax is included in the item total
              total: item.total,
            },
          })
        }

        // Create order status history
        await tx.orderStatusHistory.create({
          data: {
            orderId: order.id,
            status: "COMPLETED",
            notes: `Order created from Layaway #${layaway.layawayNumber}`,
          },
        })

        // Create notification for the completion
        await tx.notification.create({
          data: {
            title: "Layaway Completed",
            message: `Layaway #${layaway.layawayNumber} has been completed and converted to Order #${orderNumber}.`,
            type: "SUCCESS",
            userId: session.user.id,
          },
        })
      }

      return layawayRecord
    })

    return NextResponse.json(updatedLayaway)
  } catch (error) {
    console.error("Error updating layaway:", error)
    return NextResponse.json({ error: "Failed to update layaway" }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if layaway exists
    const layaway = await prisma.layaway.findUnique({
      where: { id: params.id },
      include: {
        items: true,
      },
    })

    if (!layaway) {
      return NextResponse.json({ error: "Layaway not found" }, { status: 404 })
    }

    // Only allow deletion of layaways in ACTIVE status
    if (layaway.status !== "ACTIVE") {
      return NextResponse.json({ error: "Only active layaways can be deleted" }, { status: 400 })
    }

    // Delete the layaway and return inventory in a transaction
    await prisma.$transaction(async (tx) => {
      // Return inventory
      for (const item of layaway.items) {
        if (item.variantId) {
          await tx.productVariant.update({
            where: { id: item.variantId },
            data: {
              inventoryLevel: {
                increment: item.quantity,
              },
            },
          })
        } else {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              inventoryLevel: {
                increment: item.quantity,
              },
            },
          })
        }

        // Add inventory history record
        await tx.inventoryHistory.create({
          data: {
            productId: item.productId,
            quantity: item.quantity,
            type: "ADJUSTMENT",
            reference: params.id,
            notes: `Layaway #${layaway.layawayNumber} deleted`,
          },
        })
      }

      // Delete the layaway
      await tx.layaway.delete({
        where: { id: params.id },
      })
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting layaway:", error)
    return NextResponse.json({ error: "Failed to delete layaway" }, { status: 500 })
  }
}

// Helper function to generate order number
async function generateOrderNumber() {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, "0")
  const day = date.getDate().toString().padStart(2, "0")

  const prefix = `ORD-${year}${month}${day}-`

  // Get the latest order number with this prefix
  const latestOrder = await prisma.order.findFirst({
    where: {
      orderNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      orderNumber: "desc",
    },
  })

  let sequence = 1
  if (latestOrder) {
    const latestSequence = Number.parseInt(latestOrder.orderNumber.split("-")[2])
    sequence = latestSequence + 1
  }

  return `${prefix}${sequence.toString().padStart(4, "0")}`
}

