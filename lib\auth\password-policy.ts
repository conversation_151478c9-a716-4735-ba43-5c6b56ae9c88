import { db } from "../db"
import crypto from "crypto"

export interface PasswordPolicy {
  minLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSpecialChars: boolean
  preventCommonPasswords: boolean
  maxAge: number // in days, 0 means no expiration
  historyCount: number // number of previous passwords to remember
  maxAttempts: number // max failed attempts before lockout
  lockoutDuration: number // in minutes
}

export const DEFAULT_PASSWORD_POLICY: PasswordPolicy = {
  minLength: 10,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  preventCommonPasswords: true,
  maxAge: 90, // 90 days
  historyCount: 5, // remember last 5 passwords
  maxAttempts: 5, // 5 failed attempts
  lockoutDuration: 15, // 15 minutes
}

// Common passwords to prevent (this would be a much larger list in production)
const COMMON_PASSWORDS = ["password", "123456", "qwerty", "admin", "welcome", "password123"]

export class PasswordPolicyService {
  /**
   * Get the current password policy
   */
  static async getPolicy(): Promise<PasswordPolicy> {
    const policy = await db.settings.findFirst({
      where: { key: "passwordPolicy" },
    })

    if (!policy) {
      return DEFAULT_PASSWORD_POLICY
    }

    return JSON.parse(policy.value)
  }

  /**
   * Update the password policy
   */
  static async updatePolicy(policy: Partial<PasswordPolicy>): Promise<PasswordPolicy> {
    const currentPolicy = await this.getPolicy()
    const newPolicy = { ...currentPolicy, ...policy }

    await db.settings.upsert({
      where: { key: "passwordPolicy" },
      update: { value: JSON.stringify(newPolicy) },
      create: {
        key: "passwordPolicy",
        value: JSON.stringify(newPolicy),
      },
    })

    return newPolicy
  }

  /**
   * Validate a password against the policy
   */
  static async validatePassword(password: string, userId?: string): Promise<{ isValid: boolean; errors: string[] }> {
    const policy = await this.getPolicy()
    const errors: string[] = []

    // Check length
    if (password.length < policy.minLength) {
      errors.push(`Password must be at  = [];
    
    // Check length
    if (password.length < policy.minLength) {
      errors.push(\`Password must be at least ${policy.minLength} characters long`)
    }

    // Check for uppercase letters
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter")
    }

    // Check for lowercase letters
    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter")
    }

    // Check for numbers
    if (policy.requireNumbers && !/[0-9]/.test(password)) {
      errors.push("Password must contain at least one number")
    }

    // Check for special characters
    if (policy.requireSpecialChars && !/[^A-Za-z0-9]/.test(password)) {
      errors.push("Password must contain at least one special character")
    }

    // Check for common passwords
    if (policy.preventCommonPasswords && COMMON_PASSWORDS.includes(password.toLowerCase())) {
      errors.push("Password is too common and easily guessed")
    }

    // Check password history if userId is provided
    if (userId && policy.historyCount > 0) {
      const isInHistory = await this.isPasswordInHistory(userId, password)
      if (isInHistory) {
        errors.push(`Password has been used recently. Please choose a different password`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * Check if a password is in the user's password history
   */
  static async isPasswordInHistory(userId: string, password: string): Promise<boolean> {
    const policy = await this.getPolicy()

    // Get the user's password history
    const history = await db.passwordHistory.findMany({
      where: { userId },
      orderBy: { createdAt: "desc" },
      take: policy.historyCount,
    })

    // Check if the password matches any in the history
    for (const entry of history) {
      // In a real implementation, you would use a proper password hashing library
      const hashedPassword = crypto.createHash("sha256").update(password).digest("hex")

      if (entry.passwordHash === hashedPassword) {
        return true
      }
    }

    return false
  }

  /**
   * Add a password to the user's password history
   */
  static async addToPasswordHistory(userId: string, passwordHash: string): Promise<void> {
    await db.passwordHistory.create({
      data: {
        userId,
        passwordHash,
        createdAt: new Date(),
      },
    })

    // Clean up old history entries
    const policy = await this.getPolicy()

    if (policy.historyCount > 0) {
      const history = await db.passwordHistory.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
      })

      if (history.length > policy.historyCount) {
        // Delete older entries
        const entriesToDelete = history.slice(policy.historyCount)
        for (const entry of entriesToDelete) {
          await db.passwordHistory.delete({
            where: { id: entry.id },
          })
        }
      }
    }
  }

  /**
   * Check if a user's password has expired
   */
  static async isPasswordExpired(userId: string): Promise<boolean> {
    const policy = await this.getPolicy()

    // If maxAge is 0, passwords never expire
    if (policy.maxAge === 0) {
      return false
    }

    // Get the user's most recent password change
    const latestChange = await db.passwordHistory.findFirst({
      where: { userId },
      orderBy: { createdAt: "desc" },
    })

    if (!latestChange) {
      // If no history, assume the password is from user creation
      const user = await db.users.findUnique({
        where: { id: userId },
      })

      if (!user) {
        return false
      }

      const daysSinceCreation = Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24))

      return daysSinceCreation > policy.maxAge
    }

    // Calculate days since last password change
    const daysSinceChange = Math.floor((Date.now() - latestChange.createdAt.getTime()) / (1000 * 60 * 60 * 24))

    return daysSinceChange > policy.maxAge
  }

  /**
   * Record a failed login attempt
   */
  static async recordFailedAttempt(userId: string): Promise<{
    isLocked: boolean
    attemptsRemaining: number
    lockoutEnd?: Date
  }> {
    const policy = await this.getPolicy()

    // Get current failed attempts
    const user = await db.users.findUnique({
      where: { id: userId },
      select: {
        failedLoginAttempts: true,
        lockedUntil: true,
      },
    })

    if (!user) {
      throw new Error("User not found")
    }

    // Check if the user is already locked out
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      return {
        isLocked: true,
        attemptsRemaining: 0,
        lockoutEnd: user.lockedUntil,
      }
    }

    // Increment failed attempts
    const newAttempts = (user.failedLoginAttempts || 0) + 1

    // Check if the user should be locked out
    let isLocked = false
    let lockoutEnd: Date | undefined

    if (newAttempts >= policy.maxAttempts) {
      isLocked = true
      lockoutEnd = new Date(Date.now() + policy.lockoutDuration * 60 * 1000)

      // Update the user record
      await db.users.update({
        where: { id: userId },
        data: {
          failedLoginAttempts: 0, // Reset attempts
          lockedUntil: lockoutEnd,
        },
      })
    } else {
      // Update the user record
      await db.users.update({
        where: { id: userId },
        data: {
          failedLoginAttempts: newAttempts,
        },
      })
    }

    return {
      isLocked,
      attemptsRemaining: policy.maxAttempts - newAttempts,
      lockoutEnd,
    }
  }

  /**
   * Reset failed login attempts
   */
  static async resetFailedAttempts(userId: string): Promise<void> {
    await db.users.update({
      where: { id: userId },
      data: {
        failedLoginAttempts: 0,
        lockedUntil: null,
      },
    })
  }
}

