"use client"

import type React from "react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { cn } from "@/lib/utils"
import { HelpCircle } from "lucide-react"

interface HelpItem {
  id: string
  title: string
  content: React.ReactNode
}

interface ContextualHelpProps {
  items: HelpItem[]
  placement?: "inline" | "icon" | "button"
  title?: string
  className?: string
}

export function ContextualHelp({ items, placement = "icon", title = "Help & Tips", className }: ContextualHelpProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeItemId, setActiveItemId] = useState<string | null>(items.length > 0 ? items[0].id : null)

  const activeItem = items.find((item) => item.id === activeItemId)

  const renderTrigger = () => {
    switch (placement) {
      case "inline":
        return (
          <Button variant="link" className="h-auto p-0 text-muted-foreground" onClick={() => setIsOpen(true)}>
            Need help?
          </Button>
        )
      case "button":
        return (
          <Button variant="outline" size="sm" onClick={() => setIsOpen(true)} className={className}>
            <HelpCircle className="mr-2 h-4 w-4" />
            Help
          </Button>
        )
      case "icon":
      default:
        return (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsOpen(true)}
            className={cn("rounded-full", className)}
            aria-label="Open help"
          >
            <HelpCircle className="h-5 w-5" />
          </Button>
        )
    }
  }

  return (
    <>
      {renderTrigger()}

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            {activeItem && <DialogDescription>{activeItem.title}</DialogDescription>}
          </DialogHeader>

          <div className="grid gap-6 py-4 md:grid-cols-[200px_1fr]">
            {/* Help topics sidebar */}
            <div className="flex flex-col space-y-1">
              {items.map((item) => (
                <Button
                  key={item.id}
                  variant={activeItemId === item.id ? "secondary" : "ghost"}
                  className="justify-start"
                  onClick={() => setActiveItemId(item.id)}
                >
                  {item.title}
                </Button>
              ))}
            </div>

            {/* Help content */}
            <div className="rounded-md border p-4">
              {activeItem ? (
                <div className="prose prose-sm max-w-none dark:prose-invert">{activeItem.content}</div>
              ) : (
                <p className="text-sm text-muted-foreground">Select a topic from the left to view help content.</p>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

