import { z } from "zod"

export const orderItemSchema = z.object({
  productId: z.string().min(1, "Product is required"),
  quantity: z.coerce.number().int("Quantity must be a whole number").positive("Quantity must be positive"),
  unitPrice: z.coerce.number().min(0, "Price cannot be negative"),
  variantId: z.string().optional(),
})

export const orderSchema = z.object({
  customerId: z.string().min(1, "Customer is required"),
  orderDate: z.date({
    required_error: "Order date is required",
    invalid_type_error: "Invalid date format",
  }),
  status: z.enum(
    [
      "PENDING",
      "PROCESSING",
      "AWAITING_PAYMENT",
      "AWAITING_FULFILLMENT",
      "AWAITING_SHIPMENT",
      "PARTIALLY_SHIPPED",
      "SHIPPED",
      "DELIVERED",
      "COMPLETED",
      "CANCELLED",
      "ON_HOLD",
      "BACKORDERED",
    ],
    {
      invalid_type_error: "Invalid order status",
    },
  ),
  paymentStatus: z.enum(["UNPAID", "PARTIALLY_PAID", "PAID", "REFUNDED", "PARTIALLY_REFUNDED", "VOIDED"], {
    invalid_type_error: "Invalid payment status",
  }),
  paymentMethod: z
    .enum(
      ["CASH", "CREDIT_CARD", "DEBIT_CARD", "BANK_TRANSFER", "CHECK", "PAYPAL", "STORE_CREDIT", "GIFT_CARD", "OTHER"],
      {
        invalid_type_error: "Invalid payment method",
      },
    )
    .optional(),
  shippingAddress: z.string().optional(),
  billingAddress: z.string().optional(),
  notes: z.string().optional(),
  items: z.array(orderItemSchema).min(1, "Order must contain at least one item"),
})

export type OrderFormValues = z.infer<typeof orderSchema>
export type OrderItemFormValues = z.infer<typeof orderItemSchema>

