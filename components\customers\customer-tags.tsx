"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"
import { Loader2, Plus, Trash2, Edit } from "lucide-react"

interface CustomerTag {
  id: string
  name: string
  color: string
  _count?: {
    customers: number
  }
}

export function CustomerTags() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [tags, setTags] = useState<CustomerTag[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [newTag, setNewTag] = useState({
    name: "",
    color: "#3b82f6",
  })
  const [editingTag, setEditingTag] = useState<CustomerTag | null>(null)

  const fetchTags = async () => {
    try {
      setIsLoading(true)
      const response = await fetchApi("/api/customer-tags")
      setTags(response)
    } catch (error) {
      console.error("Failed to fetch customer tags:", error)
      toast({
        title: "Error",
        description: "Failed to load customer tags",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchTags()
  }, [toast])

  const handleAddTag = async () => {
    try {
      await fetchApi("/api/customer-tags", {
        method: "POST",
        body: JSON.stringify(newTag),
      })

      toast({
        title: "Success",
        description: "Customer tag added successfully",
      })

      setIsAddDialogOpen(false)
      setNewTag({
        name: "",
        color: "#3b82f6",
      })

      fetchTags()
    } catch (error) {
      console.error("Failed to add customer tag:", error)
      toast({
        title: "Error",
        description: "Failed to add customer tag",
        variant: "destructive",
      })
    }
  }

  const handleEditTag = async () => {
    if (!editingTag) return

    try {
      await fetchApi(`/api/customer-tags/${editingTag.id}`, {
        method: "PUT",
        body: JSON.stringify({
          name: editingTag.name,
          color: editingTag.color,
        }),
      })

      toast({
        title: "Success",
        description: "Customer tag updated successfully",
      })

      setIsEditDialogOpen(false)
      setEditingTag(null)

      fetchTags()
    } catch (error) {
      console.error("Failed to update customer tag:", error)
      toast({
        title: "Error",
        description: "Failed to update customer tag",
        variant: "destructive",
      })
    }
  }

  const handleDeleteTag = async (id: string) => {
    if (!confirm("Are you sure you want to delete this tag?")) return

    try {
      await fetchApi(`/api/customer-tags/${id}`, {
        method: "DELETE",
      })

      toast({
        title: "Success",
        description: "Customer tag deleted successfully",
      })

      fetchTags()
    } catch (error) {
      console.error("Failed to delete customer tag:", error)
      toast({
        title: "Error",
        description: "Failed to delete customer tag",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Customer Tags</CardTitle>
            <CardDescription>Manage tags for customer segmentation</CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Tag
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Customer Tag</DialogTitle>
                <DialogDescription>Create a new tag for categorizing customers.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    className="col-span-3"
                    value={newTag.name}
                    onChange={(e) => setNewTag({ ...newTag, name: e.target.value })}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="color" className="text-right">
                    Color
                  </Label>
                  <div className="col-span-3 flex items-center gap-2">
                    <Input
                      id="color"
                      type="color"
                      className="w-16 h-10"
                      value={newTag.color}
                      onChange={(e) => setNewTag({ ...newTag, color: e.target.value })}
                    />
                    <Input value={newTag.color} onChange={(e) => setNewTag({ ...newTag, color: e.target.value })} />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" onClick={handleAddTag}>
                  Add Tag
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {tags.length > 0 ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tag</TableHead>
                  <TableHead>Color</TableHead>
                  <TableHead>Customers</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tags.map((tag) => (
                  <TableRow key={tag.id}>
                    <TableCell>
                      <Badge style={{ backgroundColor: tag.color, color: "#fff" }}>{tag.name}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full border" style={{ backgroundColor: tag.color }} />
                        {tag.color}
                      </div>
                    </TableCell>
                    <TableCell>{tag._count?.customers || 0}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setEditingTag(tag)
                            setIsEditDialogOpen(true)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleDeleteTag(tag.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No tags found. Create your first tag to get started.
          </div>
        )}
      </CardContent>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Customer Tag</DialogTitle>
            <DialogDescription>Update the tag details.</DialogDescription>
          </DialogHeader>
          {editingTag && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="edit-name"
                  className="col-span-3"
                  value={editingTag.name}
                  onChange={(e) => setEditingTag({ ...editingTag, name: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-color" className="text-right">
                  Color
                </Label>
                <div className="col-span-3 flex items-center gap-2">
                  <Input
                    id="edit-color"
                    type="color"
                    className="w-16 h-10"
                    value={editingTag.color}
                    onChange={(e) => setEditingTag({ ...editingTag, color: e.target.value })}
                  />
                  <Input
                    value={editingTag.color}
                    onChange={(e) => setEditingTag({ ...editingTag, color: e.target.value })}
                  />
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button type="button" onClick={handleEditTag}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

