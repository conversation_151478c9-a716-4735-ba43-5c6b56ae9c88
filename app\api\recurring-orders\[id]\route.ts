import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const recurringOrder = await prisma.recurringOrder.findUnique({
      where: { id: params.id },
      include: {
        customer: true,
        items: {
          include: {
            product: true,
            variant: true,
          },
        },
        orderHistory: {
          orderBy: {
            scheduledDate: "desc",
          },
        },
      },
    })

    if (!recurringOrder) {
      return NextResponse.json({ error: "Recurring order not found" }, { status: 404 })
    }

    return NextResponse.json(recurringOrder)
  } catch (error) {
    console.error("Error fetching recurring order:", error)
    return NextResponse.json({ error: "Failed to fetch recurring order" }, { status: 500 })
  }
}

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const { status, frequency, nextOrderDate, endDate, paymentMethod, shippingAddress, billingAddress, notes } = body

    // Validate the recurring order exists
    const recurringOrder = await prisma.recurringOrder.findUnique({
      where: { id: params.id },
    })

    if (!recurringOrder) {
      return NextResponse.json({ error: "Recurring order not found" }, { status: 404 })
    }

    // Update the recurring order
    const updatedRecurringOrder = await prisma.recurringOrder.update({
      where: { id: params.id },
      data: {
        status: status || recurringOrder.status,
        frequency: frequency || recurringOrder.frequency,
        nextOrderDate: nextOrderDate ? new Date(nextOrderDate) : recurringOrder.nextOrderDate,
        endDate: endDate ? new Date(endDate) : recurringOrder.endDate,
        paymentMethod: paymentMethod || recurringOrder.paymentMethod,
        shippingAddress: shippingAddress !== undefined ? shippingAddress : recurringOrder.shippingAddress,
        billingAddress: billingAddress !== undefined ? billingAddress : recurringOrder.billingAddress,
        notes: notes !== undefined ? notes : recurringOrder.notes,
      },
    })

    // Create notification for the update
    if (status && status !== recurringOrder.status) {
      await prisma.notification.create({
        data: {
          title: "Recurring Order Updated",
          message: `Recurring order status has been updated to ${status.replace(/_/g, " ").toLowerCase()}.`,
          type: "INFO",
          userId: session.user.id,
        },
      })
    }

    return NextResponse.json(updatedRecurringOrder)
  } catch (error) {
    console.error("Error updating recurring order:", error)
    return NextResponse.json({ error: "Failed to update recurring order" }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if recurring order exists
    const recurringOrder = await prisma.recurringOrder.findUnique({
      where: { id: params.id },
    })

    if (!recurringOrder) {
      return NextResponse.json({ error: "Recurring order not found" }, { status: 404 })
    }

    // Delete the recurring order
    await prisma.recurringOrder.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting recurring order:", error)
    return NextResponse.json({ error: "Failed to delete recurring order" }, { status: 500 })
  }
}

