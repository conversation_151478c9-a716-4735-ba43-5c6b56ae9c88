import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { returnSchema } from "@/lib/validations/return-schema"
import { generateReturnNumber } from "@/lib/utils"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const customerId = searchParams.get("customerId")
    const orderId = searchParams.get("orderId")
    const status = searchParams.get("status")

    const where: any = {}

    if (customerId) where.customerId = customerId
    if (orderId) where.orderId = orderId
    if (status) where.status = status

    const returns = await prisma.return.findMany({
      where,
      include: {
        customer: true,
        order: true,
        items: {
          include: {
            product: true,
            variant: true,
            orderItem: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(returns)
  } catch (error) {
    console.error("Error fetching returns:", error)
    return NextResponse.json({ error: "Failed to fetch returns" }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()

    try {
      returnSchema.parse(body)
    } catch (error) {
      return NextResponse.json({ error: "Invalid return data", details: error }, { status: 400 })
    }

    const { orderId, customerId, reason, notes, items } = body

    // Generate a unique return number
    const returnNumber = await generateReturnNumber()

    // Create the return and its items in a transaction
    const newReturn = await prisma.$transaction(async (tx) => {
      // Create the return
      const returnRecord = await tx.return.create({
        data: {
          returnNumber,
          status: "REQUESTED",
          reason,
          notes,
          orderId,
          customerId,
          createdById: session.user.id,
          statusHistory: {
            create: {
              status: "REQUESTED",
              notes: "Return requested by customer",
            },
          },
        },
      })

      // Create return items
      for (const item of items) {
        await tx.returnItem.create({
          data: {
            returnId: returnRecord.id,
            orderItemId: item.orderItemId,
            productId: item.productId,
            variantId: item.variantId || null,
            quantity: item.quantity,
            reason: item.reason,
            condition: item.condition,
            refundAmount: item.refundAmount || null,
          },
        })
      }

      // Create notification for the return
      await tx.notification.create({
        data: {
          title: "New Return Request",
          message: `Return #${returnNumber} has been created and is awaiting review.`,
          type: "INFO",
          userId: session.user.id,
        },
      })

      return returnRecord
    })

    return NextResponse.json(newReturn, { status: 201 })
  } catch (error) {
    console.error("Error creating return:", error)
    return NextResponse.json({ error: "Failed to create return" }, { status: 500 })
  }
}

