import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { generateOrderNumber } from "@/lib/utils"

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Validate the recurring order exists
    const recurringOrder = await prisma.recurringOrder.findUnique({
      where: { id: params.id },
      include: {
        customer: true,
        items: {
          include: {
            product: true,
            variant: true,
          },
        },
      },
    })

    if (!recurringOrder) {
      return NextResponse.json({ error: "Recurring order not found" }, { status: 404 })
    }

    // Check if the recurring order is active
    if (recurringOrder.status !== "ACTIVE") {
      return NextResponse.json({ error: "Cannot process inactive recurring order" }, { status: 400 })
    }

    // Process the recurring order in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Generate order number
      const orderNumber = await generateOrderNumber()

      // Calculate totals
      const subtotal = recurringOrder.items.reduce((sum, item) => sum + item.total, 0)
      const tax = 0 // Assuming tax is calculated elsewhere or included in item prices
      const total = subtotal + tax

      // Create order
      const order = await tx.order.create({
        data: {
          orderNumber,
          status: "PROCESSING",
          paymentStatus: "UNPAID",
          paymentMethod: recurringOrder.paymentMethod,
          subtotal,
          tax,
          shipping: 0, // Assuming shipping is calculated elsewhere
          discount: 0, // Assuming no discounts
          total,
          shippingAddress: recurringOrder.shippingAddress,
          billingAddress: recurringOrder.billingAddress,
          notes: `Generated from recurring order #${recurringOrder.id}`,
          customerId: recurringOrder.customerId,
          userId: session.user.id,
        },
      })

      // Create order items
      for (const item of recurringOrder.items) {
        await tx.orderItem.create({
          data: {
            orderId: order.id,
            productId: item.productId,
            variantId: item.variantId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            subtotal: item.total,
            tax: 0, // Assuming tax is included in the item total
            total: item.total,
          },
        })

        // Update inventory
        if (item.variantId) {
          await tx.productVariant.update({
            where: { id: item.variantId },
            data: {
              inventoryLevel: {
                decrement: item.quantity,
              },
            },
          })
        } else {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              inventoryLevel: {
                decrement: item.quantity,
              },
            },
          })
        }

        // Add inventory history record
        await tx.inventoryHistory.create({
          data: {
            productId: item.productId,
            quantity: -item.quantity,
            type: "SALE",
            reference: order.id,
            notes: `Order #${orderNumber} from recurring order`,
          },
        })
      }

      // Create order status history
      await tx.orderStatusHistory.create({
        data: {
          orderId: order.id,
          status: "PROCESSING",
          notes: `Order created from recurring order #${recurringOrder.id}`,
        },
      })

      // Add to recurring order history
      const orderHistory = await tx.recurringOrderHistory.create({
        data: {
          recurringOrderId: recurringOrder.id,
          orderId: order.id,
          scheduledDate: recurringOrder.nextOrderDate,
          processedDate: new Date(),
          status: "COMPLETED",
          notes: `Order #${orderNumber} created`,
        },
      })

      // Calculate next order date based on frequency
      let nextOrderDate = new Date(recurringOrder.nextOrderDate)

      switch (recurringOrder.frequency) {
        case "DAILY":
          nextOrderDate.setDate(nextOrderDate.getDate() + 1)
          break
        case "WEEKLY":
          nextOrderDate.setDate(nextOrderDate.getDate() + 7)
          break
        case "BIWEEKLY":
          nextOrderDate.setDate(nextOrderDate.getDate() + 14)
          break
        case "MONTHLY":
          nextOrderDate.setMonth(nextOrderDate.getMonth() + 1)
          break
        case "QUARTERLY":
          nextOrderDate.setMonth(nextOrderDate.getMonth() + 3)
          break
        case "BIANNUALLY":
          nextOrderDate.setMonth(nextOrderDate.getMonth() + 6)
          break
        case "ANNUALLY":
          nextOrderDate.setFullYear(nextOrderDate.getFullYear() + 1)
          break
      }

      // Check if next order date is after end date
      let newStatus = recurringOrder.status
      if (recurringOrder.endDate && nextOrderDate > recurringOrder.endDate) {
        nextOrderDate = recurringOrder.endDate
        newStatus = "COMPLETED"
      }

      // Update recurring order with new next order date
      const updatedRecurringOrder = await tx.recurringOrder.update({
        where: { id: recurringOrder.id },
        data: {
          nextOrderDate,
          status: newStatus,
        },
      })

      // Create notification
      await tx.notification.create({
        data: {
          title: "Recurring Order Processed",
          message: `Recurring order processed and Order #${orderNumber} created.`,
          type: "SUCCESS",
          userId: session.user.id,
        },
      })

      return { order, orderHistory, updatedRecurringOrder }
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error("Error processing recurring order:", error)
    return NextResponse.json({ error: "Failed to process recurring order" }, { status: 500 })
  }
}

