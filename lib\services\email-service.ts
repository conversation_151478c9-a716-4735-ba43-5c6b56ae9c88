import nodemailer from "nodemailer"

interface EmailAttachment {
  filename: string
  content: Buffer
  contentType?: string
}

interface SendEmailOptions {
  to: string | string[]
  subject: string
  text?: string
  html?: string
  attachments?: EmailAttachment[]
}

// Create a transporter using SMTP configuration
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: Number.parseInt(process.env.SMTP_PORT || "587"),
    secure: process.env.SMTP_SECURE === "true",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  })
}

// Send an email
export async function sendEmail(options: SendEmailOptions): Promise<boolean> {
  try {
    const transporter = createTransporter()

    const mailOptions = {
      from: process.env.SMTP_FROM,
      to: Array.isArray(options.to) ? options.to.join(",") : options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
      attachments: options.attachments,
    }

    const info = await transporter.sendMail(mailOptions)
    console.log("Email sent:", info.messageId)
    return true
  } catch (error) {
    console.error("Error sending email:", error)
    return false
  }
}

// Send a scheduled report email
export async function sendScheduledReport(
  schedule: any,
  reportData: any,
  attachmentBuffer: Buffer,
  format: string,
): Promise<boolean> {
  try {
    const { name, recipients, subject, message } = schedule
    const reportName = reportData.report?.name || "Report"
    const executedAt = reportData.executedAt ? new Date(reportData.executedAt) : new Date()

    // Determine content type based on format
    let contentType = "text/csv"
    let extension = "csv"

    if (format === "excel") {
      contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      extension = "xlsx"
    } else if (format === "pdf") {
      contentType = "application/pdf"
      extension = "pdf"
    }

    // Create filename
    const timestamp = format(executedAt, "yyyyMMdd_HHmmss")
    const filename = `${reportName.replace(/\s+/g, "_")}_${timestamp}.${extension}`

    // Create HTML content
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">${reportName}</h2>
        <p>Your scheduled report is attached to this email.</p>
        ${message ? `<p>${message}</p>` : ""}
        <div style="margin-top: 20px; padding: 10px; background-color: #f5f5f5; border-radius: 4px;">
          <p style="margin: 0;"><strong>Report:</strong> ${reportName}</p>
          <p style="margin: 0;"><strong>Generated:</strong> ${format(executedAt, "PPpp")}</p>
          <p style="margin: 0;"><strong>Format:</strong> ${format.toUpperCase()}</p>
        </div>
        <p style="margin-top: 20px; font-size: 12px; color: #666;">
          This is an automated email from your StockSync inventory system.
        </p>
      </div>
    `

    // Send email with attachment
    return await sendEmail({
      to: recipients,
      subject: subject || `${reportName} - ${format(executedAt, "PPP")}`,
      html,
      attachments: [
        {
          filename,
          content: attachmentBuffer,
          contentType,
        },
      ],
    })
  } catch (error) {
    console.error("Error sending scheduled report email:", error)
    return false
  }
}

