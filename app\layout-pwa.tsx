"use client"

import { useEffect } from "react"

export function PWASetup() {
  useEffect(() => {
    // Register service worker
    if ("serviceWorker" in navigator) {
      window.addEventListener("load", () => {
        navigator.serviceWorker
          .register("/service-worker.js")
          .then((registration) => {
            console.log("Service Worker registered with scope:", registration.scope)
          })
          .catch((error) => {
            console.error("Service Worker registration failed:", error)
          })
      })
    }

    // Handle beforeinstallprompt event
    let deferredPrompt: any

    window.addEventListener("beforeinstallprompt", (e) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault()
      // Stash the event so it can be triggered later
      deferredPrompt = e

      // Update UI to notify the user they can add to home screen
      const installButton = document.getElementById("install-button")
      if (installButton) {
        installButton.style.display = "block"

        installButton.addEventListener("click", () => {
          // Show the install prompt
          deferredPrompt.prompt()

          // Wait for the user to respond to the prompt
          deferredPrompt.userChoice.then((choiceResult: { outcome: string }) => {
            if (choiceResult.outcome === "accepted") {
              console.log("User accepted the install prompt")
            } else {
              console.log("User dismissed the install prompt")
            }
            deferredPrompt = null

            // Hide the install button
            installButton.style.display = "none"
          })
        })
      }
    })

    // Handle app installed event
    window.addEventListener("appinstalled", () => {
      console.log("PWA was installed")

      // Hide the install button
      const installButton = document.getElementById("install-button")
      if (installButton) {
        installButton.style.display = "none"
      }
    })
  }, [])

  return null
}

