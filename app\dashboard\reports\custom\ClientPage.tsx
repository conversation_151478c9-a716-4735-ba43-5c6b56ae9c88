"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { CustomReportsList } from "@/components/reports/custom-reports-list"

export default function CustomReportsPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Custom Reports</h2>
          <p className="text-muted-foreground">Create, manage, and run custom reports for your business</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/reports/custom/new">
            <Plus className="mr-2 h-4 w-4" />
            New Report
          </Link>
        </Button>
      </div>

      <CustomReportsList />
    </div>
  )
}

