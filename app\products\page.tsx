"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Plus, Trash, Download, Upload, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DataTable, type Column } from "@/components/ui/data-table"
import { BulkActions } from "@/components/ui/bulk-actions"
import { ContextualHelp } from "@/components/ui/contextual-help"
import { PageHeader, Section, Container } from "@/components/ui/design-system"
import { useAccessibility } from "@/providers/accessibility-provider"
import { useToast } from "@/components/ui/use-toast"

interface Product {
  id: string
  name: string
  sku: string
  category: string
  price: number
  stock: number
  status: "in-stock" | "low-stock" | "out-of-stock"
}

export default function ProductsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { announceMessage } = useAccessibility();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedProducts, setSelectedProducts] = useState<Record<string, boolean>>({});
  const [sortBy, setSortBy] = useState<string | null>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc" | null>("asc");
  const [filter, setFilter] = useState("");
  const [pageIndex, setPageIndex] = useState(0);
  const pageSize = 10;

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        
        // Mock data
        const mockProducts: Product[] = Array.from({ length: 50 }).map((_, i) => ({
          id: `prod-${i + 1}`,
          name: `Product ${i + 1}`,
          sku: `SKU-${1000 + i}`,
          category: `Category ${Math.floor(i / 10) + 1}`,
          price: Math.floor(Math.random() * 100) + 10,
          stock: Math.floor(Math.random() * 100),
          status: Math.random() > 0.7 
            ? "low-stock" 
            : Math.random() > 0.9 
              ? "out-of-stock" 
              : "in-stock",
        }));
        
        setProducts(mockProducts);
      } catch (error) {
        console.error("Error fetching products:", error);
        toast({
          title: "Error",
          description: "Failed to load products. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [toast]);

  // Handle sort change
  const handleSortChange = (column: string, direction: "asc" | "desc") => {
    setSortBy(column);
    setSortDirection(direction);
  };

  // Handle filter change
  const handleFilterChange = (value: string) => {
    setFilter(value);
    setPageIndex(0); // Reset to first page when filtering
  };

  // Handle row selection
  const handleSelectRow = (id: string, selected: boolean) => {
    setSelectedProducts((prev) => ({
      ...prev,
      [id]: selected,
    }));
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    const newSelected: Record<string, boolean> = {};
    
    displayedProducts.forEach((product) => {
      newSelected[product.id] = selected;
    });
    
    setSelectedProducts(newSelected);
    
    announceMessage(
      selected
        ? `Selected all ${displayedProducts.length} products`
        : "Deselected all products",
      "polite"
    );
  };

  // Handle row click
  const handleRowClick = (product: Product) => {
    router.push(`/products/${product.id}`);
  };

  // Bulk actions
  const bulkActions = [
    {
      id: "delete",
      label: "Delete",
      icon: <Trash className="h-4 w-4" />,
      variant: "destructive" as const,
      confirmationRequired: true,
      confirmationMessage: "Are you sure you want to delete the selected products? This action cannot be undone.",
      action: async (selectedItems: Product[]) => {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        
        // Remove selected products
        setProducts((prev) =>
          prev.filter((p) => !selectedItems.some((item) => item.id === p.id))
        );
        
        // Clear selection
        setSelectedProducts({});
        
        toast({
          title: "Success",
          description: `${selectedItems.length} products deleted successfully.`,
        });
      },
    },
    {
      id: "export",
      label: "Export",
      icon: <Download className="h-4 w-4" />,
      action: async (selectedItems: Product[]) => {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        
        toast({
          title: "Success",
          description: `${selectedItems.length} products exported successfully.`,
        });
      },
    },
    {
      id: "update-stock",
      label: "Update Stock",
      icon: <Upload className="h-4 w-4" />,
      action: async (selectedItems: Product[]) => {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        
        toast({
          title: "Success",
          description: `Stock updated for ${selectedItems.length} products.`,
        });
      },
    },
  ];

  // Table columns
  const columns: Column<Product>[] = [
    {
      id: "name",
      header: "Product Name",
      accessorKey: "name",
      sortable: true,
    },
    {
      id: "sku",
      header: "SKU",
      accessorKey: "sku",
      sortable: true,
    },
    {
      id: "category",
      header: "Category",
      accessorKey: "category",
      sortable: true,
    },
    {
      id: "price",
      header: "Price",
      cell: (product) => `$${product.price.toFixed(2)}`,
      sortable: true,
    },
    {
      id: "stock",
      header: "Stock",
      accessorKey: "stock",
      sortable: true,
    },
    {
      id: "status",
      header: "Status",
      cell: (product) => (
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
            product.status === "in-stock"
              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
              : product.status === "low-stock"
              ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
              : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
          }`}
        >
          {product.status === "in-stock"
            ? "In Stock"
            : product.status === "low-stock"
            ? "Low Stock"
            : "Out of Stock"}
        </span>
      ),
      sortable: true,
    },
  ];

  // Help items
  const helpItems = [
    {
      id: "overview",
      title: "Products Overview",
      content: (
        <>
          <p>
            The Products page allows you to manage all your inventory items in one place.
            You can view, search, filter, and perform bulk actions on your products.
          </p>
          <p className="mt-2">
            Click on any product row to view and edit its details.
          </p>
        </>
      ),
    },
    {
      id: "bulk-actions",
      title: "Bulk Actions",
      content: (
        <>
          <p>
            To perform actions on multiple products at once:
          </p>
          <ol className="ml-5 list-decimal">
            <li>Select products using the checkboxes</li>
            <li>Use the bulk action buttons that appear</li>
            <li>Confirm the action if prompted</li>
          </ol>
        </>
      ),
    },
    {
      id: "filtering",
      title: "Filtering & Sorting",
      content: (
        <>
          <p>
            Use the search box to quickly find products by name, SKU, or category.
          </p>
          <p className="mt-2">
            Click on column headers to sort the table by that column.
            Click again to reverse the sort order.
          </p>
        </>
      ),
    },
  ];

  // Apply sorting and filtering
  let displayedProducts = [...products];
  
  // Apply filter
  if (filter) {
    const lowerFilter = filter.toLowerCase();
    displayedProducts = displayedProducts.filter(
      (product) =>
        product.name.toLowerCase().includes(lowerFilter) ||
        product.sku.toLowerCase().includes(lowerFilter) ||
        product.category.toLowerCase().includes(lowerFilter)
    );
  }
  
  // Apply sorting
  if (sortBy) {
    displayedProducts.sort((a, b) => {
      const aValue = a[sortBy as keyof Product];
      const bValue = b[sortBy as keyof Product];
      
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      }
      
      return 0;
    });
  }
  
  // Apply pagination
  const pageCount = Math.ceil(displayedProducts.length / pageSize);
  const paginatedProducts = displayedProducts.slice(
    pageIndex * pageSize,
    (pageIndex + 1) * pageSize
  );
  
  // Get selected products as array
  const selectedProductsArray = paginatedProducts.filter(
    (product) => selectedProducts[product.id]
  );

  return (
    <Container>
      <Section>
        <PageHeader
          title="Products"
          description="Manage your inventory products"
          actions={
            <>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
              <Button asChild>
                <a href="/products/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Product
                </a>
              </Button>
              <ContextualHelp items={helpItems} placement="icon" />
            </>
          }
        />

        {selectedProductsArray.length > 0 && (
          <div className="mb-4">
            <BulkActions
              actions={bulkActions}
              selectedItems={selectedProductsArray}
              getItemId={(product) => product.id}
              getItemLabel={(product) => product.name}
              onActionComplete={() => router.refresh()}
            />
          </div>
        )}

        <DataTable
          columns={columns}
          data={paginatedProducts}
          isLoading={isLoading}
          emptyState={{
            title: "No products found",
            description: "Add your first product to get started.",
            action: (
              <Button asChild>
                <a href="/products/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Product
                </a>
              </Button>\
            },
          }}
          pagination={{
            pageIndex,
            pageSize,
            pageCount,
            onPageChange: setPageIndex,
          }}
          sorting={{
            sortBy,
            sortDirection,
            onSortChange: handleSortChange,
          }}
          filtering={{
            filter,
            onFilterChange: handleFilterChange,
          }}
          selection={{
            selectedRows: selectedProducts,
            onSelectRow: handleSelectRow,
            onSelectAll: handleSelectAll,
            getRowId: (product) => product.id,
          }}
          onRowClick={handleRowClick}
        />
      </Section>
    </Container>
  );
}

