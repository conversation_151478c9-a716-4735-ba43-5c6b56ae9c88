"use client"

import { useEffect, useState } from "react"
import { useSocket } from "./socket-provider"
import { AlertCircle, CheckCircle2, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

export function ConnectionStatus({ className }: { className?: string }) {
  const { status, reconnect, isReconnecting, reconnectAttempts } = useSocket()
  const [showReconnectButton, setShowReconnectButton] = useState(false)

  // Show reconnect button after multiple failed attempts
  useEffect(() => {
    if (reconnectAttempts >= 3) {
      setShowReconnectButton(true)
    } else if (status === "connected") {
      setShowReconnectButton(false)
    }
  }, [reconnectAttempts, status])

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={cn("flex items-center gap-2", className)}>
            {status === "connected" ? (
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            ) : status === "connecting" || isReconnecting ? (
              <RefreshCw className="h-4 w-4 text-amber-500 animate-spin" />
            ) : (
              <AlertCircle className="h-4 w-4 text-destructive" />
            )}

            <span className="text-xs font-medium hidden md:inline-block">
              {status === "connected"
                ? "Connected"
                : isReconnecting
                  ? `Reconnecting (${reconnectAttempts})...`
                  : "Disconnected"}
            </span>

            {showReconnectButton && status !== "connected" && (
              <Button
                variant="outline"
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => {
                  reconnect()
                  setShowReconnectButton(false)
                }}
                disabled={isReconnecting}
              >
                Reconnect
              </Button>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          {status === "connected"
            ? "Connected to server"
            : isReconnecting
              ? `Reconnecting to server (Attempt ${reconnectAttempts})`
              : "Disconnected from server - Operating in offline mode"}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

