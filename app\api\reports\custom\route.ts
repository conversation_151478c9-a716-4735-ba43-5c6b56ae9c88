import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

// Schema for custom report creation/update
const customReportSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Report name is required"),
  description: z.string().optional(),
  entity: z.enum(["products", "orders", "customers", "inventory", "sales"]),
  fields: z.array(z.string()).min(1, "At least one field must be selected"),
  filters: z
    .array(
      z.object({
        field: z.string(),
        operator: z.string(),
        value: z.union([z.string(), z.number(), z.boolean(), z.null()]),
      }),
    )
    .optional(),
  groupBy: z.array(z.string()).optional(),
  sortBy: z
    .array(
      z.object({
        field: z.string(),
        direction: z.enum(["asc", "desc"]),
      }),
    )
    .optional(),
  isPublic: z.boolean().default(false),
})

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const validatedData = customReportSchema.parse(body)

    // Create or update custom report
    const report = await prisma.customReport.upsert({
      where: {
        id: validatedData.id || "",
      },
      update: {
        name: validatedData.name,
        description: validatedData.description || "",
        entity: validatedData.entity,
        fields: validatedData.fields,
        filters: validatedData.filters || [],
        groupBy: validatedData.groupBy || [],
        sortBy: validatedData.sortBy || [],
        isPublic: validatedData.isPublic,
        updatedAt: new Date(),
      },
      create: {
        name: validatedData.name,
        description: validatedData.description || "",
        entity: validatedData.entity,
        fields: validatedData.fields,
        filters: validatedData.filters || [],
        groupBy: validatedData.groupBy || [],
        sortBy: validatedData.sortBy || [],
        isPublic: validatedData.isPublic,
        userId: session.user.id,
      },
    })

    return NextResponse.json(report)
  } catch (error) {
    console.error("Error creating/updating custom report:", error)
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const entity = searchParams.get("entity")

    // Build query filters
    const filters: any = {
      OR: [{ userId: session.user.id }, { isPublic: true }],
    }

    if (entity) {
      filters.entity = entity
    }

    // Fetch custom reports
    const reports = await prisma.customReport.findMany({
      where: filters,
      orderBy: {
        updatedAt: "desc",
      },
    })

    return NextResponse.json(reports)
  } catch (error) {
    console.error("Error fetching custom reports:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

