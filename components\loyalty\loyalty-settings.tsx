"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"
import { Loader2, Plus, Trash2 } from "lucide-react"

const loyaltyTierSchema = z.object({
  name: z.string().min(1, "Tier name is required"),
  minimumPoints: z.coerce.number().min(0, "Minimum points must be at least 0"),
  pointsMultiplier: z.coerce.number().min(1, "Multiplier must be at least 1"),
  benefits: z.string().optional(),
})

const loyaltySettingsSchema = z.object({
  enabled: z.boolean(),
  pointsPerDollar: z.coerce.number().min(0.1, "Points per dollar must be at least 0.1"),
  minimumPointsToRedeem: z.coerce.number().min(1, "Minimum points must be at least 1"),
  pointsValueInCents: z.coerce.number().min(0.1, "Points value must be at least 0.1 cents"),
  expiryMonths: z.coerce.number().min(0, "Expiry months must be at least 0").optional(),
  tiers: z.array(loyaltyTierSchema),
})

type LoyaltySettingsFormValues = z.infer<typeof loyaltySettingsSchema>

export function LoyaltySettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)

  const form = useForm<LoyaltySettingsFormValues>({
    resolver: zodResolver(loyaltySettingsSchema),
    defaultValues: {
      enabled: false,
      pointsPerDollar: 1,
      minimumPointsToRedeem: 100,
      pointsValueInCents: 1,
      tiers: [],
    },
  })

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetchApi("/api/loyalty/settings")
        if (response) {
          form.reset({
            enabled: response.enabled,
            pointsPerDollar: response.pointsPerDollar,
            minimumPointsToRedeem: response.minimumPointsToRedeem,
            pointsValueInCents: response.pointsValueInCents,
            expiryMonths: response.expiryMonths || undefined,
            tiers: response.tiers || [],
          })
        }
      } catch (error) {
        console.error("Failed to fetch loyalty settings:", error)
        toast({
          title: "Error",
          description: "Failed to load loyalty program settings",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchSettings()
  }, [form, toast])

  const onSubmit = async (values: LoyaltySettingsFormValues) => {
    setIsLoading(true)
    try {
      await fetchApi("/api/loyalty/settings", {
        method: "PUT",
        body: JSON.stringify(values),
      })

      toast({
        title: "Success",
        description: "Loyalty program settings updated successfully",
      })
    } catch (error) {
      console.error("Failed to update loyalty settings:", error)
      toast({
        title: "Error",
        description: "Failed to update loyalty program settings",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const addTier = () => {
    const currentTiers = form.getValues("tiers") || []
    form.setValue("tiers", [
      ...currentTiers,
      {
        name: "",
        minimumPoints: 0,
        pointsMultiplier: 1,
        benefits: "",
      },
    ])
  }

  const removeTier = (index: number) => {
    const currentTiers = form.getValues("tiers") || []
    form.setValue(
      "tiers",
      currentTiers.filter((_, i) => i !== index),
    )
  }

  if (isLoading && !form.formState.isSubmitting) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Tabs defaultValue="general" className="space-y-4">
          <TabsList>
            <TabsTrigger value="general">General Settings</TabsTrigger>
            <TabsTrigger value="tiers">Loyalty Tiers</TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>Loyalty Program Settings</CardTitle>
                <CardDescription>Configure your customer loyalty program</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="enabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Enable Loyalty Program</FormLabel>
                        <FormDescription>Activate the loyalty program for your customers</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="pointsPerDollar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Points Per Dollar</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.1" {...field} />
                        </FormControl>
                        <FormDescription>How many points customers earn per dollar spent</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="minimumPointsToRedeem"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Points to Redeem</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormDescription>Minimum points required before redemption</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pointsValueInCents"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Points Value (cents)</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.1" {...field} />
                        </FormControl>
                        <FormDescription>Value of each point in cents when redeemed</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expiryMonths"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Points Expiry (months)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            value={field.value === undefined ? "" : field.value}
                            onChange={(e) => {
                              const value = e.target.value === "" ? undefined : Number.parseInt(e.target.value)
                              field.onChange(value)
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Number of months before points expire (leave empty for no expiry)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Settings"
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="tiers">
            <Card>
              <CardHeader>
                <CardTitle>Loyalty Tiers</CardTitle>
                <CardDescription>Configure loyalty tiers and benefits</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {form.getValues("tiers")?.map((_, index) => (
                  <div key={index} className="rounded-lg border p-4 space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium">Tier {index + 1}</h3>
                      <Button type="button" variant="ghost" size="sm" onClick={() => removeTier(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name={`tiers.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tier Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`tiers.${index}.minimumPoints`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Minimum Points</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`tiers.${index}.pointsMultiplier`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Points Multiplier</FormLabel>
                            <FormControl>
                              <Input type="number" step="0.1" {...field} />
                            </FormControl>
                            <FormDescription>Multiplier for points earned at this tier</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`tiers.${index}.benefits`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Benefits</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ""} />
                            </FormControl>
                            <FormDescription>Description of tier benefits</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                ))}

                <Button type="button" variant="outline" onClick={addTier} className="w-full">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Tier
                </Button>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Tiers"
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </form>
    </Form>
  )
}

