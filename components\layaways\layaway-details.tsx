"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { toast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Loader2, ArrowLeft, CheckCircle, XCircle, Clock, CreditCard } from "lucide-react"
import { formatDate, formatCurrency } from "@/lib/utils"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { layawayPaymentSchema, type LayawayPaymentFormValues } from "@/lib/validations/layaway-schema"

interface LayawayDetailsProps {
  layaway: any
}

export function LayawayDetails({ layaway }: LayawayDetailsProps) {
  const router = useRouter()
  const [isUpdating, setIsUpdating] = useState(false)
  const [status, setStatus] = useState(layaway.status)
  const [notes, setNotes] = useState("")
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false)
  const [isProcessingPayment, setIsProcessingPayment] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LayawayPaymentFormValues>({
    resolver: zodResolver(layawayPaymentSchema),
    defaultValues: {
      amount: 0,
      paymentMethod: "CASH",
      notes: "",
    },
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            Active
          </Badge>
        )
      case "COMPLETED":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        )
      case "CANCELLED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Cancelled
          </Badge>
        )
      case "EXPIRED":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
            <Clock className="mr-1 h-3 w-3" />
            Expired
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  const updateLayawayStatus = async () => {
    try {
      setIsUpdating(true)

      const response = await fetch(`/api/layaways/${layaway.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
          notes,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update layaway")
      }

      toast({
        title: "Layaway Updated",
        description: `Layaway #${layaway.layawayNumber} has been updated successfully.`,
      })

      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update layaway",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const addPayment = async (data: LayawayPaymentFormValues) => {
    try {
      setIsProcessingPayment(true)

      const response = await fetch(`/api/layaways/${layaway.id}/payments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to add payment")
      }

      toast({
        title: "Payment Added",
        description: `Payment of ${formatCurrency(data.amount)} has been added successfully.`,
      })

      setIsPaymentDialogOpen(false)
      reset()
      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add payment",
        variant: "destructive",
      })
    } finally {
      setIsProcessingPayment(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => router.back()} className="flex items-center" asChild>
          <Link href="/dashboard/layaways">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Layaways
          </Link>
        </Button>
        {getStatusBadge(layaway.status)}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex justify-between">
                <span>Layaway #{layaway.layawayNumber}</span>
              </CardTitle>
              <CardDescription>Created on {formatDate(layaway.createdAt)}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Customer</h3>
                  <p>
                    {layaway.customer.firstName} {layaway.customer.lastName}
                  </p>
                  {layaway.customer.email && <p>{layaway.customer.email}</p>}
                  {layaway.customer.phone && <p>{layaway.customer.phone}</p>}
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Layaway Details</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <span>{getStatusBadge(layaway.status)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Expiry Date:</span>
                      <span>{formatDate(layaway.expiryDate)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Amount:</span>
                      <span>{formatCurrency(layaway.totalAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Deposit Amount:</span>
                      <span>{formatCurrency(layaway.depositAmount)}</span>
                    </div>
                    <div className="flex justify-between font-medium">
                      <span className="text-muted-foreground">Balance Due:</span>
                      <span>{formatCurrency(layaway.balanceDue)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-medium mb-4">Layaway Items</h3>
                <div className="space-y-4">
                  {layaway.items.map((item: any) => (
                    <div key={item.id} className="flex justify-between items-center border-b pb-4">
                      <div>
                        <p className="font-medium">{item.product.name}</p>
                        {item.variant && <p className="text-sm text-muted-foreground">Variant: {item.variant.sku}</p>}
                        <p className="text-sm text-muted-foreground">
                          {formatCurrency(item.unitPrice)} x {item.quantity}
                        </p>
                      </div>
                      <p className="font-medium">{formatCurrency(item.total)}</p>
                    </div>
                  ))}
                </div>
              </div>

              {layaway.notes && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Notes</h3>
                  <p className="text-muted-foreground">{layaway.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {layaway.status === "ACTIVE" && (
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  className="w-full"
                  onClick={() => setIsPaymentDialogOpen(true)}
                  disabled={layaway.balanceDue <= 0}
                >
                  <CreditCard className="mr-2 h-4 w-4" /> Add Payment
                </Button>
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={() => {
                    setStatus("CANCELLED")
                    setNotes("Layaway cancelled by staff")
                    updateLayawayStatus()
                  }}
                >
                  <XCircle className="mr-2 h-4 w-4" /> Cancel Layaway
                </Button>
                {layaway.balanceDue <= 0 && (
                  <Button
                    className="w-full"
                    variant="default"
                    onClick={() => {
                      setStatus("COMPLETED")
                      setNotes("Layaway completed by staff")
                      updateLayawayStatus()
                    }}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" /> Complete Layaway
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>Record of payments for this layaway</CardDescription>
            </CardHeader>
            <CardContent>
              {layaway.payments.length === 0 ? (
                <p className="text-center text-muted-foreground py-4">No payments recorded yet</p>
              ) : (
                <div className="space-y-4">
                  {layaway.payments.map((payment: any) => (
                    <div key={payment.id} className="flex justify-between items-center border-b pb-4">
                      <div>
                        <p className="font-medium">{formatCurrency(payment.amount)}</p>
                        <p className="text-sm text-muted-foreground">{payment.paymentMethod.replace(/_/g, " ")}</p>
                        {payment.notes && <p className="text-sm text-muted-foreground">{payment.notes}</p>}
                      </div>
                      <p className="text-sm text-muted-foreground">{formatDate(payment.createdAt)}</p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Payment</DialogTitle>
            <DialogDescription>
              Add a payment to this layaway. The maximum payment amount is {formatCurrency(layaway.balanceDue)}.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit(addPayment)}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Payment Amount</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0.01"
                  max={layaway.balanceDue}
                  {...register("amount")}
                />
                {errors.amount && <p className="text-sm text-red-500">{errors.amount.message}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select
                  defaultValue="CASH"
                  onValueChange={(value) => register("paymentMethod").onChange({ target: { value } })}
                >
                  <SelectTrigger id="paymentMethod">
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CASH">Cash</SelectItem>
                    <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
                    <SelectItem value="DEBIT_CARD">Debit Card</SelectItem>
                    <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                    <SelectItem value="CHECK">Check</SelectItem>
                    <SelectItem value="PAYPAL">PayPal</SelectItem>
                    <SelectItem value="STORE_CREDIT">Store Credit</SelectItem>
                    <SelectItem value="GIFT_CARD">Gift Card</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
                {errors.paymentMethod && <p className="text-sm text-red-500">{errors.paymentMethod.message}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea id="notes" placeholder="Payment notes" {...register("notes")} />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsPaymentDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isProcessingPayment}>
                {isProcessingPayment && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Add Payment
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}

