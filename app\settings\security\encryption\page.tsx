import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { EncryptionKeyRotation } from "@/components/data-protection/encryption-key-rotation"

export default async function EncryptionSettingsPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user || !session.user.isAdmin) {
    redirect("/login")
  }

  // Get encryption keys
  const keys = await db.encryptionKeys.findMany({
    orderBy: { createdAt: "desc" },
  })

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Encryption Key Management</h1>

      <div className="grid grid-cols-1 gap-8">
        <EncryptionKeyRotation keys={keys} />
      </div>
    </div>
  )
}

