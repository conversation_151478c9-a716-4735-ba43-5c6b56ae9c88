import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const reportId = params.id

    // Fetch the report definition
    const report = await prisma.customReport.findUnique({
      where: { id: reportId },
    })

    if (!report) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 })
    }

    // Check if user has access to this report
    if (report.userId !== session.user.id && !report.isPublic) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Execute the report based on entity type
    let data: any[] = []

    switch (report.entity) {
      case "products":
        data = await executeProductReport(report)
        break
      case "orders":
        data = await executeOrderReport(report)
        break
      case "customers":
        data = await executeCustomerReport(report)
        break
      case "inventory":
        data = await executeInventoryReport(report)
        break
      case "sales":
        data = await executeSalesReport(report)
        break
      default:
        return NextResponse.json({ error: "Invalid report entity" }, { status: 400 })
    }

    // Apply grouping if specified
    if (report.groupBy && report.groupBy.length > 0) {
      data = applyGrouping(data, report.groupBy)
    }

    // Apply sorting if specified
    if (report.sortBy && report.sortBy.length > 0) {
      data = applySorting(data, report.sortBy)
    }

    // Generate summary metrics
    const summary = generateSummary(data, report.entity)

    return NextResponse.json({
      report,
      data,
      summary,
      executedAt: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Error executing custom report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// Helper functions for executing reports
async function executeProductReport(report: any) {
  // Build query based on report fields and filters
  const select: any = {}
  report.fields.forEach((field: string) => {
    select[field] = true
  })

  // Add related fields if needed
  if (select.category || select.categoryName) {
    select.category = { select: { name: true, id: true } }
  }
  if (select.supplier || select.supplierName) {
    select.supplier = { select: { name: true, id: true } }
  }

  // Build where clause from filters
  const where: any = buildWhereClause(report.filters)

  // Execute query
  const products = await prisma.product.findMany({
    select,
    where,
  })

  // Transform data if needed
  return products.map((product) => {
    const result: any = { ...product }

    // Handle related fields
    if (product.category) {
      result.categoryName = product.category.name
      if (!select.category) delete result.category
    }

    if (product.supplier) {
      result.supplierName = product.supplier.name
      if (!select.supplier) delete result.supplier
    }

    return result
  })
}

async function executeOrderReport(report: any) {
  // Similar implementation for orders
  const select: any = {}
  report.fields.forEach((field: string) => {
    select[field] = true
  })

  // Add related fields if needed
  if (select.customer || select.customerName) {
    select.customer = { select: { name: true, id: true, email: true } }
  }
  if (select.items || select.itemCount || select.products) {
    select.items = {
      select: {
        id: true,
        quantity: true,
        price: true,
        product: { select: { name: true, id: true, sku: true } },
      },
    }
  }

  // Build where clause from filters
  const where: any = buildWhereClause(report.filters)

  // Execute query
  const orders = await prisma.order.findMany({
    select,
    where,
  })

  // Transform data if needed
  return orders.map((order) => {
    const result: any = { ...order }

    // Handle related fields
    if (order.customer) {
      result.customerName = order.customer.name
      result.customerEmail = order.customer.email
      if (!select.customer) delete result.customer
    }

    if (order.items) {
      result.itemCount = order.items.length
      result.products = order.items.map((item) => item.product.name).join(", ")
      if (!select.items) delete result.items
    }

    return result
  })
}

async function executeCustomerReport(report: any) {
  // Implementation for customers report
  const select: any = {}
  report.fields.forEach((field: string) => {
    select[field] = true
  })

  // Add related fields if needed
  if (select.orders || select.orderCount || select.totalSpent) {
    select.orders = {
      select: {
        id: true,
        total: true,
        createdAt: true,
      },
    }
  }

  // Build where clause from filters
  const where: any = buildWhereClause(report.filters)

  // Execute query
  const customers = await prisma.customer.findMany({
    select,
    where,
  })

  // Transform data if needed
  return customers.map((customer) => {
    const result: any = { ...customer }

    // Handle related fields
    if (customer.orders) {
      result.orderCount = customer.orders.length
      result.totalSpent = customer.orders.reduce((sum, order) => sum + Number(order.total), 0)
      result.lastOrderDate =
        customer.orders.length > 0
          ? new Date(Math.max(...customer.orders.map((o) => o.createdAt.getTime()))).toISOString()
          : null
      if (!select.orders) delete result.orders
    }

    return result
  })
}

async function executeInventoryReport(report: any) {
  // Implementation for inventory report
  const select: any = {
    id: true,
    sku: true,
    name: true,
    quantity: true,
    price: true,
    cost: true,
    category: { select: { name: true } },
  }

  // Ensure all requested fields are included
  report.fields.forEach((field: string) => {
    if (field.includes(".")) {
      const [relation, relationField] = field.split(".")
      if (!select[relation]) select[relation] = { select: {} }
      select[relation].select[relationField] = true
    } else {
      select[field] = true
    }
  })

  // Build where clause from filters
  const where: any = buildWhereClause(report.filters)

  // Execute query
  const products = await prisma.product.findMany({
    select,
    where,
  })

  // Transform data
  return products.map((product) => {
    const result: any = { ...product }

    // Calculate inventory value
    result.inventoryValue = Number(product.quantity) * Number(product.cost || product.price)

    // Handle related fields
    if (product.category) {
      result.categoryName = product.category.name
      if (!report.fields.includes("category")) delete result.category
    }

    return result
  })
}

async function executeSalesReport(report: any) {
  // Implementation for sales report
  const where: any = buildWhereClause(report.filters)

  // Always filter for completed orders for sales reports
  where.status = "completed"

  const orders = await prisma.order.findMany({
    select: {
      id: true,
      orderNumber: true,
      total: true,
      subtotal: true,
      tax: true,
      discount: true,
      createdAt: true,
      customer: { select: { name: true, id: true } },
      items: {
        select: {
          quantity: true,
          price: true,
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              cost: true,
              category: { select: { name: true } },
            },
          },
        },
      },
    },
    where,
  })

  // Transform to sales data
  const salesData: any[] = []

  orders.forEach((order) => {
    order.items.forEach((item) => {
      const cost = Number(item.product.cost || 0)
      const price = Number(item.price)
      const quantity = Number(item.quantity)
      const revenue = price * quantity
      const profit = revenue - cost * quantity

      salesData.push({
        orderId: order.id,
        orderNumber: order.orderNumber,
        date: order.createdAt,
        customerId: order.customer?.id,
        customerName: order.customer?.name,
        productId: item.product.id,
        productName: item.product.name,
        productSku: item.product.sku,
        categoryName: item.product.category?.name,
        quantity: quantity,
        unitPrice: price,
        unitCost: cost,
        revenue: revenue,
        profit: profit,
        profitMargin: cost > 0 ? (profit / revenue) * 100 : null,
      })
    })
  })

  // Filter fields based on report.fields
  return salesData.map((sale) => {
    const result: any = {}
    report.fields.forEach((field: string) => {
      result[field] = sale[field]
    })
    return result
  })
}

// Helper function to build where clause from filters
function buildWhereClause(filters: any[] = []) {
  if (!filters || filters.length === 0) return {}

  const where: any = {}

  filters.forEach((filter) => {
    const { field, operator, value } = filter

    // Handle different operators
    switch (operator) {
      case "equals":
        where[field] = value
        break
      case "contains":
        where[field] = { contains: value, mode: "insensitive" }
        break
      case "startsWith":
        where[field] = { startsWith: value, mode: "insensitive" }
        break
      case "endsWith":
        where[field] = { endsWith: value, mode: "insensitive" }
        break
      case "gt":
        where[field] = { gt: value }
        break
      case "gte":
        where[field] = { gte: value }
        break
      case "lt":
        where[field] = { lt: value }
        break
      case "lte":
        where[field] = { lte: value }
        break
      case "in":
        where[field] = { in: Array.isArray(value) ? value : [value] }
        break
      case "notIn":
        where[field] = { notIn: Array.isArray(value) ? value : [value] }
        break
      case "isNull":
        where[field] = null
        break
      case "isNotNull":
        where[field] = { not: null }
        break
      default:
        // Ignore invalid operators
        break
    }
  })

  return where
}

// Helper function to apply grouping
function applyGrouping(data: any[], groupBy: string[]) {
  if (!groupBy || groupBy.length === 0) return data

  const groupedData: any = {}

  data.forEach((item) => {
    // Create a key based on groupBy fields
    const key = groupBy.map((field) => item[field]).join("|")

    if (!groupedData[key]) {
      // Initialize group with groupBy fields
      groupedData[key] = {}
      groupBy.forEach((field) => {
        groupedData[key][field] = item[field]
      })

      // Initialize aggregates
      groupedData[key].count = 0
      groupedData[key].sum = {}
      groupedData[key].avg = {}
      groupedData[key].min = {}
      groupedData[key].max = {}
    }

    // Update aggregates
    groupedData[key].count++

    // Calculate aggregates for numeric fields
    Object.keys(item).forEach((field) => {
      if (typeof item[field] === "number") {
        // Sum
        if (groupedData[key].sum[field] === undefined) {
          groupedData[key].sum[field] = 0
        }
        groupedData[key].sum[field] += item[field]

        // Min/Max
        if (groupedData[key].min[field] === undefined || item[field] < groupedData[key].min[field]) {
          groupedData[key].min[field] = item[field]
        }
        if (groupedData[key].max[field] === undefined || item[field] > groupedData[key].max[field]) {
          groupedData[key].max[field] = item[field]
        }
      }
    })
  })

  // Calculate averages
  Object.keys(groupedData).forEach((key) => {
    Object.keys(groupedData[key].sum).forEach((field) => {
      groupedData[key].avg[field] = groupedData[key].sum[field] / groupedData[key].count
    })
  })

  // Convert back to array
  return Object.values(groupedData)
}

// Helper function to apply sorting
function applySorting(data: any[], sortBy: { field: string; direction: "asc" | "desc" }[]) {
  if (!sortBy || sortBy.length === 0) return data

  return [...data].sort((a, b) => {
    for (const sort of sortBy) {
      const { field, direction } = sort

      if (a[field] === undefined || b[field] === undefined) continue

      // Handle different data types
      if (typeof a[field] === "string" && typeof b[field] === "string") {
        const comparison = a[field].localeCompare(b[field])
        if (comparison !== 0) {
          return direction === "asc" ? comparison : -comparison
        }
      } else {
        const comparison = a[field] < b[field] ? -1 : a[field] > b[field] ? 1 : 0
        if (comparison !== 0) {
          return direction === "asc" ? comparison : -comparison
        }
      }
    }

    return 0
  })
}

// Helper function to generate summary metrics
function generateSummary(data: any[], entity: string) {
  if (!data || data.length === 0) {
    return { count: 0 }
  }

  const summary: any = {
    count: data.length,
  }

  // Calculate sums for numeric fields
  const numericFields = Object.keys(data[0]).filter((key) => typeof data[0][key] === "number" && !isNaN(data[0][key]))

  numericFields.forEach((field) => {
    summary[`total_${field}`] = data.reduce((sum, item) => sum + (item[field] || 0), 0)
    summary[`avg_${field}`] = summary[`total_${field}`] / data.length
    summary[`min_${field}`] = Math.min(...data.map((item) => item[field] || Number.POSITIVE_INFINITY))
    summary[`max_${field}`] = Math.max(...data.map((item) => item[field] || Number.NEGATIVE_INFINITY))
  })

  // Entity-specific summaries
  switch (entity) {
    case "products":
      summary.totalProducts = data.length
      if (summary.total_quantity !== undefined) {
        summary.totalInventoryValue = data.reduce(
          (sum, item) => sum + (item.quantity || 0) * (item.cost || item.price || 0),
          0,
        )
      }
      break

    case "orders":
      summary.totalOrders = data.length
      break

    case "sales":
      summary.totalSales = data.length
      break

    case "customers":
      summary.totalCustomers = data.length
      break
  }

  return summary
}

