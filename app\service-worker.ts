/// <reference lib="webworker" />

// This service worker can be customized!
// See https://developers.google.com/web/tools/workbox/modules
// for the list of available Workbox modules, or add any other
// code you'd like.

declare const self: ServiceWorkerGlobalScope

const CACHE_NAME = "stocksync-cache-v1"
const OFFLINE_URL = "/offline"

// Add all the routes you want to cache for offline access
const ROUTES_TO_CACHE = ["/", "/dashboard", "/offline", "/manifest.json"]

// Add all the static assets you want to cache
const ASSETS_TO_CACHE = ["/logo.png", "/favicon.ico"]

// Install event - cache the offline page and static assets
self.addEventListener("install", (event) => {
  event.waitUntil(
    (async () => {
      const cache = await caches.open(CACHE_NAME)

      // Cache the offline page
      await cache.add(new Request(OFFLINE_URL, { cache: "reload" }))

      // Cache routes
      await Promise.all(ROUTES_TO_CACHE.map((route) => cache.add(new Request(route, { cache: "reload" }))))

      // Cache assets
      await Promise.all(ASSETS_TO_CACHE.map((asset) => cache.add(new Request(asset, { cache: "reload" }))))
    })(),
  )

  // Force the waiting service worker to become the active service worker
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  event.waitUntil(
    (async () => {
      // Enable navigation preload if it's supported
      if ("navigationPreload" in self.registration) {
        await self.registration.navigationPreload.enable()
      }

      // Clean up old caches
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.filter((name) => name !== CACHE_NAME).map((name) => caches.delete(name)))
    })(),
  )

  // Tell the active service worker to take control of the page immediately
  self.clients.claim()
})

// Fetch event - serve from cache if available, otherwise fetch from network
self.addEventListener("fetch", (event) => {
  // Skip cross-origin requests
  if (event.request.mode === "navigate") {
    event.respondWith(
      (async () => {
        try {
          // First, try to use the navigation preload response if it's supported
          const preloadResponse = await event.preloadResponse
          if (preloadResponse) {
            return preloadResponse
          }

          // Always try the network first for navigation requests
          return await fetch(event.request)
        } catch (error) {
          // If network fails, serve from cache
          const cache = await caches.open(CACHE_NAME)
          const cachedResponse = await cache.match(event.request)

          if (cachedResponse) {
            return cachedResponse
          }

          // If the requested page is not in the cache, show the offline page
          return await cache.match(OFFLINE_URL)
        }
      })(),
    )
  } else if (
    event.request.destination === "image" ||
    event.request.destination === "style" ||
    event.request.destination === "script" ||
    event.request.url.includes("/api/")
  ) {
    // Use cache-first strategy for assets and API requests
    event.respondWith(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((cachedResponse) => {
          // Return cached response if available
          if (cachedResponse) {
            // Fetch from network in the background to update cache
            fetch(event.request)
              .then((response) => {
                cache.put(event.request, response.clone())
              })
              .catch(() => {
                // Network request failed, but we already have a cached response
              })
            return cachedResponse
          }

          // If not in cache, fetch from network
          return fetch(event.request)
            .then((response) => {
              // Cache the fetched response
              cache.put(event.request, response.clone())
              return response
            })
            .catch(() => {
              // For API requests that fail and aren't cached, return a JSON error
              if (event.request.url.includes("/api/")) {
                return new Response(
                  JSON.stringify({
                    error: "You are offline and this data is not cached.",
                  }),
                  {
                    headers: { "Content-Type": "application/json" },
                  },
                )
              }

              // For image requests, return a placeholder
              if (event.request.destination === "image") {
                return new Response(
                  '<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="#eee"/><text x="50%" y="50%" font-family="sans-serif" font-size="12" text-anchor="middle" dominant-baseline="middle" fill="#999">Offline</text></svg>',
                  { headers: { "Content-Type": "image/svg+xml" } },
                )
              }

              // For other assets, return an empty response
              return new Response("", { status: 408, statusText: "Offline" })
            })
        })
      }),
    )
  }
})

// Background sync for offline actions
self.addEventListener("sync", (event) => {
  if (event.tag === "sync-pending-actions") {
    event.waitUntil(syncPendingActions())
  }
})

async function syncPendingActions() {
  try {
    // Get all clients
    const clients = await self.clients.matchAll()

    // Send message to client to process pending actions
    clients.forEach((client) => {
      client.postMessage({
        type: "SYNC_PENDING_ACTIONS",
      })
    })
  } catch (error) {
    console.error("Error syncing pending actions:", error)
  }
}

// Push notification event
self.addEventListener("push", (event) => {
  if (!event.data) return

  const data = event.data.json()

  const options = {
    body: data.body,
    icon: "/logo.png",
    badge: "/badge.png",
    data: {
      url: data.url || "/",
    },
  }

  event.waitUntil(self.registration.showNotification(data.title, options))
})

// Notification click event
self.addEventListener("notificationclick", (event) => {
  event.notification.close()

  event.waitUntil(
    clients.matchAll({ type: "window" }).then((windowClients) => {
      // Check if there is already a window/tab open with the target URL
      for (const client of windowClients) {
        if (client.url === event.notification.data.url && "focus" in client) {
          return client.focus()
        }
      }

      // If no window/tab is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(event.notification.data.url)
      }
    }),
  )
})

