"use client"

import { useState, useEffect } from "react"
import type { DateRange } from "react-day-picker"
import { format } from "date-fns"
import { useToast } from "@/hooks/use-toast"

interface ReportFilters {
  category: string
  store: string
  product: string
  groupBy: string
}

export function useReportData(
  reportType: "sales" | "inventory" | "customers",
  dateRange?: DateRange,
  filters?: ReportFilters,
) {
  const [data, setData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    const fetchReportData = async () => {
      if (!dateRange?.from || !dateRange?.to) return

      setIsLoading(true)
      setError(null)

      try {
        // Build query parameters
        const params = new URLSearchParams({
          from: format(dateRange.from, "yyyy-MM-dd"),
          to: format(dateRange.to, "yyyy-MM-dd"),
          ...(filters?.category !== "all" && { category: filters.category }),
          ...(filters?.store !== "all" && { store: filters.store }),
          ...(filters?.product !== "all" && { product: filters.product }),
          ...(filters?.groupBy && { groupBy: filters.groupBy }),
        })

        // Fetch data from API
        const response = await fetch(`/api/reports/${reportType}?${params.toString()}`)

        if (!response.ok) {
          throw new Error(`Failed to fetch ${reportType} report data`)
        }

        const result = await response.json()
        setData(result)
      } catch (err) {
        console.error(`Error fetching ${reportType} report:`, err)
        setError(err instanceof Error ? err : new Error(String(err)))

        toast({
          title: "Error fetching report data",
          description: err instanceof Error ? err.message : "An unexpected error occurred",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchReportData()
  }, [reportType, dateRange, filters, toast])

  return { data, isLoading, error }
}

