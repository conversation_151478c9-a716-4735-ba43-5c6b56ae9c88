import type { StateCreator } from "zustand"
import type { StoreState } from "./index"
import { produce } from "immer"

export interface Settings {
  company: {
    name: string
    address: string
    phone: string
    email: string
    website: string
    logo?: string | null
  }
  tax: {
    rate: number
    includeInPrice: boolean
    taxNumber: string
  }
  receipt: {
    showLogo: boolean
    footer: string
    includeItemSku: boolean
    includeTaxDetails: boolean
  }
  system: {
    currency: string
    dateFormat: string
    timeFormat: string
    lowStockThreshold: number
    enableNotifications: boolean
    autoBackup: boolean
    theme: "light" | "dark" | "system"
    language: "en" | "es" | "fr" | "de" | "zh"
  }
}

export interface SettingsSlice {
  settings: Settings
  updateSettings: (settings: Partial<Settings>) => void
}

export const createSettingsSlice: StateCreator<StoreState, [], [], SettingsSlice> = (set) => ({
  settings: {
    company: {
      name: "StockSync Inc.",
      address: "123 Business St, Suite 100, Business City, BC 12345",
      phone: "555-ST<PERSON>KSYNC",
      email: "<EMAIL>",
      website: "https://stocksync.com",
      logo: null,
    },
    tax: {
      rate: 8,
      includeInPrice: false,
      taxNumber: "TAX-12345-678",
    },
    receipt: {
      showLogo: true,
      footer: "Thank you for your business!",
      includeItemSku: true,
      includeTaxDetails: true,
    },
    system: {
      currency: "USD",
      dateFormat: "MM/DD/YYYY",
      timeFormat: "12h",
      lowStockThreshold: 5,
      enableNotifications: true,
      autoBackup: true,
      theme: "system",
      language: "en",
    },
  },
  updateSettings: (newSettings) =>
    set(
      produce((state: SettingsSlice) => {
        // Handle nested updates with proper immutability
        if (newSettings.company) {
          state.settings.company = {
            ...state.settings.company,
            ...newSettings.company,
          }
        }

        if (newSettings.tax) {
          state.settings.tax = {
            ...state.settings.tax,
            ...newSettings.tax,
          }
        }

        if (newSettings.receipt) {
          state.settings.receipt = {
            ...state.settings.receipt,
            ...newSettings.receipt,
          }
        }

        if (newSettings.system) {
          state.settings.system = {
            ...state.settings.system,
            ...newSettings.system,
          }
        }
      }),
    ),
})

