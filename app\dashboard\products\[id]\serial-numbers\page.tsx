import { Suspense } from "react"
import { notFound } from "next/navigation"
import { prisma } from "@/lib/prisma"
import { SerialNumberManager } from "@/components/inventory/serial-number-manager"
import { Skeleton } from "@/components/ui/skeleton"

interface SerialNumbersPageProps {
  params: {
    id: string
  }
  searchParams: {
    variantId?: string
  }
}

async function getProduct(id: string) {
  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      variants: true,
    },
  })

  if (!product) {
    notFound()
  }

  return product
}

export default async function SerialNumbersPage({ params, searchParams }: SerialNumbersPageProps) {
  const product = await getProduct(params.id)
  const variantId = searchParams.variantId

  let variantName: string | undefined

  if (variantId) {
    const variant = product.variants.find((v) => v.id === variantId)
    if (variant) {
      variantName = variant.sku
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Serial Numbers</h2>
        <p className="text-muted-foreground">
          Manage serial numbers for {product.name}
          {variantName && ` - ${variantName}`}
        </p>
      </div>

      <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
        <SerialNumberManager
          productId={product.id}
          variantId={variantId}
          productName={product.name}
          variantName={variantName}
        />
      </Suspense>
    </div>
  )
}

