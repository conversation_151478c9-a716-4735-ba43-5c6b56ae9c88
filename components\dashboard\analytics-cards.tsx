"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useSocket } from "@/components/socket-provider"
import { DollarSign, Package, ShoppingCart, Users } from "lucide-react"

interface AnalyticsData {
  totalSales: number
  totalOrders: number
  totalProducts: number
  totalCustomers: number
  lowStockItems: number
  averageOrderValue: number
}

export function AnalyticsCards() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { emit, socket, status } = useSocket()

  useEffect(() => {
    if (status === "connected") {
      setIsLoading(true)

      const syncId = `analytics_sync_${Date.now()}`

      const handleSyncResponse = (response: any) => {
        if (response.id === syncId) {
          setData(response.data)
          setIsLoading(false)

          // Remove the event listener
          socket?.off("sync_response", handleSyncResponse)
        }
      }

      // Listen for the response
      socket?.on("sync_response", handleSyncResponse)

      // Request analytics data
      emit("sync_request", {
        id: syncId,
        timestamp: Date.now(),
        type: "analytics",
      })

      // Fallback in case we don't get a response
      const timeout = setTimeout(() => {
        if (isLoading) {
          // Use mock data if we don't get a response
          setData({
            totalSales: 12589.99,
            totalOrders: 156,
            totalProducts: 48,
            totalCustomers: 89,
            lowStockItems: 5,
            averageOrderValue: 80.71,
          })
          setIsLoading(false)
          socket?.off("sync_response", handleSyncResponse)
        }
      }, 3000)

      return () => {
        clearTimeout(timeout)
        socket?.off("sync_response", handleSyncResponse)
      }
    }
  }, [status, emit, socket, isLoading])

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-[100px]" />
          ) : (
            <>
              <div className="text-2xl font-bold">${data?.totalSales.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground">+20.1% from last month</p>
            </>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Orders</CardTitle>
          <ShoppingCart className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-[100px]" />
          ) : (
            <>
              <div className="text-2xl font-bold">{data?.totalOrders}</div>
              <p className="text-xs text-muted-foreground">+12.2% from last month</p>
            </>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Products</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-[100px]" />
          ) : (
            <>
              <div className="text-2xl font-bold">{data?.totalProducts}</div>
              <p className="text-xs text-muted-foreground">{data?.lowStockItems} items low in stock</p>
            </>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Customers</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-[100px]" />
          ) : (
            <>
              <div className="text-2xl font-bold">{data?.totalCustomers}</div>
              <p className="text-xs text-muted-foreground">+4.6% from last month</p>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

