"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Camera, QrCode, AlertCircle, Check, X } from "lucide-react"
import { NativeCamera } from "@/lib/native/native-features"
import { useToast } from "@/hooks/use-toast"

interface ARInventoryScannerProps {
  onScan?: (barcode: string) => void
}

export default function ARInventoryScanner({ onScan }: ARInventoryScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isSupported, setIsSupported] = useState<boolean | null>(null)
  const [isScanning, setIsScanning] = useState(false)
  const [manualBarcode, setManualBarcode] = useState("")
  const [lastScanned, setLastScanned] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Check if the device supports the camera
  useEffect(() => {
    const checkCameraSupport = async () => {
      try {
        if (typeof navigator === "undefined" || !navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          setIsSupported(false)
          return
        }

        // Try to access the camera
        await navigator.mediaDevices.getUserMedia({ video: true })
        setIsSupported(true)
      } catch (error) {
        console.error("Error checking camera support:", error)
        setIsSupported(false)
      }
    }

    checkCameraSupport()
  }, [])

  // Handle barcode scanning
  const handleScan = async () => {
    if (isScanning) {
      stopScanning()
      return
    }

    try {
      setIsScanning(true)
      setError(null)

      // Try to use the native barcode scanner if available
      if (NativeCamera && typeof NativeCamera.scanBarcode === "function") {
        const barcode = await NativeCamera.scanBarcode()

        if (barcode) {
          handleBarcodeDetected(barcode)
        } else {
          setError("No barcode detected")
        }

        setIsScanning(false)
        return
      }

      // Fallback to web camera
      if (!videoRef.current) {
        throw new Error("Video element not found")
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "environment" },
      })

      videoRef.current.srcObject = stream
      videoRef.current.play()

      // Start processing frames for barcode detection
      requestAnimationFrame(processVideoFrame)
    } catch (error) {
      console.error("Error starting barcode scanner:", error)
      setError(error instanceof Error ? error.message : "Failed to start camera")
      setIsScanning(false)
    }
  }

  // Process video frames to detect barcodes
  const processVideoFrame = () => {
    if (!isScanning || !videoRef.current || !canvasRef.current) return

    const video = videoRef.current
    const canvas = canvasRef.current
    const context = canvas.getContext("2d")

    if (!context || video.readyState !== video.HAVE_ENOUGH_DATA) {
      requestAnimationFrame(processVideoFrame)
      return
    }

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    // Draw the current video frame to the canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height)

    // Here you would integrate a barcode detection library
    // For example, using ZXing or a similar library
    // This is a placeholder for the actual barcode detection logic

    // For demonstration, we'll simulate finding a barcode after 3 seconds
    setTimeout(() => {
      if (isScanning) {
        const simulatedBarcode = `PROD${Math.floor(Math.random() * 10000)
          .toString()
          .padStart(4, "0")}`
        handleBarcodeDetected(simulatedBarcode)
      }
    }, 3000)

    requestAnimationFrame(processVideoFrame)
  }

  // Handle a detected barcode
  const handleBarcodeDetected = (barcode: string) => {
    setLastScanned(barcode)
    stopScanning()

    toast({
      title: "Barcode Detected",
      description: `Scanned barcode: ${barcode}`,
      variant: "default",
    })

    if (onScan) {
      onScan(barcode)
    }
  }

  // Stop the scanning process
  const stopScanning = () => {
    setIsScanning(false)

    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      const tracks = stream.getTracks()

      tracks.forEach((track) => track.stop())
      videoRef.current.srcObject = null
    }
  }

  // Handle manual barcode submission
  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!manualBarcode.trim()) {
      setError("Please enter a barcode")
      return
    }

    handleBarcodeDetected(manualBarcode)
    setManualBarcode("")
  }

  if (isSupported === null) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <QrCode className="h-5 w-5" />
          <span>Inventory Scanner</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="rounded-md bg-destructive/10 p-3 text-sm text-destructive">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          </div>
        )}

        <div className="relative aspect-video w-full overflow-hidden rounded-md bg-muted">
          {isScanning ? (
            <>
              <video ref={videoRef} className="absolute inset-0 h-full w-full object-cover" playsInline muted />
              <canvas ref={canvasRef} className="absolute inset-0 h-full w-full object-cover" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="h-48 w-48 border-2 border-primary/50 bg-transparent"></div>
              </div>
            </>
          ) : (
            <div className="flex h-full flex-col items-center justify-center">
              <Camera className="mb-2 h-12 w-12 text-muted-foreground" />
              <p className="text-center text-sm text-muted-foreground">
                {isSupported
                  ? "Press the scan button to start scanning barcodes"
                  : "Camera access is not available on this device"}
              </p>
            </div>
          )}
        </div>

        <div className="flex justify-center">
          <Button
            onClick={handleScan}
            disabled={!isSupported}
            variant={isScanning ? "destructive" : "default"}
            className="w-full"
          >
            {isScanning ? (
              <>
                <X className="mr-2 h-4 w-4" />
                Stop Scanning
              </>
            ) : (
              <>
                <Camera className="mr-2 h-4 w-4" />
                Start Scanning
              </>
            )}
          </Button>
        </div>

        {lastScanned && (
          <div className="rounded-md bg-primary/10 p-3 text-sm">
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-primary" />
              <span>
                Last scanned: <strong>{lastScanned}</strong>
              </span>
            </div>
          </div>
        )}

        <Separator />

        <form onSubmit={handleManualSubmit} className="space-y-3">
          <Label htmlFor="manual-barcode">Manual Entry</Label>
          <div className="flex gap-2">
            <Input
              id="manual-barcode"
              placeholder="Enter barcode manually"
              value={manualBarcode}
              onChange={(e) => setManualBarcode(e.target.value)}
            />
            <Button type="submit">Submit</Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

