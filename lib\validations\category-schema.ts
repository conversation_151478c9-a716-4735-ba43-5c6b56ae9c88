import { z } from "zod"

export const categorySchema = z.object({
  name: z.string().min(1, "Category name is required").max(50, "Category name cannot exceed 50 characters"),
  description: z.string().optional(),
  color: z
    .string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Invalid color format")
    .optional(),
  parentId: z.string().optional(),
})

export type CategoryFormValues = z.infer<typeof categorySchema>

