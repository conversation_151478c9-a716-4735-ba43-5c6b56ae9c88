"use client"

import { useStore } from "@/lib/store"
import { useEffect, useState } from "react"

// Only render in development
export function StoreMonitor() {
  const [isVisible, setIsVisible] = useState(false)
  const [storeSnapshot, setStoreSnapshot] = useState<any>(null)

  // Get the entire store state
  const store = useStore()

  useEffect(() => {
    // Only enable in development
    if (process.env.NODE_ENV !== "development") return

    const handleKeyPress = (e: KeyboardEvent) => {
      // Toggle visibility with Ctrl+Shift+S
      if (e.ctrlKey && e.shiftKey && e.key === "S") {
        setIsVisible((prev) => !prev)
        e.preventDefault()
      }
    }

    window.addEventListener("keydown", handleKeyPress)
    return () => window.removeEventListener("keydown", handleKeyPress)
  }, [])

  useEffect(() => {
    if (isVisible) {
      // Create a sanitized snapshot of the store
      const snapshot = { ...store }
      // Remove functions
      Object.keys(snapshot).forEach((key) => {
        if (typeof snapshot[key] === "function") {
          snapshot[key] = "[Function]"
        }
      })
      setStoreSnapshot(snapshot)
    }
  }, [isVisible, store])

  if (!isVisible || process.env.NODE_ENV !== "development") return null

  return (
    <div className="fixed bottom-0 right-0 w-96 h-96 bg-background border border-border rounded-tl-lg shadow-lg z-50 overflow-auto p-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-semibold">Store Monitor</h3>
        <button onClick={() => setIsVisible(false)} className="text-muted-foreground hover:text-foreground">
          Close
        </button>
      </div>
      <pre className="text-xs overflow-auto">{JSON.stringify(storeSnapshot, null, 2)}</pre>
    </div>
  )
}

