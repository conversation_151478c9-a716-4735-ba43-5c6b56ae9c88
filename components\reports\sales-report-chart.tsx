"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
} from "@/components/ui/chart"

interface SalesReportChartProps {
  data: any
  isLoading: boolean
  groupBy: string
}

export function SalesReportChart({ data, isLoading, groupBy }: SalesReportChartProps) {
  const [chartType, setChartType] = useState<"line" | "bar" | "area" | "pie">("line")

  if (isLoading) {
    return <Skeleton className="w-full h-full min-h-[300px]" />
  }

  if (!data?.chartData || data.chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-full min-h-[300px] text-muted-foreground">
        No data available for the selected period
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Tabs value={chartType} onValueChange={(value) => setChartType(value as any)}>
        <TabsList className="grid grid-cols-4 w-[400px]">
          <TabsTrigger value="line">Line</TabsTrigger>
          <TabsTrigger value="bar">Bar</TabsTrigger>
          <TabsTrigger value="area">Area</TabsTrigger>
          <TabsTrigger value="pie">Pie</TabsTrigger>
        </TabsList>

        <TabsContent value="line" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data.chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={groupBy === "day" ? "date" : groupBy === "week" ? "week" : "month"}
                tickFormatter={(value) => {
                  if (groupBy === "day") return value
                  if (groupBy === "week") return `Week ${value}`
                  return value
                }}
              />
              <YAxis />
              <Tooltip formatter={(value) => `$${value}`} />
              <Legend />
              <Line type="monotone" dataKey="revenue" stroke="#3b82f6" name="Revenue" activeDot={{ r: 8 }} />
              <Line type="monotone" dataKey="profit" stroke="#10b981" name="Profit" />
            </LineChart>
          </ResponsiveContainer>
        </TabsContent>

        <TabsContent value="bar" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={groupBy === "day" ? "date" : groupBy === "week" ? "week" : "month"}
                tickFormatter={(value) => {
                  if (groupBy === "day") return value
                  if (groupBy === "week") return `Week ${value}`
                  return value
                }}
              />
              <YAxis />
              <Tooltip formatter={(value) => `$${value}`} />
              <Legend />
              <Bar dataKey="revenue" fill="#3b82f6" name="Revenue" />
              <Bar dataKey="profit" fill="#10b981" name="Profit" />
            </BarChart>
          </ResponsiveContainer>
        </TabsContent>

        <TabsContent value="area" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data.chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={groupBy === "day" ? "date" : groupBy === "week" ? "week" : "month"}
                tickFormatter={(value) => {
                  if (groupBy === "day") return value
                  if (groupBy === "week") return `Week ${value}`
                  return value
                }}
              />
              <YAxis />
              <Tooltip formatter={(value) => `$${value}`} />
              <Legend />
              <Area
                type="monotone"
                dataKey="revenue"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.3}
                name="Revenue"
              />
              <Area type="monotone" dataKey="profit" stroke="#10b981" fill="#10b981" fillOpacity={0.3} name="Profit" />
            </AreaChart>
          </ResponsiveContainer>
        </TabsContent>

        <TabsContent value="pie" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={
                  data.pieData || [
                    { name: "Revenue", value: data.metrics.totalRevenue },
                    { name: "Profit", value: data.metrics.totalProfit },
                    { name: "Tax", value: data.metrics.totalTax },
                  ]
                }
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {data.pieData?.map((entry: any, index: number) => (
                  <Cell key={`cell-${index}`} fill={index === 0 ? "#3b82f6" : index === 1 ? "#10b981" : "#f59e0b"} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => `$${value}`} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </TabsContent>
      </Tabs>
    </div>
  )
}

