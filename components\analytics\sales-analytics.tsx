"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { ArrowUpRight, ArrowDownRight, TrendingUp, DollarSign, ShoppingCart, Users } from "lucide-react"

interface SalesAnalyticsProps {
  data: any
}

export function SalesAnalytics({ data }: SalesAnalyticsProps) {
  const [timeframe, setTimeframe] = useState("monthly")

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No sales analytics data available</p>
      </div>
    )
  }

  const { monthlySales, weeklySales, dailySales, growth, predictions, topProducts, topCustomers, salesByCategory } =
    data

  // Get sales data based on selected timeframe
  const getSalesData = () => {
    switch (timeframe) {
      case "daily":
        return dailySales
      case "weekly":
        return weeklySales
      case "monthly":
      default:
        return monthlySales
    }
  }

  // Get growth rate based on selected timeframe
  const getGrowthRate = () => {
    switch (timeframe) {
      case "daily":
        return growth.daily
      case "weekly":
        return growth.weekly
      case "monthly":
      default:
        return growth.monthly
    }
  }

  const salesData = getSalesData()
  const growthRate = getGrowthRate()

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(value)
  }

  // Generate colors for pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82CA9D", "#FFC658", "#8DD1E1"]

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(salesData.reduce((sum: number, item: any) => sum + item.sales, 0))}
            </div>
            <div className="flex items-center pt-1">
              {growthRate > 0 ? (
                <>
                  <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                  <span className="text-xs text-emerald-500">+{growthRate.toFixed(1)}%</span>
                </>
              ) : (
                <>
                  <ArrowDownRight className="mr-1 h-4 w-4 text-rose-500" />
                  <span className="text-xs text-rose-500">{growthRate.toFixed(1)}%</span>
                </>
              )}
              <span className="text-xs text-muted-foreground ml-2">from previous period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Predicted Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(predictions.nextMonth)}</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">Next month forecast</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Product</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold truncate">{topProducts[0]?.name || "N/A"}</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">
                {topProducts[0] ? formatCurrency(topProducts[0].totalSales) : "No data"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Customer</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold truncate">{topCustomers[0]?.name || "N/A"}</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">
                {topCustomers[0] ? formatCurrency(topCustomers[0].totalSpent) : "No data"}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sales Trend Chart */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Sales Trend</CardTitle>
              <CardDescription>Sales performance over time with prediction</CardDescription>
            </div>
            <Tabs value={timeframe} onValueChange={setTimeframe}>
              <TabsList>
                <TabsTrigger value="daily">Daily</TabsTrigger>
                <TabsTrigger value="weekly">Weekly</TabsTrigger>
                <TabsTrigger value="monthly">Monthly</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={salesData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <defs>
                  <linearGradient id="colorSales" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={timeframe === "daily" ? "day" : timeframe === "weekly" ? "week" : "month"} />
                <YAxis tickFormatter={(value) => formatCurrency(value).replace(".00", "")} />
                <Tooltip
                  formatter={(value) => [formatCurrency(value as number), "Sales"]}
                  labelFormatter={(label) => `${label}`}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="sales"
                  stroke="#8884d8"
                  fillOpacity={1}
                  fill="url(#colorSales)"
                  name="Sales"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Top Products and Categories */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Top Products</CardTitle>
            <CardDescription>Best selling products by revenue</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={topProducts.slice(0, 5)}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" tickFormatter={(value) => formatCurrency(value).replace(".00", "")} />
                  <YAxis
                    type="category"
                    dataKey="name"
                    width={100}
                    tickFormatter={(value) => (value.length > 15 ? `${value.substring(0, 15)}...` : value)}
                  />
                  <Tooltip formatter={(value) => [formatCurrency(value as number), "Sales"]} />
                  <Legend />
                  <Bar dataKey="totalSales" fill="#8884d8" name="Sales" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Sales by Category</CardTitle>
            <CardDescription>Revenue distribution across categories</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={salesByCategory}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="sales"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {salesByCategory.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Customers */}
      <Card>
        <CardHeader>
          <CardTitle>Top Customers</CardTitle>
          <CardDescription>Customers with highest spending</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={topCustomers.slice(0, 5)} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  tickFormatter={(value) => (value.length > 10 ? `${value.substring(0, 10)}...` : value)}
                />
                <YAxis tickFormatter={(value) => formatCurrency(value).replace(".00", "")} />
                <Tooltip formatter={(value) => [formatCurrency(value as number), "Total Spent"]} />
                <Legend />
                <Bar dataKey="totalSpent" fill="#82ca9d" name="Total Spent" />
                <Bar dataKey="orderCount" fill="#ffc658" name="Order Count" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

