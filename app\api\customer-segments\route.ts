import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const segments = await prisma.customerSegment.findMany({
      orderBy: {
        name: "asc",
      },
      include: {
        _count: {
          select: {
            customers: true,
          },
        },
      },
    })

    return NextResponse.json(segments)
  } catch (error) {
    console.error("[CUSTOMER_SEGMENTS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { name, description, criteria, isAutomatic } = body

    if (!name) {
      return new NextResponse("Name is required", { status: 400 })
    }

    const segment = await prisma.customerSegment.create({
      data: {
        name,
        description,
        criteria,
        isAutomatic: isAutomatic || false,
      },
    })

    return NextResponse.json(segment)
  } catch (error) {
    console.error("[CUSTOMER_SEGMENTS_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

