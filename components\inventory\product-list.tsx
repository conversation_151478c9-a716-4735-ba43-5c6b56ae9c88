"use client"

import { useState } from "react"
import { useCachedData } from "@/lib/hooks/use-cached-data"
import { fetchApi } from "@/lib/api-client"
import { useToast } from "@/hooks/use-toast"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { Edit, Trash2, Plus, Search } from "lucide-react"
import { ProductVariantsDisplay } from "./product-variants-display"

interface Product {
  id: string
  name: string
  sku: string
  barcode?: string
  description?: string
  price: number
  stockQuantity: number
  reorderPoint: number
  category: {
    id: string
    name: string
  }
  image?: string | null
  variants?: {
    id: string
    sku: string
    price: number | null
    stockQuantity: number
    isDefault: boolean
    options: {
      optionGroup: {
        name: string
        displayName: string
      }
      optionValue: string
    }[]
  }[]
}

export function ProductList() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null)

  const {
    data: products,
    isLoading,
    mutate,
  } = useCachedData<Product[]>({
    key: "products",
    fetcher: () => fetchApi("/api/products?includeVariants=true"),
  })

  const { data: categories } = useCachedData<{ id: string; name: string }[]>({
    key: "categories",
    fetcher: () => fetchApi("/api/categories"),
  })

  const filteredProducts = products?.filter((product) => {
    const matchesSearch =
      searchTerm === "" ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.barcode && product.barcode.includes(searchTerm))

    const matchesCategory = !categoryFilter || product.category.id === categoryFilter

    return matchesSearch && matchesCategory
  })

  const handleDeleteProduct = async (id: string) => {
    // Optimistic update
    const previousProducts = products || []

    mutate(
      previousProducts.filter((product) => product.id !== id),
      false,
    )

    try {
      await fetchApi(`/api/products/${id}`, {
        method: "DELETE",
      })

      toast({
        title: "Product deleted",
        description: "The product has been successfully deleted",
      })
    } catch (error) {
      // Revert on error
      mutate(previousProducts, false)

      toast({
        title: "Failed to delete product",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      })
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Products</CardTitle>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="h-10 rounded-md border border-input bg-background px-3 py-2"
              value={categoryFilter || ""}
              onChange={(e) => setCategoryFilter(e.target.value || null)}
            >
              <option value="">All Categories</option>
              {categories?.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div className="rounded-md border">
            <div className="grid grid-cols-12 gap-2 p-4 font-medium border-b">
              <div className="col-span-4">Product</div>
              <div className="col-span-2">SKU</div>
              <div className="col-span-2">Price</div>
              <div className="col-span-2">Stock</div>
              <div className="col-span-2">Actions</div>
            </div>

            {isLoading ? (
              Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="grid grid-cols-12 gap-2 p-4 border-b last:border-0">
                  <Skeleton className="col-span-4 h-6" />
                  <Skeleton className="col-span-2 h-6" />
                  <Skeleton className="col-span-2 h-6" />
                  <Skeleton className="col-span-2 h-6" />
                  <Skeleton className="col-span-2 h-6" />
                </div>
              ))
            ) : filteredProducts?.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">No products found</div>
            ) : (
              filteredProducts?.map((product) => (
                <div key={product.id} className="border-b last:border-0">
                  <div className="grid grid-cols-12 gap-2 p-4 items-center">
                    <div className="col-span-4 flex items-center gap-2">
                      {product.image ? (
                        <img
                          src={product.image || "/placeholder.svg"}
                          alt={product.name}
                          className="h-8 w-8 rounded object-cover"
                        />
                      ) : (
                        <div className="h-8 w-8 rounded bg-muted flex items-center justify-center text-xs">
                          {product.name.charAt(0)}
                        </div>
                      )}
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-xs text-muted-foreground">{product.category.name}</div>
                      </div>
                    </div>
                    <div className="col-span-2 text-sm">{product.sku}</div>
                    <div className="col-span-2 text-sm">${product.price.toFixed(2)}</div>
                    <div className="col-span-2 text-sm">
                      <span
                        className={product.stockQuantity <= product.reorderPoint ? "text-destructive font-medium" : ""}
                      >
                        {product.stockQuantity}
                      </span>
                    </div>
                    <div className="col-span-2 flex items-center gap-2">
                      <Button variant="ghost" size="icon">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteProduct(product.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {product.variants && product.variants.length > 0 && (
                    <div className="px-4 pb-4">
                      <ProductVariantsDisplay variants={product.variants} productPrice={product.price} />
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

