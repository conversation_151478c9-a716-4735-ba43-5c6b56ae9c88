import { prisma } from "@/lib/prisma"
import { getDay, getDate } from "date-fns"
import { executeReport } from "@/lib/services/report-service"
import { generateReportExport } from "@/lib/services/report-service"
import { sendScheduledReport } from "@/lib/services/email-service"

// Process scheduled reports
export async function processScheduledReports() {
  try {
    console.log("Processing scheduled reports:", new Date().toISOString())

    // Get all active scheduled reports
    const scheduledReports = await prisma.scheduledReport.findMany({
      where: {
        active: true,
      },
      include: {
        report: true,
      },
    })

    console.log(`Found ${scheduledReports.length} active scheduled reports`)

    // Current time
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    const currentDayOfWeek = getDay(now) // 0-6, 0 is Sunday
    const currentDayOfMonth = getDate(now) // 1-31

    // Process each scheduled report
    for (const schedule of scheduledReports) {
      try {
        // Check if it's time to run this schedule
        if (!shouldRunSchedule(schedule, currentHour, currentMinute, currentDayOfWeek, currentDayOfMonth)) {
          continue
        }

        console.log(`Processing scheduled report: ${schedule.name} (${schedule.id})`)

        // Execute the report
        const reportData = await executeReport({
          entity: schedule.report.entity,
          fields: schedule.report.fields,
          filters: schedule.report.filters,
          sortFields: schedule.report.sortFields,
          groupByFields: schedule.report.groupByFields,
        })

        // Generate export file
        const exportBuffer = await generateReportExport({
          data: reportData.data,
          format: schedule.format,
          reportName: schedule.report.name,
        })

        // Send email
        const emailSent = await sendScheduledReport(schedule, reportData, exportBuffer, schedule.format)

        // Update last run time
        await prisma.scheduledReport.update({
          where: { id: schedule.id },
          data: {
            lastRunAt: new Date(),
            lastRunStatus: emailSent ? "success" : "failed",
          },
        })

        console.log(`Scheduled report ${schedule.id} processed successfully`)
      } catch (error) {
        console.error(`Error processing scheduled report ${schedule.id}:`, error)

        // Update last run status
        await prisma.scheduledReport.update({
          where: { id: schedule.id },
          data: {
            lastRunAt: new Date(),
            lastRunStatus: "failed",
          },
        })
      }
    }
  } catch (error) {
    console.error("Error processing scheduled reports:", error)
  }
}

// Check if a schedule should run now
function shouldRunSchedule(
  schedule: any,
  currentHour: number,
  currentMinute: number,
  currentDayOfWeek: number,
  currentDayOfMonth: number,
): boolean {
  // Check hour and minute
  if (schedule.hour !== currentHour) return false

  // For minute, we check if we're within the same 15-minute window
  // This is to account for cron job timing which might not run exactly on the minute
  const minuteWindows = [0, 15, 30, 45]
  const scheduleWindow = minuteWindows.find((m) => schedule.minute >= m && schedule.minute < m + 15)
  const currentWindow = minuteWindows.find((m) => currentMinute >= m && currentMinute < m + 15)

  if (scheduleWindow !== currentWindow) return false

  // Check frequency-specific conditions
  switch (schedule.frequency) {
    case "daily":
      return true

    case "weekly":
      return schedule.dayOfWeek === currentDayOfWeek

    case "monthly":
      return schedule.dayOfMonth === currentDayOfMonth

    case "quarterly":
      // For quarterly, we check if it's the right day of the month
      // and if the month is a quarter month (1, 4, 7, 10)
      const now = new Date()
      const currentMonth = now.getMonth() + 1 // 1-12
      const isQuarterMonth = currentMonth % 3 === 1 // 1, 4, 7, 10
      return isQuarterMonth && schedule.dayOfMonth === currentDayOfMonth

    default:
      return false
  }
}

