import { prisma } from "@/lib/prisma"
import { subMonths, addMonths, format } from "date-fns"

// Get predictive analytics
export async function getPredictiveAnalytics() {
  // Generate sales forecast
  const salesForecast = await generateSalesForecast()

  // Generate inventory predictions
  const inventoryPredictions = await generateInventoryPredictions()

  // Generate demand forecast
  const demandForecast = await generateDemandForecast()

  // Generate seasonal trends
  const seasonalTrends = await generateSeasonalTrends()

  // Generate pricing recommendations
  const pricingRecommendations = await generatePricingRecommendations()

  return {
    salesForecast,
    inventoryPredictions,
    demandForecast,
    seasonalTrends,
    pricingRecommendations,
  }
}

// Generate sales forecast for the next 6 months
async function generateSalesForecast() {
  // Get historical sales data
  const historicalSales = await getHistoricalSales(12) // Last 12 months

  // Calculate average growth rate
  const growthRate = calculateGrowthRate(historicalSales)

  // Generate forecast
  const forecast = []
  const now = new Date()

  // Add last 6 months of actual data
  for (let i = 6; i > 0; i--) {
    const date = subMonths(now, i)
    const month = format(date, "MMM yyyy")
    const actualData = historicalSales.find((item) => item.month === month)

    if (actualData) {
      forecast.push({
        month,
        actual: actualData.sales,
        predicted: null,
        confidenceLevel: null,
        growthTrend: growthRate,
      })
    }
  }

  // Add next 6 months of predicted data
  let lastActualSales = historicalSales[historicalSales.length - 1].sales

  for (let i = 0; i < 6; i++) {
    const date = addMonths(now, i)
    const month = format(date, "MMM yyyy")

    // Apply growth rate and seasonal adjustment
    const seasonalFactor = getSeasonalFactor(month)
    const predictedSales = lastActualSales * (1 + growthRate / 100) * seasonalFactor

    // Calculate confidence level (decreases as we predict further into the future)
    const confidenceLevel = Math.max(95 - i * 5, 70)

    forecast.push({
      month,
      actual: null,
      predicted: predictedSales,
      confidenceLevel,
      growthTrend: growthRate,
    })

    lastActualSales = predictedSales
  }

  return forecast
}

// Generate inventory predictions
async function generateInventoryPredictions() {
  // Get products with inventory data
  const products = await prisma.product.findMany({
    select: {
      id: true,
      name: true,
      quantity: true,
      price: true,
    },
    where: {
      quantity: {
        gt: 0,
      },
    },
    take: 20,
  })

  // Get historical sales data for these products
  const productIds = products.map((p) => p.id)
  const orderItems = await prisma.orderItem.findMany({
    where: {
      productId: {
        in: productIds,
      },
      order: {
        createdAt: {
          gte: subMonths(new Date(), 3),
        },
      },
    },
    include: {
      product: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  // Calculate monthly demand for each product
  const productDemand: Record<string, number> = {}

  productIds.forEach((id) => {
    const items = orderItems.filter((item) => item.productId === id)
    const totalQuantity = items.reduce((sum, item) => sum + Number(item.quantity), 0)
    const monthlyDemand = totalQuantity / 3 // Average over 3 months
    productDemand[id] = monthlyDemand
  })

  // Generate predictions
  return products
    .map((product) => {
      const demand = productDemand[product.id] || 0.1 // Avoid division by zero
      const daysUntilStockout = demand > 0 ? Math.round((Number(product.quantity) / demand) * 30) : 999

      // Calculate recommended order quantity
      // Formula: 2 months of demand + safety stock - current inventory
      const safetyStock = Math.ceil(demand * 0.5) // 2 weeks of safety stock
      const recommendedOrder = Math.max(0, Math.ceil(demand * 2 + safetyStock - Number(product.quantity)))

      // Determine risk level
      let riskLevel = "Low"
      if (daysUntilStockout < 15) {
        riskLevel = "High"
      } else if (daysUntilStockout < 30) {
        riskLevel = "Medium"
      }

      return {
        id: product.id,
        name: product.name,
        currentStock: Number(product.quantity),
        predictedDemand: demand.toFixed(1),
        daysUntilStockout,
        recommendedOrder,
        riskLevel,
      }
    })
    .sort((a, b) => a.daysUntilStockout - b.daysUntilStockout)
}

// Generate demand forecast
async function generateDemandForecast() {
  // This would be a more complex implementation in a real system
  // For now, we'll return a simplified version

  const categories = await prisma.category.findMany({
    select: {
      id: true,
      name: true,
    },
  })

  return categories.map((category) => {
    // Generate random growth between -5% and +15%
    const growth = Math.round((Math.random() * 20 - 5) * 10) / 10

    return {
      id: category.id,
      name: category.name,
      currentDemand: Math.floor(Math.random() * 100) + 20,
      projectedGrowth: growth,
      confidenceLevel: Math.floor(Math.random() * 15) + 80,
    }
  })
}

// Generate seasonal trends
async function generateSeasonalTrends() {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]

  // These would be calculated from historical data in a real system
  // For now, we'll use predefined seasonal patterns
  const seasonalIndices = [90, 85, 95, 100, 105, 110, 115, 120, 110, 105, 95, 130]

  // Generate average sales for each month
  const averageSales = []
  for (let i = 0; i < 12; i++) {
    // Base value between 8000 and 12000
    const base = 8000 + Math.random() * 4000
    // Apply seasonal adjustment
    averageSales.push(base * (seasonalIndices[i] / 100))
  }

  return months.map((month, index) => ({
    month,
    seasonalIndex: seasonalIndices[index],
    averageSales: Math.round(averageSales[index]),
  }))
}

// Generate pricing recommendations
async function generatePricingRecommendations() {
  // Get top selling products
  const products = await prisma.product.findMany({
    select: {
      id: true,
      name: true,
      price: true,
    },
    take: 10,
  })

  return products.map((product) => {
    // Generate a price change recommendation between -10% and +15%
    const priceChangePercent = Math.round((Math.random() * 25 - 10) * 10) / 10
    const recommendedPrice = Number(product.price) * (1 + priceChangePercent / 100)

    // Predict impact on revenue and volume
    // In a real system, this would be based on price elasticity models
    const volumeImpact =
      priceChangePercent > 0
        ? -Math.round(priceChangePercent * 1.2 * 10) / 10 // Price up, volume down
        : Math.round(-priceChangePercent * 1.5 * 10) / 10 // Price down, volume up

    const revenueImpact = Math.round((priceChangePercent + volumeImpact) * 10) / 10

    return {
      id: product.id,
      name: product.name,
      currentPrice: Number(product.price),
      recommendedPrice: Math.round(recommendedPrice * 100) / 100,
      priceChange: priceChangePercent,
      predictedImpact: {
        revenue: revenueImpact,
        volume: volumeImpact,
      },
    }
  })
}

// Helper functions

// Get historical sales data
async function getHistoricalSales(months: number) {
  const now = new Date()
  const data = []

  for (let i = months - 1; i >= 0; i--) {
    const date = subMonths(now, i)
    const month = format(date, "MMM yyyy")

    // In a real system, this would query the database
    // For now, we'll generate some realistic data

    // Base value between 10000 and 15000
    const base = 10000 + Math.random() * 5000

    // Add a growth trend (0.5% to 2% per month)
    const trendFactor = 1 + ((months - i) * (0.5 + Math.random() * 1.5)) / 100

    // Add seasonal variation
    const seasonalFactor = getSeasonalFactor(month)

    // Calculate sales
    const sales = base * trendFactor * seasonalFactor

    data.push({
      month,
      sales: Math.round(sales),
    })
  }

  return data
}

// Calculate growth rate from historical data
function calculateGrowthRate(data: any[]) {
  if (data.length < 2) return 0

  // Calculate average monthly growth rate over the last 6 months
  let sum = 0
  let count = 0

  for (let i = data.length - 6; i < data.length - 1; i++) {
    if (i >= 0) {
      const current = data[i + 1].sales
      const previous = data[i].sales
      const monthlyGrowth = ((current - previous) / previous) * 100
      sum += monthlyGrowth
      count++
    }
  }

  return count > 0 ? Math.round((sum / count) * 10) / 10 : 0
}

// Get seasonal factor for a given month
function getSeasonalFactor(month: string) {
  // These would be calculated from historical data in a real system
  // For now, we'll use predefined seasonal factors
  const seasonalFactors: Record<string, number> = {
    Jan: 0.9,
    Feb: 0.85,
    Mar: 0.95,
    Apr: 1.0,
    May: 1.05,
    Jun: 1.1,
    Jul: 1.15,
    Aug: 1.2,
    Sep: 1.1,
    Oct: 1.05,
    Nov: 0.95,
    Dec: 1.3,
  }

  const monthName = month.substring(0, 3)
  return seasonalFactors[monthName] || 1.0
}

