import { Suspense } from "react"
import { InventoryForecast } from "@/components/inventory/inventory-forecast"
import { Skeleton } from "@/components/ui/skeleton"

export default function ForecastPage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Inventory Forecast</h2>
        <p className="text-muted-foreground">Predict future inventory levels and optimize reorder points</p>
      </div>

      <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
        <InventoryForecast />
      </Suspense>
    </div>
  )
}

