"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"
import { addDays, format } from "date-fns"
import type { DateRange } from "react-day-picker"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface DateRangePickerProps {
  dateRange: DateRange | undefined
  onDateRangeChange: (range: DateRange | undefined) => void
  className?: string
}

export function DateRangePicker({ dateRange, onDateRangeChange, className }: DateRangePickerProps) {
  const [isPopoverOpen, setIsPopoverOpen] = React.useState(false)

  // Predefined date ranges
  const handlePredefinedRange = (value: string) => {
    const today = new Date()

    switch (value) {
      case "today":
        onDateRangeChange({ from: today, to: today })
        break
      case "yesterday":
        const yesterday = addDays(today, -1)
        onDateRangeChange({ from: yesterday, to: yesterday })
        break
      case "last7days":
        onDateRangeChange({ from: addDays(today, -6), to: today })
        break
      case "last30days":
        onDateRangeChange({ from: addDays(today, -29), to: today })
        break
      case "thisMonth": {
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
        onDateRangeChange({ from: firstDayOfMonth, to: today })
        break
      }
      case "lastMonth": {
        const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
        onDateRangeChange({ from: firstDayOfLastMonth, to: lastDayOfLastMonth })
        break
      }
      case "thisYear": {
        const firstDayOfYear = new Date(today.getFullYear(), 0, 1)
        onDateRangeChange({ from: firstDayOfYear, to: today })
        break
      }
      default:
        break
    }

    setIsPopoverOpen(false)
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn("w-[300px] justify-start text-left font-normal", !dateRange && "text-muted-foreground")}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange?.from ? (
              dateRange.to ? (
                <>
                  {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                </>
              ) : (
                format(dateRange.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3 border-b">
            <Select onValueChange={handlePredefinedRange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a preset range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="yesterday">Yesterday</SelectItem>
                <SelectItem value="last7days">Last 7 days</SelectItem>
                <SelectItem value="last30days">Last 30 days</SelectItem>
                <SelectItem value="thisMonth">This month</SelectItem>
                <SelectItem value="lastMonth">Last month</SelectItem>
                <SelectItem value="thisYear">This year</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange?.from}
            selected={dateRange}
            onSelect={onDateRangeChange}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}

