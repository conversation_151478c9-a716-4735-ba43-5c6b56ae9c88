"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useSocket } from "@/components/socket-provider"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"
import type { SocketPayload } from "@/lib/socket"

interface Product {
  id: string
  name: string
  sku: string
  stockQuantity: number
  reorderPoint: number
}

interface RealTimeInventoryProps {
  productId?: string
  showNotifications?: boolean
  onStockUpdate?: (product: Product) => void
  children?: React.ReactNode
}

export function RealTimeInventory({
  productId,
  showNotifications = true,
  onStockUpdate,
  children,
}: RealTimeInventoryProps) {
  const { subscribe, emit } = useSocket()
  const { toast } = useToast()
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  useEffect(() => {
    // Subscribe to inventory updates
    const unsubscribe = subscribe("inventory_update", (payload: SocketPayload) => {
      // If we're watching a specific product, only process updates for that product
      if (productId && payload.data.id !== productId) {
        return
      }

      setLastUpdate(new Date())

      // Show notification if enabled
      if (showNotifications) {
        toast({
          title: "Inventory Updated",
          description: `${payload.data.name}: ${payload.data.stockQuantity} in stock`,
        })
      }

      // Call the callback if provided
      if (onStockUpdate) {
        onStockUpdate(payload.data)
      }
    })

    // Subscribe to low stock alerts
    const unsubscribeLowStock = subscribe("low_stock_alert", (payload: SocketPayload) => {
      // If we're watching a specific product, only process alerts for that product
      if (productId && payload.data.id !== productId) {
        return
      }

      // Always show low stock alerts
      toast({
        title: "Low Stock Alert",
        description: `${payload.data.name} is below the reorder point (${payload.data.stockQuantity} remaining)`,
        variant: "destructive",
      })

      // Call the callback if provided
      if (onStockUpdate) {
        onStockUpdate(payload.data)
      }
    })

    // Clean up subscriptions
    return () => {
      unsubscribe()
      unsubscribeLowStock()
    }
  }, [productId, showNotifications, subscribe, toast, onStockUpdate])

  // If this is just a wrapper component, render children
  if (children) {
    return <>{children}</>
  }

  // Otherwise render a status indicator
  return (
    <div className="flex items-center gap-2">
      <Badge variant="outline" className="text-xs">
        {lastUpdate ? `Last updated: ${lastUpdate.toLocaleTimeString()}` : "Waiting for updates..."}
      </Badge>
    </div>
  )
}

