"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { UserNav } from "@/components/user-nav"
import { ModeToggle } from "@/components/mode-toggle"
import { Button } from "@/components/ui/button"
import { Menu } from "lucide-react"
import OfflineStatus from "@/components/offline/offline-status"

interface NavbarProps {
  toggleSidebar?: () => void
}

export function Navbar({ toggleSidebar }: NavbarProps) {
  const pathname = usePathname()

  return (
    <nav className="border-b bg-background">
      <div className="flex h-16 items-center px-4">
        <div className="md:hidden">
          <Button variant="ghost" size="icon" onClick={toggleSidebar}>
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle sidebar</span>
          </Button>
        </div>

        <Link href="/" className="flex items-center">
          <span className="hidden md:inline-block text-xl font-bold">StockSync</span>
        </Link>

        <div className="ml-auto flex items-center space-x-4">
          <OfflineStatus />
          <ModeToggle />
          <UserNav />
        </div>
      </div>
    </nav>
  )
}

