"use client"

import { useState, useEffect, useRef } from "react"

type WorkerTask = "calculateSalesTrends" | "analyzeInventory" | "forecastInventory"

interface UseWorkerOptions {
  onMessage?: (result: any) => void
  onError?: (error: any) => void
}

export function useAnalyticsWorker(options: UseWorkerOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [result, setResult] = useState<any>(null)
  const workerRef = useRef<Worker | null>(null)

  useEffect(() => {
    // Create worker only on client side
    if (typeof window !== "undefined") {
      workerRef.current = new Worker(new URL("../lib/workers/analytics-worker.ts", import.meta.url))

      workerRef.current.onmessage = (event) => {
        const { task, result } = event.data
        setResult(result)
        setIsLoading(false)
        if (options.onMessage) {
          options.onMessage(result)
        }
      }

      workerRef.current.onerror = (event) => {
        const error = new Error(`Worker error: ${event.message}`)
        setError(error)
        setIsLoading(false)
        if (options.onError) {
          options.onError(error)
        }
      }
    }

    // Cleanup worker on unmount
    return () => {
      workerRef.current?.terminate()
    }
  }, [options.onMessage, options.onError])

  const runTask = (task: WorkerTask, data: any) => {
    if (!workerRef.current) {
      const error = new Error("Worker not initialized")
      setError(error)
      if (options.onError) {
        options.onError(error)
      }
      return
    }

    setIsLoading(true)
    setError(null)
    workerRef.current.postMessage({ task, data })
  }

  return {
    runTask,
    isLoading,
    error,
    result,
  }
}

