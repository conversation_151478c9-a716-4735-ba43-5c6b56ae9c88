import { prisma } from "@/lib/prisma"
import { subMonths, subWeeks, subDays, format, startOfDay, endOfDay, startOfMonth, endOfMonth } from "date-fns"

// Get sales trend analysis
export async function getSalesTrends() {
  const now = new Date()

  // Get sales data for the last 12 months
  const monthlySales = await getMonthlyData(12)

  // Get sales data for the last 12 weeks
  const weeklySales = await getWeeklyData(12)

  // Get sales data for the last 30 days
  const dailySales = await getDailyData(30)

  // Calculate growth rates
  const monthlyGrowth = calculateGrowthRate(monthlySales)
  const weeklyGrowth = calculateGrowthRate(weeklySales)
  const dailyGrowth = calculateGrowthRate(dailySales)

  // Predict next month's sales
  const predictedSales = predictNextPeriodSales(monthlySales)

  // Identify top-selling products
  const topProducts = await getTopSellingProducts()

  // Identify top customers
  const topCustomers = await getTopCustomers()

  // Calculate sales by category
  const salesByCategory = await getSalesByCategory()

  return {
    monthlySales,
    weeklySales,
    dailySales,
    growth: {
      monthly: monthlyGrowth,
      weekly: weeklyGrowth,
      daily: dailyGrowth,
    },
    predictions: {
      nextMonth: predictedSales,
    },
    topProducts,
    topCustomers,
    salesByCategory,
  }
}

// Get inventory analytics
export async function getInventoryAnalytics() {
  // Get current inventory status
  const inventoryStatus = await getCurrentInventoryStatus()

  // Calculate inventory turnover
  const inventoryTurnover = await calculateInventoryTurnover()

  // Identify slow-moving products
  const slowMovingProducts = await getSlowMovingProducts()

  // Identify fast-moving products
  const fastMovingProducts = await getFastMovingProducts()

  // Calculate optimal reorder points
  const reorderPoints = await calculateReorderPoints()

  // Predict stockouts
  const predictedStockouts = await predictStockouts()

  return {
    inventoryStatus,
    inventoryTurnover,
    slowMovingProducts,
    fastMovingProducts,
    reorderPoints,
    predictedStockouts,
  }
}

// Get customer analytics
export async function getCustomerAnalytics() {
  // Calculate customer acquisition cost
  const acquisitionCost = await calculateCustomerAcquisitionCost()

  // Calculate customer lifetime value
  const lifetimeValue = await calculateCustomerLifetimeValue()

  // Calculate customer retention rate
  const retentionRate = await calculateCustomerRetentionRate()

  // Identify at-risk customers
  const atRiskCustomers = await identifyAtRiskCustomers()

  // Identify loyal customers
  const loyalCustomers = await identifyLoyalCustomers()

  // Calculate customer segmentation
  const customerSegmentation = await calculateCustomerSegmentation()

  return {
    acquisitionCost,
    lifetimeValue,
    retentionRate,
    atRiskCustomers,
    loyalCustomers,
    customerSegmentation,
  }
}

// Helper functions

// Get monthly sales data
async function getMonthlyData(months: number) {
  const data = []
  const now = new Date()

  for (let i = months - 1; i >= 0; i--) {
    const date = subMonths(now, i)
    const startDate = startOfMonth(date)
    const endDate = endOfMonth(date)

    const monthData = await prisma.order.aggregate({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: "completed",
      },
      _sum: {
        total: true,
      },
    })

    data.push({
      date: format(date, "yyyy-MM"),
      month: format(date, "MMM yyyy"),
      sales: Number(monthData._sum.total || 0),
    })
  }

  return data
}

// Get weekly sales data
async function getWeeklyData(weeks: number) {
  const data = []
  const now = new Date()

  for (let i = weeks - 1; i >= 0; i--) {
    const date = subWeeks(now, i)
    const startDate = startOfDay(subDays(date, date.getDay()))
    const endDate = endOfDay(subDays(date, date.getDay() - 6))

    const weekData = await prisma.order.aggregate({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: "completed",
      },
      _sum: {
        total: true,
      },
    })

    data.push({
      date: format(date, "yyyy-'W'ww"),
      week: `Week ${format(date, "w")}`,
      sales: Number(weekData._sum.total || 0),
    })
  }

  return data
}

// Get daily sales data
async function getDailyData(days: number) {
  const data = []
  const now = new Date()

  for (let i = days - 1; i >= 0; i--) {
    const date = subDays(now, i)
    const startDate = startOfDay(date)
    const endDate = endOfDay(date)

    const dayData = await prisma.order.aggregate({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: "completed",
      },
      _sum: {
        total: true,
      },
    })

    data.push({
      date: format(date, "yyyy-MM-dd"),
      day: format(date, "MMM d"),
      sales: Number(dayData._sum.total || 0),
    })
  }

  return data
}

// Calculate growth rate
function calculateGrowthRate(data: any[]) {
  if (data.length < 2) return 0

  const currentPeriod = data[data.length - 1].sales
  const previousPeriod = data[data.length - 2].sales

  if (previousPeriod === 0) return 0

  return ((currentPeriod - previousPeriod) / previousPeriod) * 100
}

// Predict next period sales using simple moving average
function predictNextPeriodSales(data: any[]) {
  if (data.length < 3) return 0

  // Use last 3 periods for prediction
  const lastThreePeriods = data.slice(-3)
  const sum = lastThreePeriods.reduce((acc, period) => acc + period.sales, 0)
  const average = sum / 3

  // Apply trend factor
  const trend = calculateGrowthRate(data) / 100
  const prediction = average * (1 + trend)

  return prediction
}

// Get top selling products
async function getTopSellingProducts(limit = 10) {
  const topProducts = await prisma.orderItem.groupBy({
    by: ["productId"],
    _sum: {
      quantity: true,
      price: true,
    },
    orderBy: {
      _sum: {
        price: "desc",
      },
    },
    take: limit,
  })

  // Get product details
  const productsWithDetails = await Promise.all(
    topProducts.map(async (item) => {
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        select: {
          name: true,
          sku: true,
          category: {
            select: {
              name: true,
            },
          },
        },
      })

      return {
        productId: item.productId,
        name: product?.name || "Unknown Product",
        sku: product?.sku || "",
        category: product?.category?.name || "Uncategorized",
        totalQuantity: item._sum.quantity || 0,
        totalSales: item._sum.price || 0,
      }
    }),
  )

  return productsWithDetails
}

// Get top customers
async function getTopCustomers(limit = 10) {
  const topCustomers = await prisma.order.groupBy({
    by: ["customerId"],
    _sum: {
      total: true,
    },
    _count: {
      id: true,
    },
    orderBy: {
      _sum: {
        total: "desc",
      },
    },
    take: limit,
    where: {
      customerId: {
        not: null,
      },
    },
  })

  // Get customer details
  const customersWithDetails = await Promise.all(
    topCustomers.map(async (item) => {
      const customer = await prisma.customer.findUnique({
        where: { id: item.customerId as string },
        select: {
          name: true,
          email: true,
        },
      })

      return {
        customerId: item.customerId,
        name: customer?.name || "Unknown Customer",
        email: customer?.email || "",
        orderCount: item._count.id || 0,
        totalSpent: item._sum.total || 0,
      }
    }),
  )

  return customersWithDetails
}

// Get sales by category
async function getSalesByCategory() {
  // Get all categories
  const categories = await prisma.category.findMany()

  // Get sales data for each category
  const salesByCategory = await Promise.all(
    categories.map(async (category) => {
      const sales = await prisma.orderItem.aggregate({
        where: {
          product: {
            categoryId: category.id,
          },
        },
        _sum: {
          price: true,
        },
      })

      return {
        categoryId: category.id,
        name: category.name,
        sales: Number(sales._sum.price || 0),
      }
    }),
  )

  // Sort by sales
  return salesByCategory.sort((a, b) => b.sales - a.sales)
}

// Get current inventory status
async function getCurrentInventoryStatus() {
  const products = await prisma.product.findMany({
    select: {
      id: true,
      name: true,
      sku: true,
      quantity: true,
      price: true,
      cost: true,
      category: {
        select: {
          name: true,
        },
      },
    },
  })

  const inventoryValue = products.reduce((sum, product) => {
    const cost = Number(product.cost || product.price)
    return sum + Number(product.quantity) * cost
  }, 0)

  const lowStockThreshold = 5 // This could be a setting
  const lowStockItems = products.filter((product) => Number(product.quantity) <= lowStockThreshold)

  const outOfStockItems = products.filter((product) => Number(product.quantity) <= 0)

  return {
    totalProducts: products.length,
    totalInventoryValue: inventoryValue,
    lowStockItems: lowStockItems.length,
    outOfStockItems: outOfStockItems.length,
    inventoryDetails: products.map((product) => ({
      id: product.id,
      name: product.name,
      sku: product.sku,
      quantity: Number(product.quantity),
      value: Number(product.quantity) * Number(product.cost || product.price),
      category: product.category?.name || "Uncategorized",
    })),
  }
}

// Calculate inventory turnover
async function calculateInventoryTurnover() {
  // This is a simplified calculation
  // Inventory turnover = Cost of goods sold / Average inventory value

  // Get cost of goods sold (last 30 days)
  const thirtyDaysAgo = subDays(new Date(), 30)

  const sales = await prisma.orderItem.findMany({
    where: {
      order: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
        status: "completed",
      },
    },
    include: {
      product: {
        select: {
          cost: true,
        },
      },
    },
  })

  const cogs = sales.reduce((sum, item) => {
    const cost = Number(item.product?.cost || item.price)
    return sum + cost * Number(item.quantity)
  }, 0)

  // Get current inventory value
  const inventoryStatus = await getCurrentInventoryStatus()
  const inventoryValue = inventoryStatus.totalInventoryValue

  // Calculate turnover (annualized)
  const annualizedCOGS = cogs * 12
  const turnover = inventoryValue > 0 ? annualizedCOGS / inventoryValue : 0

  return {
    turnover,
    cogs,
    inventoryValue,
  }
}

// Get slow-moving products
async function getSlowMovingProducts(limit = 10) {
  const thirtyDaysAgo = subDays(new Date(), 30)

  // Get products with low sales
  const productSales = await prisma.orderItem.groupBy({
    by: ["productId"],
    _sum: {
      quantity: true,
    },
    where: {
      order: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    },
  })

  // Get all products
  const allProducts = await prisma.product.findMany({
    where: {
      quantity: {
        gt: 0,
      },
    },
    select: {
      id: true,
      name: true,
      sku: true,
      quantity: true,
      price: true,
      cost: true,
      category: {
        select: {
          name: true,
        },
      },
    },
  })

  // Map sales data to products
  const productsWithSales = allProducts.map((product) => {
    const salesData = productSales.find((item) => item.productId === product.id)
    return {
      ...product,
      salesQuantity: Number(salesData?._sum.quantity || 0),
      inventoryValue: Number(product.quantity) * Number(product.cost || product.price),
    }
  })

  // Sort by sales quantity (ascending) and filter to products with inventory
  const slowMoving = productsWithSales
    .filter((product) => Number(product.quantity) > 0)
    .sort((a, b) => a.salesQuantity - b.salesQuantity)
    .slice(0, limit)

  return slowMoving
}

// Get fast-moving products
async function getFastMovingProducts(limit = 10) {
  const thirtyDaysAgo = subDays(new Date(), 30)

  // Get products with high sales
  const productSales = await prisma.orderItem.groupBy({
    by: ["productId"],
    _sum: {
      quantity: true,
    },
    where: {
      order: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    },
    orderBy: {
      _sum: {
        quantity: "desc",
      },
    },
    take: limit,
  })

  // Get product details
  const fastMovingProducts = await Promise.all(
    productSales.map(async (item) => {
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        select: {
          id: true,
          name: true,
          sku: true,
          quantity: true,
          price: true,
          cost: true,
          category: {
            select: {
              name: true,
            },
          },
        },
      })

      return {
        ...product,
        salesQuantity: Number(item._sum.quantity || 0),
        inventoryValue: Number(product?.quantity || 0) * Number(product?.cost || product?.price || 0),
        daysOfInventoryLeft:
          product?.quantity && item._sum.quantity
            ? Math.round(Number(product.quantity) / (Number(item._sum.quantity) / 30))
            : null,
      }
    }),
  )

  return fastMovingProducts
}

// Calculate optimal reorder points
async function calculateReorderPoints() {
  const thirtyDaysAgo = subDays(new Date(), 30)

  // Get sales velocity for all products
  const productSales = await prisma.orderItem.groupBy({
    by: ["productId"],
    _sum: {
      quantity: true,
    },
    where: {
      order: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    },
  })

  // Get all products
  const allProducts = await prisma.product.findMany({
    select: {
      id: true,
      name: true,
      sku: true,
      quantity: true,
    },
  })

  // Calculate reorder points
  // Reorder Point = (Average Daily Usage × Lead Time) + Safety Stock
  const reorderPoints = allProducts.map((product) => {
    const salesData = productSales.find((item) => item.productId === product.id)
    const dailyUsage = salesData ? Number(salesData._sum.quantity) / 30 : 0

    // Assume 7 days lead time and safety stock of 3 days
    const leadTime = 7
    const safetyStock = dailyUsage * 3

    const reorderPoint = Math.ceil(dailyUsage * leadTime + safetyStock)

    return {
      id: product.id,
      name: product.name,
      sku: product.sku,
      currentStock: Number(product.quantity),
      dailyUsage,
      reorderPoint,
      needsReorder: Number(product.quantity) <= reorderPoint,
    }
  })

  return reorderPoints
}

// Predict stockouts
async function predictStockouts() {
  const thirtyDaysAgo = subDays(new Date(), 30)

  // Get sales velocity for all products
  const productSales = await prisma.orderItem.groupBy({
    by: ["productId"],
    _sum: {
      quantity: true,
    },
    where: {
      order: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    },
  })

  // Get all products
  const allProducts = await prisma.product.findMany({
    where: {
      quantity: {
        gt: 0,
      },
    },
    select: {
      id: true,
      name: true,
      sku: true,
      quantity: true,
      category: {
        select: {
          name: true,
        },
      },
    },
  })

  // Predict days until stockout
  const predictions = allProducts.map((product) => {
    const salesData = productSales.find((item) => item.productId === product.id)
    const dailyUsage = salesData ? Number(salesData._sum.quantity) / 30 : 0

    let daysUntilStockout = null
    if (dailyUsage > 0) {
      daysUntilStockout = Math.round(Number(product.quantity) / dailyUsage)
    }

    return {
      id: product.id,
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "Uncategorized",
      currentStock: Number(product.quantity),
      dailyUsage,
      daysUntilStockout,
      riskLevel:
        daysUntilStockout === null
          ? "Low"
          : daysUntilStockout <= 7
            ? "High"
            : daysUntilStockout <= 14
              ? "Medium"
              : "Low",
    }
  })

  // Filter to products at risk of stockout in the next 30 days
  const atRiskProducts = predictions
    .filter((product) => product.daysUntilStockout !== null && product.daysUntilStockout <= 30)
    .sort((a, b) => (a.daysUntilStockout || 0) - (b.daysUntilStockout || 0))

  return atRiskProducts
}

// Calculate customer acquisition cost
async function calculateCustomerAcquisitionCost() {
  // This would typically involve marketing costs, which we don't have in our system
  // For demonstration, we'll return a placeholder
  return {
    cac: 25.0,
    marketingSpend: 5000,
    newCustomers: 200,
  }
}

// Calculate customer lifetime value
async function calculateCustomerLifetimeValue() {
  // Get average order value
  const averageOrderValue = await prisma.order.aggregate({
    where: {
      status: "completed",
      customerId: {
        not: null,
      },
    },
    _avg: {
      total: true,
    },
  })

  // Get average purchase frequency (orders per customer)
  const customers = await prisma.customer.findMany({
    include: {
      _count: {
        select: {
          orders: true,
        },
      },
    },
  })

  const totalCustomers = customers.length
  const totalOrders = customers.reduce((sum, customer) => sum + customer._count.orders, 0)
  const averageOrdersPerCustomer = totalCustomers > 0 ? totalOrders / totalCustomers : 0

  // Assume average customer lifespan of 3 years and 30% profit margin
  const averageLifespan = 3
  const profitMargin = 0.3

  // Calculate CLV
  const clv = Number(averageOrderValue._avg.total || 0) * averageOrdersPerCustomer * averageLifespan * profitMargin

  return {
    clv,
    averageOrderValue: Number(averageOrderValue._avg.total || 0),
    averageOrdersPerCustomer,
    averageLifespan,
    profitMargin,
  }
}

// Calculate customer retention rate
async function calculateCustomerRetentionRate() {
  const oneYearAgo = subMonths(new Date(), 12)
  const twoYearsAgo = subMonths(new Date(), 24)

  // Get customers who made a purchase between 1-2 years ago
  const previousCustomers = await prisma.order.groupBy({
    by: ["customerId"],
    where: {
      createdAt: {
        gte: twoYearsAgo,
        lt: oneYearAgo,
      },
      customerId: {
        not: null,
      },
    },
  })

  // Get customers from the previous period who also made a purchase in the last year
  const retainedCustomers = await prisma.order.groupBy({
    by: ["customerId"],
    where: {
      createdAt: {
        gte: oneYearAgo,
      },
      customerId: {
        in: previousCustomers.map((c) => c.customerId),
      },
    },
  })

  // Calculate retention rate
  const retentionRate = previousCustomers.length > 0 ? (retainedCustomers.length / previousCustomers.length) * 100 : 0

  return {
    retentionRate,
    previousCustomers: previousCustomers.length,
    retainedCustomers: retainedCustomers.length,
  }
}

// Identify at-risk customers
async function identifyAtRiskCustomers(limit = 10) {
  const sixMonthsAgo = subMonths(new Date(), 6)
  const oneYearAgo = subMonths(new Date(), 12)

  // Get customers who made purchases between 6-12 months ago but not in the last 6 months
  const atRiskCustomerIds = await prisma.order.groupBy({
    by: ["customerId"],
    where: {
      createdAt: {
        gte: oneYearAgo,
        lt: sixMonthsAgo,
      },
      customerId: {
        not: null,
      },
    },
    having: {
      customerId: {
        _count: {
          gt: 0,
        },
      },
    },
  })

  // Get recent orders for these customers
  const recentOrders = await prisma.order.findMany({
    where: {
      customerId: {
        in: atRiskCustomerIds.map((c) => c.customerId),
      },
      createdAt: {
        gte: sixMonthsAgo,
      },
    },
    select: {
      customerId: true,
    },
  })

  // Filter out customers who have recent orders
  const recentCustomerIds = new Set(recentOrders.map((o) => o.customerId))
  const trulyAtRiskCustomerIds = atRiskCustomerIds
    .filter((c) => !recentCustomerIds.has(c.customerId))
    .map((c) => c.customerId)

  // Get customer details
  const atRiskCustomers = await prisma.customer.findMany({
    where: {
      id: {
        in: trulyAtRiskCustomerIds as string[],
      },
    },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      orders: {
        orderBy: {
          createdAt: "desc",
        },
        take: 1,
        select: {
          createdAt: true,
          total: true,
        },
      },
      _count: {
        select: {
          orders: true,
        },
      },
    },
    take: limit,
  })

  return atRiskCustomers.map((customer) => ({
    id: customer.id,
    name: customer.name,
    email: customer.email,
    phone: customer.phone,
    lastOrderDate: customer.orders[0]?.createdAt,
    lastOrderAmount: customer.orders[0]?.total,
    totalOrders: customer._count.orders,
    daysSinceLastOrder: customer.orders[0]
      ? Math.round((new Date().getTime() - customer.orders[0].createdAt.getTime()) / (1000 * 60 * 60 * 24))
      : null,
  }))
}

// Identify loyal customers
async function identifyLoyalCustomers(limit = 10) {
  // Get customers with the most orders
  const loyalCustomers = await prisma.customer.findMany({
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      orders: {
        orderBy: {
          createdAt: "desc",
        },
        take: 1,
        select: {
          createdAt: true,
          total: true,
        },
      },
      _count: {
        select: {
          orders: true,
        },
      },
    },
    orderBy: {
      orders: {
        _count: "desc",
      },
    },
    take: limit,
  })

  // Calculate total spent
  const customerSpending = await Promise.all(
    loyalCustomers.map(async (customer) => {
      const spending = await prisma.order.aggregate({
        where: {
          customerId: customer.id,
        },
        _sum: {
          total: true,
        },
      })

      return {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        lastOrderDate: customer.orders[0]?.createdAt,
        lastOrderAmount: customer.orders[0]?.total,
        totalOrders: customer._count.orders,
        totalSpent: Number(spending._sum.total || 0),
        averageOrderValue: customer._count.orders > 0 ? Number(spending._sum.total || 0) / customer._count.orders : 0,
      }
    }),
  )

  return customerSpending
}

// Calculate customer segmentation
async function calculateCustomerSegmentation() {
  // Get all customers with their order counts and total spent
  const customers = await prisma.customer.findMany({
    select: {
      id: true,
      _count: {
        select: {
          orders: true,
        },
      },
    },
  })

  // Get total spent per customer
  const customerSpending = await Promise.all(
    customers.map(async (customer) => {
      const spending = await prisma.order.aggregate({
        where: {
          customerId: customer.id,
        },
        _sum: {
          total: true,
        },
      })

      return {
        id: customer.id,
        orderCount: customer._count.orders,
        totalSpent: Number(spending._sum.total || 0),
      }
    }),
  )

  // Calculate median values for segmentation
  const orderCounts = customerSpending.map((c) => c.orderCount).sort((a, b) => a - b)
  const spendingAmounts = customerSpending.map((c) => c.totalSpent).sort((a, b) => a - b)

  const medianOrderCount = orderCounts[Math.floor(orderCounts.length / 2)] || 0
  const medianSpending = spendingAmounts[Math.floor(spendingAmounts.length / 2)] || 0

  // Segment customers
  const segments = {
    highValueLoyal: 0,
    highValueNew: 0,
    lowValueLoyal: 0,
    lowValueNew: 0,
  }

  customerSpending.forEach((customer) => {
    if (customer.totalSpent > medianSpending && customer.orderCount > medianOrderCount) {
      segments.highValueLoyal++
    } else if (customer.totalSpent > medianSpending && customer.orderCount <= medianOrderCount) {
      segments.highValueNew++
    } else if (customer.totalSpent <= medianSpending && customer.orderCount > medianOrderCount) {
      segments.lowValueLoyal++
    } else {
      segments.lowValueNew++
    }
  })

  return {
    segments,
    totalCustomers: customers.length,
    medianOrderCount,
    medianSpending,
  }
}

