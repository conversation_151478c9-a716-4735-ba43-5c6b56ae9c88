"use client"

import { useState, useEffect } from "react"
import { useSocket } from "@/components/socket-provider"
import type { SocketPayload } from "@/lib/socket"

interface SyncOptions {
  type: "inventory" | "orders" | "customers"
  limit?: number
  offset?: number
  filter?: Record<string, any>
}

export function useRealTimeSync<T>(options: SyncOptions) {
  const { emit, subscribe, status, isOnline } = useSocket()
  const [data, setData] = useState<T | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  // Request data sync when component mounts or options change
  useEffect(() => {
    if (status === "connected" && isOnline) {
      setIsLoading(true)

      // Create a unique request ID
      const requestId = `sync-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

      // Send sync request
      emit("sync_request", {
        id: requestId,
        timestamp: Date.now(),
        data: options,
      })

      // Set up listener for the response
      const unsubscribe = subscribe("sync_response", (payload: SocketPayload) => {
        // Only process responses for our request
        if (payload.requestId === requestId) {
          setData(payload.data as T)
          setLastUpdated(new Date())
          setIsLoading(false)
        }
      })

      // Clean up listener
      return () => {
        unsubscribe()
      }
    }
  }, [options, status, isOnline, emit, subscribe])

  // Set up listeners for real-time updates
  useEffect(() => {
    if (options.type === "inventory") {
      const unsubscribe = subscribe("inventory_update", (payload: SocketPayload) => {
        setData((prevData: any) => {
          if (!prevData || !prevData.products) return prevData

          // Update the product in the list
          const updatedProducts = prevData.products.map((product: any) =>
            product.id === payload.data.id ? { ...product, ...payload.data } : product,
          )

          return { ...prevData, products: updatedProducts }
        })

        setLastUpdated(new Date())
      })

      return () => {
        unsubscribe()
      }
    } else if (options.type === "orders") {
      // Set up listeners for order events
      const unsubscribeCreated = subscribe("order_created", (payload: SocketPayload) => {
        setData((prevData: any) => {
          if (!prevData || !prevData.orders) return prevData

          // Add the new order to the list
          return {
            ...prevData,
            orders: [payload.data, ...prevData.orders],
          }
        })

        setLastUpdated(new Date())
      })

      const unsubscribeUpdated = subscribe("order_updated", (payload: SocketPayload) => {
        setData((prevData: any) => {
          if (!prevData || !prevData.orders) return prevData

          // Update the order in the list
          const updatedOrders = prevData.orders.map((order: any) =>
            order.id === payload.data.id ? { ...order, ...payload.data } : order,
          )

          return { ...prevData, orders: updatedOrders }
        })

        setLastUpdated(new Date())
      })

      return () => {
        unsubscribeCreated()
        unsubscribeUpdated()
      }
    }

    // Default cleanup
    return () => {}
  }, [options.type, subscribe])

  // Function to manually refresh data
  const refresh = () => {
    if (status === "connected" && isOnline) {
      setIsLoading(true)

      emit("sync_request", {
        id: `sync-${Date.now()}`,
        timestamp: Date.now(),
        data: options,
      })
    }
  }

  return {
    data,
    isLoading,
    error,
    lastUpdated,
    refresh,
    isOnline,
    connectionStatus: status,
  }
}

