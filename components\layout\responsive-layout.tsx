"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

interface ResponsiveLayoutProps {
  sidebar: React.ReactNode
  content: React.ReactNode
  className?: string
}

export function ResponsiveLayout({ sidebar, content, className }: ResponsiveLayoutProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkIsMobile()
    window.addEventListener("resize", checkIsMobile)
    return () => window.removeEventListener("resize", checkIsMobile)
  }, [])

  return (
    <div className={cn("flex h-full", className)}>
      {/* Sidebar for desktop */}
      <div
        className={cn(
          "fixed inset-y-0 z-20 hidden w-64 flex-shrink-0 overflow-y-auto border-r bg-background transition-all duration-300 lg:flex lg:flex-col",
          {
            "lg:w-64": !isMobile,
            "lg:w-20": isMobile && !sidebarOpen,
          },
        )}
      >
        {sidebar}
      </div>

      {/* Mobile sidebar overlay */}
      {isMobile && sidebarOpen && (
        <div className="fixed inset-0 z-10 bg-black/50" onClick={() => setSidebarOpen(false)} />
      )}

      {/* Mobile sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-20 w-64 flex-shrink-0 overflow-y-auto border-r bg-background transition-all duration-300 lg:hidden",
          {
            "translate-x-0": sidebarOpen,
            "-translate-x-full": !sidebarOpen,
          },
        )}
      >
        {sidebar}
      </div>

      {/* Main content */}
      <div
        className={cn("flex-1 overflow-y-auto transition-all duration-300", {
          "lg:pl-64": !isMobile,
          "lg:pl-20": isMobile && !sidebarOpen,
        })}
      >
        {/* Mobile header with menu button */}
        <div className="sticky top-0 z-10 flex h-16 items-center border-b bg-background px-4 lg:hidden">
          <button
            type="button"
            className="rounded-md p-2 text-muted-foreground hover:bg-muted"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="h-6 w-6"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4 md:p-6 lg:p-8">{content}</div>
      </div>
    </div>
  )
}

