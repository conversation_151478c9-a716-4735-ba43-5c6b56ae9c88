import { NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { getCachedData } from "@/lib/cache"

const SHORT_TTL = 60 // Define SHORT_TTL in seconds (e.g., 60 seconds)

export async function GET() {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const stats = await getCachedData(
      "dashboard:stats",
      async () => {
        // Fetch dashboard statistics
        const [totalProducts, lowStockProducts, totalOrders, totalSales, recentOrders, topSellingProducts] =
          await Promise.all([
            db.product.count({
              where: { userId: session.user.id },
            }),
            db.product.count({
              where: {
                userId: session.user.id,
                quantity: { lte: db.product.fields.reorderLevel },
              },
            }),
            db.order.count({
              where: { userId: session.user.id },
            }),
            db.order.aggregate({
              where: {
                userId: session.user.id,
                status: { not: "CANCELLED" },
              },
              _sum: {
                total: true,
              },
            }),
            db.order.findMany({
              where: { userId: session.user.id },
              orderBy: { createdAt: "desc" },
              take: 5,
              include: { customer: true },
            }),
            db.orderItem.groupBy({
              by: ["productId"],
              where: {
                order: { userId: session.user.id },
              },
              _sum: {
                quantity: true,
                subtotal: true,
              },
              orderBy: {
                _sum: {
                  quantity: "desc",
                },
              },
              take: 5,
            }),
          ])

        // Get product details for top selling products
        const productIds = topSellingProducts.map((item) => item.productId)
        const products = await db.product.findMany({
          where: {
            id: { in: productIds },
          },
        })

        const topProducts = topSellingProducts.map((item) => {
          const product = products.find((p) => p.id === item.productId)
          return {
            id: item.productId,
            name: product?.name || "Unknown Product",
            quantity: item._sum.quantity,
            revenue: item._sum.subtotal,
          }
        })

        return {
          totalProducts,
          lowStockProducts,
          totalOrders,
          totalSales: totalSales._sum.total || 0,
          recentOrders,
          topSellingProducts: topProducts,
        }
      },
      SHORT_TTL, // Use shorter TTL for dashboard stats as they change frequently
    )

    return NextResponse.json(stats)
  } catch (error) {
    console.error("Error fetching dashboard stats:", error)
    return NextResponse.json({ error: "Failed to fetch dashboard statistics" }, { status: 500 })
  }
}

