import type React from "react"
import { cn } from "@/lib/utils"

interface ResponsiveFormProps {
  children: React.ReactNode
  className?: string
}

export function ResponsiveForm({ children, className }: ResponsiveFormProps) {
  return <div className={cn("space-y-6 max-w-2xl mx-auto", className)}>{children}</div>
}

interface FormSectionProps {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
}

export function FormSection({ children, title, description, className }: FormSectionProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && <h3 className="text-lg font-medium">{title}</h3>}
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
      )}
      <div className="grid gap-4 sm:grid-cols-2">{children}</div>
    </div>
  )
}

interface FormFieldProps {
  children: React.ReactNode
  className?: string
  fullWidth?: boolean
}

export function FormField({ children, className, fullWidth = false }: FormFieldProps) {
  return <div className={cn(fullWidth ? "sm:col-span-2" : "", className)}>{children}</div>
}

