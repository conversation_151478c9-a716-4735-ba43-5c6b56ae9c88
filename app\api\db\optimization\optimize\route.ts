import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { DbOptimizationService } from "@/lib/services/db-optimization"
import { ApiError } from "@/lib/api-error"
import { AuditService } from "@/lib/services/audit-service"

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to optimize database
    if (session.user.role !== "ADMIN") {
      throw ApiError.forbidden("You do not have permission to optimize the database")
    }

    // Optimize database
    const result = await DbOptimizationService.optimizeDatabase()

    // Log audit event
    await AuditService.log({
      action: "DATABASE_OPTIMIZE",
      entityType: "Database",
      userId: session.user.id,
      ipAddress: req.headers.get("x-forwarded-for") || req.ip,
      userAgent: req.headers.get("user-agent"),
    })

    return NextResponse.json(result)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Database optimization error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

