"use client"

import React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Mic, Settings, Command, Plus, Trash2 } from "lucide-react"
import VoiceCommandListener from "@/components/voice/voice-command-listener"
import { useToast } from "@/hooks/use-toast"

export default function VoiceCommandSettings() {
  const [voiceEnabled, setVoiceEnabled] = useState(true)
  const [continuousMode, setContinuousMode] = useState(false)
  const [wakeWord, setWakeWord] = useState("hey stocksync")
  const [customCommands, setCustomCommands] = useState([
    { command: "show inventory summary", action: "Navigate to inventory dashboard" },
    { command: "check low stock items", action: "Filter products by low stock" },
  ])
  const [newCommand, setNewCommand] = useState("")
  const [newAction, setNewAction] = useState("")
  const { toast } = useToast()

  const handleAddCommand = () => {
    if (!newCommand.trim() || !newAction.trim()) {
      toast({
        title: "Error",
        description: "Both command and action are required",
        variant: "destructive",
      })
      return
    }

    setCustomCommands([...customCommands, { command: newCommand, action: newAction }])
    setNewCommand("")
    setNewAction("")

    toast({
      title: "Command Added",
      description: `Added "${newCommand}" command`,
      variant: "default",
    })
  }

  const handleRemoveCommand = (index: number) => {
    const newCommands = [...customCommands]
    newCommands.splice(index, 1)
    setCustomCommands(newCommands)

    toast({
      title: "Command Removed",
      description: "Custom command has been removed",
      variant: "default",
    })
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Voice Command Settings</h1>
      </div>

      <Tabs defaultValue="settings">
        <TabsList>
          <TabsTrigger value="settings">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="commands">
            <Command className="h-4 w-4 mr-2" />
            Custom Commands
          </TabsTrigger>
          <TabsTrigger value="test">
            <Mic className="h-4 w-4 mr-2" />
            Test Voice Commands
          </TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Voice Command Settings</CardTitle>
              <CardDescription>Configure how voice commands work in StockSync</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Enable Voice Commands</h3>
                  <p className="text-sm text-muted-foreground">Allow StockSync to listen for voice commands</p>
                </div>
                <Switch checked={voiceEnabled} onCheckedChange={setVoiceEnabled} />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Continuous Listening Mode</h3>
                  <p className="text-sm text-muted-foreground">
                    Always listen for commands without requiring manual activation
                  </p>
                </div>
                <Switch checked={continuousMode} onCheckedChange={setContinuousMode} disabled={!voiceEnabled} />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="wake-word">Wake Word or Phrase</Label>
                <Input
                  id="wake-word"
                  value={wakeWord}
                  onChange={(e) => setWakeWord(e.target.value)}
                  placeholder="hey stocksync"
                  disabled={!voiceEnabled || !continuousMode}
                />
                <p className="text-sm text-muted-foreground">
                  Only used in continuous mode to activate command processing
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="text-lg font-medium">Voice Feedback</h3>
                <div className="flex items-center space-x-2">
                  <Switch id="voice-feedback" defaultChecked disabled={!voiceEnabled} />
                  <Label htmlFor="voice-feedback">Speak responses aloud</Label>
                </div>
                <p className="text-sm text-muted-foreground">StockSync will speak responses to your commands</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="commands" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Custom Voice Commands</CardTitle>
              <CardDescription>Create custom voice commands for frequently used actions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="grid grid-cols-[1fr,1fr,auto] gap-4">
                  <div className="font-medium">Command Phrase</div>
                  <div className="font-medium">Action</div>
                  <div></div>

                  {customCommands.map((cmd, index) => (
                    <React.Fragment key={index}>
                      <div className="text-sm">{cmd.command}</div>
                      <div className="text-sm text-muted-foreground">{cmd.action}</div>
                      <Button variant="ghost" size="icon" onClick={() => handleRemoveCommand(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </React.Fragment>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Add New Command</h3>
                <div className="grid grid-cols-[1fr,1fr,auto] gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-command">Command Phrase</Label>
                    <Input
                      id="new-command"
                      value={newCommand}
                      onChange={(e) => setNewCommand(e.target.value)}
                      placeholder="e.g., show inventory report"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-action">Action</Label>
                    <Input
                      id="new-action"
                      value={newAction}
                      onChange={(e) => setNewAction(e.target.value)}
                      placeholder="e.g., Navigate to inventory report"
                    />
                  </div>
                  <div className="flex items-end">
                    <Button onClick={handleAddCommand}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="test" className="space-y-4 mt-4">
          <VoiceCommandListener
            continuousMode={continuousMode}
            commands={{
              hello: () => {
                toast({
                  title: "Hello!",
                  description: "Voice command recognized successfully",
                  variant: "default",
                })
              },
              ...customCommands.reduce(
                (acc, cmd) => {
                  acc[cmd.command] = () => {
                    toast({
                      title: "Custom Command Recognized",
                      description: `Action: ${cmd.action}`,
                      variant: "default",
                    })
                  }
                  return acc
                },
                {} as Record<string, () => void>,
              ),
            }}
          />

          <Card>
            <CardHeader>
              <CardTitle>Test Voice Commands</CardTitle>
              <CardDescription>Try out voice commands to see how they work</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>Try saying one of these commands:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>"Go to dashboard"</li>
                <li>"Go to products"</li>
                <li>"Search for [term]"</li>
                <li>"Create new product"</li>
                <li>"Hello" (test command)</li>
                {customCommands.map((cmd, index) => (
                  <li key={index}>"{cmd.command}"</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

