import type React from "react"
import { cn } from "@/lib/utils"

interface ResponsiveGridProps {
  children: React.ReactNode
  className?: string
  cols?: {
    default: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: string
}

export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = "gap-4",
}: ResponsiveGridProps) {
  // Build the grid template columns class
  const gridCols = `grid-cols-${cols.default}`
  const gridColsSm = cols.sm ? `sm:grid-cols-${cols.sm}` : ""
  const gridColsMd = cols.md ? `md:grid-cols-${cols.md}` : ""
  const gridColsLg = cols.lg ? `lg:grid-cols-${cols.lg}` : ""
  const gridColsXl = cols.xl ? `xl:grid-cols-${cols.xl}` : ""

  return (
    <div className={cn("grid", gridCols, gridColsSm, gridColsMd, gridColsLg, gridColsXl, gap, className)}>
      {children}
    </div>
  )
}

