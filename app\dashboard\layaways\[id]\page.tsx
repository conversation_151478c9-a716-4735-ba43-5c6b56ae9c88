import { notFound } from "next/navigation"
import { LayawayDetails } from "@/components/layaways/layaway-details"
import prisma from "@/lib/prisma"

interface LayawayPageProps {
  params: {
    id: string
  }
}

async function getLayaway(id: string) {
  const layaway = await prisma.layaway.findUnique({
    where: { id },
    include: {
      customer: true,
      items: {
        include: {
          product: true,
          variant: true,
        },
      },
      payments: {
        orderBy: {
          createdAt: "desc",
        },
      },
    },
  })

  if (!layaway) {
    notFound()
  }

  return layaway
}

export default async function LayawayPage({ params }: LayawayPageProps) {
  const layaway = await getLayaway(params.id)

  return (
    <div>
      <LayawayDetails layaway={layaway} />
    </div>
  )
}

