"use client"

import { useSession } from "next-auth/react"
import { useEffect } from "react"
import { useStore } from "@/lib/store"

export function useAuthSync() {
  const { data: session, status } = useSession()
  const { setAuth, clearAuth } = useStore()

  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      setAuth(true, {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        role: session.user.role,
      })
    } else if (status === "unauthenticated") {
      clearAuth()
    }
  }, [session, status, setAuth, clearAuth])

  return { session, status }
}

