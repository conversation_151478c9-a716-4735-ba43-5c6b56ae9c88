import type { StateCreator } from "zustand"
import type { StoreState } from "./index"
import { produce } from "immer"

export interface OfflineTransaction {
  id: string
  timestamp: number
  type: "order" | "inventory" | "customer"
  data: any
  synced: boolean
}

export interface OfflineSlice {
  isOffline: boolean
  setIsOffline: (status: boolean) => void
  offlineTransactions: OfflineTransaction[]
  addOfflineTransaction: (transaction: Omit<OfflineTransaction, "id" | "timestamp" | "synced">) => void
  markTransactionSynced: (id: string) => void
  clearSyncedTransactions: () => void
  lastSyncTimestamp: number
  setLastSyncTimestamp: (timestamp: number) => void
}

export const createOfflineSlice: StateCreator<StoreState, [], [], OfflineSlice> = (set) => ({
  isOffline: false,
  setIsOffline: (status) => set({ isOffline: status }),
  offlineTransactions: [],
  addOfflineTransaction: (transaction) =>
    set(
      produce((state: OfflineSlice) => {
        state.offlineTransactions.push({
          id: `offline_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          timestamp: Date.now(),
          synced: false,
          ...transaction,
        })
      }),
    ),
  markTransactionSynced: (id) =>
    set(
      produce((state: OfflineSlice) => {
        const transactionIndex = state.offlineTransactions.findIndex((transaction) => transaction.id === id)
        if (transactionIndex >= 0) {
          state.offlineTransactions[transactionIndex].synced = true
        }
      }),
    ),
  clearSyncedTransactions: () =>
    set(
      produce((state: OfflineSlice) => {
        state.offlineTransactions = state.offlineTransactions.filter((transaction) => !transaction.synced)
      }),
    ),
  lastSyncTimestamp: 0,
  setLastSyncTimestamp: (timestamp) => set({ lastSyncTimestamp: timestamp }),
})

