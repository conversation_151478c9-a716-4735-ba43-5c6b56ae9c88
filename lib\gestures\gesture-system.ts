import { NativeHaptics } from "@/lib/native/native-features"

// Types
export type GestureDirection = "left" | "right" | "up" | "down"
export type GestureType = "swipe" | "pan" | "pinch" | "rotate" | "longpress"

export interface GestureOptions {
  element: HTMLElement | null
  onStart?: (event: TouchEvent) => void
  onMove?: (event: TouchEvent, data: GestureData) => void
  onEnd?: (event: TouchEvent, data: GestureData) => void
  onSwipe?: (direction: GestureDirection, data: GestureData) => void
  threshold?: number
  velocityThreshold?: number
  longPressThreshold?: number
  enableHaptics?: boolean
}

export interface GestureData {
  startX: number
  startY: number
  currentX: number
  currentY: number
  deltaX: number
  deltaY: number
  velocityX: number
  velocityY: number
  direction: GestureDirection | null
  distance: number
  duration: number
  isLongPress: boolean
}

export class GestureSystem {
  private element: HTMLElement | null
  private options: GestureOptions
  private startX = 0
  private startY = 0
  private startTime = 0
  private isTracking = false
  private longPressTimer: NodeJS.Timeout | null = null
  private lastMoveEvent: TouchEvent | null = null

  constructor(options: GestureOptions) {
    this.element = options.element
    this.options = {
      threshold: 50,
      velocityThreshold: 0.3,
      longPressThreshold: 500,
      enableHaptics: true,
      ...options,
    }

    this.init()
  }

  private init() {
    if (!this.element) return

    this.element.addEventListener("touchstart", this.handleTouchStart)
    this.element.addEventListener("touchmove", this.handleTouchMove)
    this.element.addEventListener("touchend", this.handleTouchEnd)
    this.element.addEventListener("touchcancel", this.handleTouchEnd)
  }

  public destroy() {
    if (!this.element) return

    this.element.removeEventListener("touchstart", this.handleTouchStart)
    this.element.removeEventListener("touchmove", this.handleTouchMove)
    this.element.removeEventListener("touchend", this.handleTouchEnd)
    this.element.removeEventListener("touchcancel", this.handleTouchEnd)

    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
      this.longPressTimer = null
    }
  }

  private handleTouchuchStart = (event: TouchEvent) => {
    if (event.touches.length !== 1) return

    const touch = event.touches[0]
    this.startX = touch.clientX
    this.startY = touch.clientY
    this.startTime = Date.now()
    this.isTracking = true

    // Start long press timer
    if (this.options.longPressThreshold) {
      this.longPressTimer = setTimeout(() => {
        const data = this.getGestureData(event)
        data.isLongPress = true

        if (this.options.enableHaptics) {
          NativeHaptics.impact("heavy")
        }

        if (this.options.onEnd) {
          this.options.onEnd(event, data)
        }

        this.isTracking = false
      }, this.options.longPressThreshold)
    }

    if (this.options.onStart) {
      this.options.onStart(event)
    }
  }

  private handleTouchMove = (event: TouchEvent) => {
    if (!this.isTracking || event.touches.length !== 1) return

    this.lastMoveEvent = event
    const data = this.getGestureData(event)

    // If moved beyond a small threshold, cancel long press
    if (this.longPressTimer && (Math.abs(data.deltaX) > 10 || Math.abs(data.deltaY) > 10)) {
      clearTimeout(this.longPressTimer)
      this.longPressTimer = null
    }

    if (this.options.onMove) {
      this.options.onMove(event, data)
    }
  }

  private handleTouchEnd = (event: TouchEvent) => {
    if (!this.isTracking) return

    // Clear long press timer
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
      this.longPressTimer = null
    }

    // Use the last move event if available, otherwise use the end event
    const finalEvent = this.lastMoveEvent || event
    const data = this.getGestureData(finalEvent)

    // Detect swipe
    if (
      this.options.onSwipe &&
      data.distance > (this.options.threshold || 50) &&
      data.velocityX > (this.options.velocityThreshold || 0.3)
    ) {
      if (this.options.enableHaptics) {
        NativeHaptics.impact("medium")
      }

      this.options.onSwipe(data.direction!, data)
    }

    if (this.options.onEnd) {
      this.options.onEnd(event, data)
    }

    this.isTracking = false
    this.lastMoveEvent = null
  }

  private getGestureData(event: TouchEvent): GestureData {
    const touch = event.touches[0] || event.changedTouches[0]
    const currentX = touch.clientX
    const currentY = touch.clientY
    const deltaX = currentX - this.startX
    const deltaY = currentY - this.startY
    const duration = Date.now() - this.startTime

    // Calculate velocity (pixels per millisecond)
    const velocityX = Math.abs(deltaX) / duration
    const velocityY = Math.abs(deltaY) / duration

    // Determine direction
    let direction: GestureDirection | null = null

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      direction = deltaX > 0 ? "right" : "left"
    } else {
      direction = deltaY > 0 ? "down" : "up"
    }

    // Calculate distance
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    return {
      startX: this.startX,
      startY: this.startY,
      currentX,
      currentY,
      deltaX,
      deltaY,
      velocityX,
      velocityY,
      direction,
      distance,
      duration,
      isLongPress: false,
    }
  }
}

