"use client"

import { useState } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Search } from "lucide-react"

interface Field {
  id: string
  name: string
  label: string
  type: string
  category?: string
}

interface ReportFieldSelectorProps {
  availableFields: Field[]
  selectedFields: string[]
  onFieldsChange: (fields: string[]) => void
}

export function ReportFieldSelector({
  availableFields = [],
  selectedFields = [],
  onFieldsChange,
}: ReportFieldSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // Group fields by category
  const fieldsByCategory: Record<string, Field[]> = {}
  availableFields.forEach((field) => {
    const category = field.category || "General"
    if (!fieldsByCategory[category]) {
      fieldsByCategory[category] = []
    }
    fieldsByCategory[category].push(field)
  })

  // Filter fields based on search query
  const filteredCategories: Record<string, Field[]> = {}
  if (searchQuery.trim()) {
    const query = searchQuery.toLowerCase()
    Object.entries(fieldsByCategory).forEach(([category, fields]) => {
      const filteredFields = fields.filter(
        (field) => field.label.toLowerCase().includes(query) || field.name.toLowerCase().includes(query),
      )
      if (filteredFields.length > 0) {
        filteredCategories[category] = filteredFields
      }
    })
  } else {
    Object.assign(filteredCategories, fieldsByCategory)
  }

  const handleFieldToggle = (fieldId: string) => {
    if (selectedFields.includes(fieldId)) {
      onFieldsChange(selectedFields.filter((id) => id !== fieldId))
    } else {
      onFieldsChange([...selectedFields, fieldId])
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Badge variant="outline" className="px-2 py-1">
          {selectedFields.length} fields selected
        </Badge>
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search fields..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <ScrollArea className="h-[400px] rounded-md border">
        <div className="p-4 space-y-6">
          {Object.keys(filteredCategories).length > 0 ? (
            Object.entries(filteredCategories).map(([category, fields]) => (
              <div key={category} className="space-y-2">
                <h3 className="font-medium text-sm text-muted-foreground">{category}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                  {fields.map((field) => (
                    <div key={field.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`field-${field.id}`}
                        checked={selectedFields.includes(field.id)}
                        onCheckedChange={() => handleFieldToggle(field.id)}
                      />
                      <Label htmlFor={`field-${field.id}`} className="text-sm cursor-pointer">
                        {field.label}
                      </Label>
                    </div>
                  ))}
                </div>
                <Separator className="mt-4" />
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">No fields found matching your search</div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

