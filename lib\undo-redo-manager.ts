export interface UndoRedoState<T> {
  past: T[]
  present: T
  future: T[]
}

export interface UndoRedoActions<T> {
  canUndo: boolean
  canRedo: boolean
  undo: () => void
  redo: () => void
  reset: (newPresent: T) => void
  update: (newPresent: T) => void
}

export function createUndoRedoManager<T>(initialState: T, maxHistorySize = 20): [UndoRedoState<T>, UndoRedoActions<T>] {
  let state: UndoRedoState<T> = {
    past: [],
    present: initialState,
    future: [],
  }

  const actions: UndoRedoActions<T> = {
    get canUndo() {
      return state.past.length > 0
    },
    get canRedo() {
      return state.future.length > 0
    },
    undo: () => {
      if (state.past.length === 0) return

      const previous = state.past[state.past.length - 1]
      const newPast = state.past.slice(0, state.past.length - 1)

      state = {
        past: newPast,
        present: previous,
        future: [state.present, ...state.future],
      }
    },
    redo: () => {
      if (state.future.length === 0) return

      const next = state.future[0]
      const newFuture = state.future.slice(1)

      state = {
        past: [...state.past, state.present],
        present: next,
        future: newFuture,
      }
    },
    reset: (newPresent: T) => {
      state = {
        past: [],
        present: newPresent,
        future: [],
      }
    },
    update: (newPresent: T) => {
      if (JSON.stringify(newPresent) === JSON.stringify(state.present)) {
        return
      }

      state = {
        past: [...state.past, state.present].slice(-maxHistorySize),
        present: newPresent,
        future: [],
      }
    },
  }

  return [state, actions]
}

