import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RecurringOrdersList } from "@/components/recurring-orders/recurring-orders-list"
import Link from "next/link"
import { Plus } from "lucide-react"
import prisma from "@/lib/prisma"

async function getRecurringOrders() {
  const recurringOrders = await prisma.recurringOrder.findMany({
    include: {
      customer: true,
      items: true,
      orderHistory: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  })

  return recurringOrders
}

export default async function RecurringOrdersPage() {
  const recurringOrders = await getRecurringOrders()

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Recurring Orders</h2>
        <Button asChild>
          <Link href="/dashboard/recurring-orders/new">
            <Plus className="mr-2 h-4 w-4" /> New Recurring Order
          </Link>
        </Button>
      </div>

      <Suspense fallback={<div>Loading recurring orders...</div>}>
        <RecurringOrdersList recurringOrders={recurringOrders} />
      </Suspense>
    </div>
  )
}

