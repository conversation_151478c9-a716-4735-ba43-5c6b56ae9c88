"use client"

import type React from "react"

import { useState, useCallback } from "react"
import { z } from "zod"
import { useToast } from "@/components/ui/use-toast"

interface UseZodFormOptions<T extends z.ZodType> {
  schema: T
  defaultValues?: z.infer<T>
  onSubmit?: (values: z.infer<T>) => Promise<void> | void
  onError?: (errors: Record<string, string>) => void
}

export function useZodForm<T extends z.ZodType>({ schema, defaultValues, onSubmit, onError }: UseZodFormOptions<T>) {
  const [values, setValues] = useState<Partial<z.infer<T>>>(defaultValues || {})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const setValue = useCallback(
    <K extends keyof z.infer<T>>(key: K, value: z.infer<T>[K]) => {
      setValues((prev) => ({ ...prev, [key]: value }))

      // Clear error for this field when value changes
      if (errors[key as string]) {
        setErrors((prev) => {
          const newErrors = { ...prev }
          delete newErrors[key as string]
          return newErrors
        })
      }
    },
    [errors],
  )

  const validateField = useCallback(
    <K extends keyof z.infer<T>>(key: K, value: z.infer<T>[K]) => {
      try {
        // Create a partial schema for just this field
        const partialSchema = z.object({ [key]: schema.shape[key] }) as z.ZodType
        partialSchema.parse({ [key]: value })

        // Clear error if validation passes
        setErrors((prev) => {
          const newErrors = { ...prev }
          delete newErrors[key as string]
          return newErrors
        })

        return true
      } catch (error) {
        if (error instanceof z.ZodError) {
          const fieldError = error.errors[0]?.message || "Invalid value"
          setErrors((prev) => ({
            ...prev,
            [key]: fieldError,
          }))
        }
        return false
      }
    },
    [schema.shape],
  )

  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault()
      }

      setIsSubmitting(true)

      try {
        // Validate all fields
        const validatedData = schema.parse(values)

        // Clear all errors
        setErrors({})

        // Call onSubmit callback
        if (onSubmit) {
          await onSubmit(validatedData)
        }

        return validatedData
      } catch (error) {
        if (error instanceof z.ZodError) {
          // Format errors
          const formattedErrors: Record<string, string> = {}

          error.errors.forEach((err) => {
            if (err.path.length > 0) {
              formattedErrors[err.path.join(".")] = err.message
            }
          })

          setErrors(formattedErrors)

          // Call onError callback
          if (onError) {
            onError(formattedErrors)
          } else {
            toast({
              title: "Validation Error",
              description: "Please check the form for errors",
              variant: "destructive",
            })
          }
        } else {
          console.error("Form validation error:", error)
          toast({
            title: "Error",
            description: "An unexpected error occurred",
            variant: "destructive",
          })
        }

        return null
      } finally {
        setIsSubmitting(false)
      }
    },
    [schema, values, onSubmit, onError, toast],
  )

  const reset = useCallback(
    (newValues?: Partial<z.infer<T>>) => {
      setValues(newValues || defaultValues || {})
      setErrors({})
    },
    [defaultValues],
  )

  return {
    values,
    errors,
    isSubmitting,
    setValue,
    validateField,
    handleSubmit,
    reset,
  }
}

