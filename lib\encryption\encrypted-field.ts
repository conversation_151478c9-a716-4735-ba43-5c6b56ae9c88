import { EncryptionService } from "./encryption-service"
import { SearchableEncryption } from "./searchable-encryption"

export interface EncryptedField {
  encryptedData: string
  iv: string
  authTag: string
  keyId: string
  searchIndex?: string
}

export class Encrypted<PERSON>ieldHelper {
  /**
   * Encrypt a field
   */
  static async encrypt(value: string, makeSearchable = false): Promise<EncryptedField> {
    if (makeSearchable) {
      return SearchableEncryption.encryptWithIndex(value)
    } else {
      const { encryptedData, iv, authTag, keyId } = await EncryptionService.encrypt(value)

      return {
        encryptedData,
        iv,
        authTag,
        keyId,
      }
    }
  }

  /**
   * Decrypt a field
   */
  static async decrypt(field: EncryptedField): Promise<string> {
    return EncryptionService.decrypt(field.encryptedData, field.iv, field.authTag, field.keyId)
  }

  /**
   * Create a search token for finding encrypted values
   */
  static createSearchToken(searchTerm: string): string {
    return SearchableEncryption.createSearchToken(searchTerm)
  }
}

