"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, ArrowLeft, CheckCircle, XCircle, AlertCircle, Clock, RefreshCw } from "lucide-react"
import { formatDate, formatCurrency } from "@/lib/utils"

interface ReturnDetailsProps {
  returnData: any
  isAdmin?: boolean
}

export function ReturnDetails({ returnData, isAdmin = false }: ReturnDetailsProps) {
  const router = useRouter()
  const [isUpdating, setIsUpdating] = useState(false)
  const [status, setStatus] = useState(returnData.status)
  const [notes, setNotes] = useState("")
  const [refundAmount, setRefundAmount] = useState<number | undefined>(returnData.refundAmount || undefined)
  const [refundMethod, setRefundMethod] = useState<string | undefined>(returnData.refundMethod || undefined)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "REQUESTED":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            Requested
          </Badge>
        )
      case "APPROVED":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Approved
          </Badge>
        )
      case "RECEIVED":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <RefreshCw className="mr-1 h-3 w-3" />
            Received
          </Badge>
        )
      case "INSPECTED":
        return (
          <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Inspected
          </Badge>
        )
      case "COMPLETED":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        )
      case "REJECTED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        )
      case "PARTIALLY_REFUNDED":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
            <RefreshCw className="mr-1 h-3 w-3" />
            Partially Refunded
          </Badge>
        )
      case "REFUNDED":
        return (
          <Badge className="bg-emerald-100 text-emerald-800 border-emerald-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Refunded
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  const updateReturnStatus = async () => {
    try {
      setIsUpdating(true)

      const response = await fetch(`/api/returns/${returnData.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
          notes,
          refundAmount: refundAmount !== undefined ? Number.parseFloat(refundAmount.toString()) : undefined,
          refundMethod,
          refundDate: status === "REFUNDED" || status === "PARTIALLY_REFUNDED" ? new Date() : undefined,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update return")
      }

      toast({
        title: "Return Updated",
        description: `Return #${returnData.returnNumber} has been updated successfully.`,
      })

      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update return",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  // Calculate total refund amount
  const totalRefundAmount = returnData.items.reduce((total: number, item: any) => {
    return total + (item.refundAmount || 0)
  }, 0)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => router.back()} className="flex items-center">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Returns
        </Button>
        {getStatusBadge(returnData.status)}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between">
            <span>Return #{returnData.returnNumber}</span>
          </CardTitle>
          <CardDescription>
            Created on {formatDate(returnData.createdAt)} by {returnData.createdBy.name || returnData.createdBy.email}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Return Details</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <span>{getStatusBadge(returnData.status)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Order:</span>
                  <span className="font-medium">#{returnData.order.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Customer:</span>
                  <span>
                    {returnData.customer.firstName} {returnData.customer.lastName}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Reason:</span>
                  <span>{returnData.reason}</span>
                </div>
                {returnData.refundAmount && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Refund Amount:</span>
                    <span className="font-medium">{formatCurrency(returnData.refundAmount)}</span>
                  </div>
                )}
                {returnData.refundMethod && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Refund Method:</span>
                    <span>{returnData.refundMethod.replace("_", " ")}</span>
                  </div>
                )}
                {returnData.refundDate && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Refund Date:</span>
                    <span>{formatDate(returnData.refundDate)}</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Notes</h3>
              <p className="text-muted-foreground">{returnData.notes || "No notes provided"}</p>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-medium mb-4">Return Items</h3>
            <div className="space-y-4">
              {returnData.items.map((item: any) => (
                <div key={item.id} className="border rounded-md p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{item.product.name}</h4>
                      {item.variant && <p className="text-sm text-muted-foreground">Variant: {item.variant.sku}</p>}
                      <div className="mt-2 space-y-1">
                        <p className="text-sm">
                          <span className="text-muted-foreground">Quantity:</span> {item.quantity}
                        </p>
                        <p className="text-sm">
                          <span className="text-muted-foreground">Reason:</span> {item.reason}
                        </p>
                        <p className="text-sm">
                          <span className="text-muted-foreground">Condition:</span> {item.condition}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatCurrency(item.refundAmount || item.orderItem.unitPrice * item.quantity)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(item.orderItem.unitPrice)} per unit
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-medium mb-4">Status History</h3>
            <div className="space-y-2">
              {returnData.statusHistory.map((history: any) => (
                <div key={history.id} className="flex items-center justify-between py-2">
                  <div className="flex items-center">
                    {getStatusBadge(history.status)}
                    {history.notes && <span className="ml-4 text-sm text-muted-foreground">{history.notes}</span>}
                  </div>
                  <span className="text-sm text-muted-foreground">{formatDate(history.createdAt)}</span>
                </div>
              ))}
            </div>
          </div>

          {isAdmin && (
            <>
              <Separator />

              <div>
                <h3 className="text-lg font-medium mb-4">Update Return</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={status} onValueChange={setStatus}>
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="REQUESTED">Requested</SelectItem>
                        <SelectItem value="APPROVED">Approved</SelectItem>
                        <SelectItem value="RECEIVED">Received</SelectItem>
                        <SelectItem value="INSPECTED">Inspected</SelectItem>
                        <SelectItem value="COMPLETED">Completed</SelectItem>
                        <SelectItem value="REJECTED">Rejected</SelectItem>
                        <SelectItem value="PARTIALLY_REFUNDED">Partially Refunded</SelectItem>
                        <SelectItem value="REFUNDED">Refunded</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {(status === "REFUNDED" || status === "PARTIALLY_REFUNDED") && (
                    <>
                      <div>
                        <Label htmlFor="refundMethod">Refund Method</Label>
                        <Select value={refundMethod} onValueChange={setRefundMethod}>
                          <SelectTrigger id="refundMethod">
                            <SelectValue placeholder="Select refund method" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ORIGINAL_PAYMENT">Original Payment</SelectItem>
                            <SelectItem value="STORE_CREDIT">Store Credit</SelectItem>
                            <SelectItem value="EXCHANGE">Exchange</SelectItem>
                            <SelectItem value="GIFT_CARD">Gift Card</SelectItem>
                            <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                            <SelectItem value="CHECK">Check</SelectItem>
                            <SelectItem value="OTHER">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="refundAmount">Refund Amount</Label>
                        <Input
                          id="refundAmount"
                          type="number"
                          min="0"
                          step="0.01"
                          value={refundAmount}
                          onChange={(e) => setRefundAmount(Number.parseFloat(e.target.value))}
                          placeholder={totalRefundAmount.toString()}
                        />
                      </div>
                    </>
                  )}

                  <div className="md:col-span-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Add notes about this status update"
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
        {isAdmin && (
          <CardFooter>
            <Button
              onClick={updateReturnStatus}
              disabled={isUpdating || status === returnData.status}
              className="ml-auto"
            >
              {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Return
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}

