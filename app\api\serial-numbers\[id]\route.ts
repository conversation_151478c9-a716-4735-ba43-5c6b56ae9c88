import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { ApiError } from "@/lib/api-error"
import { serialNumberSchema } from "@/lib/validations/serial-number-schema"

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const serialNumber = await prisma.serialNumber.findUnique({
      where: { id: params.id },
      include: {
        product: true,
        variant: true,
        order: true,
      },
    })

    if (!serialNumber) {
      throw ApiError.notFound("Serial number not found")
    }

    return NextResponse.json(serialNumber)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Serial number fetch error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const body = await req.json()

    // Validate request body
    const validatedData = serialNumberSchema.parse(body)

    // Check if serial number exists
    const existingSerialNumber = await prisma.serialNumber.findUnique({
      where: { id: params.id },
    })

    if (!existingSerialNumber) {
      throw ApiError.notFound("Serial number not found")
    }

    // Check if the new serial number already exists for this product (if changed)
    if (validatedData.serialNumber !== existingSerialNumber.serialNumber) {
      const duplicateSerialNumber = await prisma.serialNumber.findFirst({
        where: {
          productId: validatedData.productId,
          serialNumber: validatedData.serialNumber,
          id: { not: params.id },
        },
      })

      if (duplicateSerialNumber) {
        throw ApiError.conflict("This serial number already exists for this product")
      }
    }

    // Update serial number
    const serialNumber = await prisma.serialNumber.update({
      where: { id: params.id },
      data: {
        serialNumber: validatedData.serialNumber,
        status: validatedData.status,
        notes: validatedData.notes || null,
        purchaseDate: validatedData.purchaseDate ? new Date(validatedData.purchaseDate) : null,
        productId: validatedData.productId,
        variantId: validatedData.variantId || null,
      },
    })

    return NextResponse.json({
      success: true,
      serialNumber,
    })
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Serial number update error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if serial number exists
    const existingSerialNumber = await prisma.serialNumber.findUnique({
      where: { id: params.id },
    })

    if (!existingSerialNumber) {
      throw ApiError.notFound("Serial number not found")
    }

    // Delete serial number
    await prisma.serialNumber.delete({
      where: { id: params.id },
    })

    return NextResponse.json({
      success: true,
    })
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Serial number deletion error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

