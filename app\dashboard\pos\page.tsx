"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Separator } from "@/components/ui/separator"
import { Search, ShoppingCart, Trash2, Plus, Minus, CreditCard, Printer, Save, WifiOff } from "lucide-react"
import { usePOSStore, useOfflineStore } from "@/lib/store"
import { useSocket } from "@/components/socket-provider"
import { useToast } from "@/hooks/use-toast"

export default function POSPage() {
  const { toast } = useToast()
  const { status, isOnline, emit } = useSocket()
  const [searchQuery, setSearchQuery] = useState("")

  // Get cart from Zustand store
  const { cart, addToCart, removeFromCart, updateQuantity, clearCart, holdSale, resumeSale, heldSales } = usePOSStore()

  // Get offline state
  const { pendingTransactions, addPendingTransaction, removePendingTransaction } = useOfflineStore()

  const products = [
    {
      id: "PRD001",
      name: "Wireless Headphones",
      price: 79.99,
      category: "Electronics",
    },
    {
      id: "PRD002",
      name: "Organic Coffee Beans",
      price: 12.99,
      category: "Food & Beverage",
    },
    {
      id: "PRD003",
      name: "Cotton T-Shirt (M)",
      price: 24.99,
      category: "Apparel",
    },
    {
      id: "PRD004",
      name: "Smartphone Charger",
      price: 19.99,
      category: "Electronics",
    },
    {
      id: "PRD005",
      name: "Notebook Set",
      price: 9.99,
      category: "Stationery",
    },
    {
      id: "PRD006",
      name: "Fitness Tracker",
      price: 49.99,
      category: "Electronics",
    },
    {
      id: "PRD007",
      name: "Water Bottle",
      price: 14.99,
      category: "Sports",
    },
    {
      id: "PRD008",
      name: "Desk Lamp",
      price: 34.99,
      category: "Home",
    },
    {
      id: "PRD009",
      name: "Bluetooth Speaker",
      price: 59.99,
      category: "Electronics",
    },
    {
      id: "PRD010",
      name: "Yoga Mat",
      price: 29.99,
      category: "Sports",
    },
  ]

  const categories = ["All", "Electronics", "Food & Beverage", "Apparel", "Stationery", "Sports", "Home"]

  const filteredProducts = searchQuery
    ? products.filter(
        (product) =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : products

  const subtotal = cart.reduce((sum, item) => sum + item.price * item.quantity, 0)
  const tax = subtotal * 0.08 // 8% tax
  const total = subtotal + tax

  // Process payment
  const handlePayment = () => {
    if (cart.length === 0) return

    const order = {
      id: `ORD-${Math.floor(Math.random() * 1000)}-${new Date().getFullYear()}`,
      orderNumber: `${Math.floor(Math.random() * 10000)}`,
      items: cart,
      subtotal,
      tax,
      total,
      status: "Completed",
      payment: "Credit Card",
      timestamp: Date.now(),
    }

    if (isOnline && status === "connected") {
      // If online, emit the order created event
      emit("order_created", {
        id: order.id,
        timestamp: Date.now(),
        data: order,
      })

      // Also emit inventory updates for each item
      cart.forEach((item) => {
        emit("inventory_update", {
          id: item.id,
          timestamp: Date.now(),
          data: {
            id: item.id,
            name: item.name,
            stockQuantity: -item.quantity, // Negative to indicate reduction
            action: "decrement",
          },
        })
      })

      toast({
        title: "Order Completed",
        description: `Order #${order.orderNumber} has been processed successfully.`,
      })
    } else {
      // If offline, store the transaction for later sync
      addPendingTransaction({
        type: "order",
        data: order,
        timestamp: Date.now(),
      })

      toast({
        title: "Order Saved Offline",
        description: "The order will be synchronized when you're back online.",
        variant: "warning",
      })
    }

    // Clear the cart
    clearCart()
  }

  // Hold the current sale
  const handleHoldSale = () => {
    if (cart.length === 0) return

    const saleName = `Sale ${new Date().toLocaleTimeString()}`
    holdSale(saleName)

    toast({
      title: "Sale On Hold",
      description: `The current sale has been saved as "${saleName}".`,
    })
  }

  // Resume a held sale
  const handleResumeSale = (id: string) => {
    resumeSale(id)
  }

  // Try to sync pending transactions when coming back online
  useEffect(() => {
    if (isOnline && status === "connected" && pendingTransactions.length > 0) {
      toast({
        title: "Syncing Offline Transactions",
        description: `Syncing ${pendingTransactions.length} pending transactions...`,
      })

      // Process each pending transaction
      pendingTransactions.forEach((transaction) => {
        if (transaction.type === "order") {
          emit("order_created", {
            id: transaction.data.id,
            timestamp: Date.now(),
            data: transaction.data,
          })

          // Also emit inventory updates for each item
          transaction.data.items.forEach((item: any) => {
            emit("inventory_update", {
              id: item.id,
              timestamp: Date.now(),
              data: {
                id: item.id,
                name: item.name,
                stockQuantity: -item.quantity,
                action: "decrement",
              },
            })
          })

          // Remove the transaction after processing
          removePendingTransaction(transaction.data.id)
        }
      })

      toast({
        title: "Sync Complete",
        description: "All offline transactions have been synchronized.",
      })
    }
  }, [isOnline, status, pendingTransactions, emit, toast, removePendingTransaction])

  return (
    <div className="flex h-[calc(100vh-4rem)] gap-4">
      <div className="flex flex-1 flex-col">
        <div className="mb-4 flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search products..."
              className="w-full pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline">Scan Barcode</Button>
          {!isOnline && (
            <Button variant="destructive" size="icon" className="h-10 w-10">
              <WifiOff className="h-4 w-4" />
            </Button>
          )}
        </div>

        <Tabs defaultValue="All" className="flex-1">
          <TabsList className="grid w-full grid-cols-7">
            {categories.map((category) => (
              <TabsTrigger key={category} value={category}>
                {category}
              </TabsTrigger>
            ))}
          </TabsList>
          {categories.map((category) => (
            <TabsContent key={category} value={category} className="flex-1 overflow-auto p-1">
              <div className="grid grid-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
                {filteredProducts
                  .filter((product) => category === "All" || product.category === category)
                  .map((product) => (
                    <Card
                      key={product.id}
                      className="cursor-pointer transition-all hover:bg-accent"
                      onClick={() => addToCart(product)}
                    >
                      <CardHeader className="p-3 pb-0">
                        <CardTitle className="line-clamp-1 text-sm">{product.name}</CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-1">
                        <p className="text-xs text-muted-foreground">{product.category}</p>
                      </CardContent>
                      <CardFooter className="p-3 pt-0">
                        <p className="font-bold">${product.price.toFixed(2)}</p>
                      </CardFooter>
                    </Card>
                  ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>

      <div className="w-96 flex-shrink-0">
        <Card className="h-full flex flex-col">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <ShoppingCart className="mr-2 h-5 w-5" />
              Current Sale
              {!isOnline && (
                <span className="ml-auto text-xs text-red-500 flex items-center">
                  <WifiOff className="mr-1 h-3 w-3" /> Offline Mode
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 overflow-auto p-3">
            {cart.length === 0 ? (
              <div className="flex h-full flex-col items-center justify-center text-center text-muted-foreground">
                <ShoppingCart className="mb-2 h-8 w-8" />
                <p>Your cart is empty</p>
                <p className="text-sm">Add products to get started</p>

                {heldSales.length > 0 && (
                  <div className="mt-8 w-full">
                    <h3 className="mb-2 font-medium">Held Sales</h3>
                    <div className="space-y-2">
                      {heldSales.map((sale) => (
                        <Button
                          key={sale.id}
                          variant="outline"
                          className="w-full justify-between"
                          onClick={() => handleResumeSale(sale.id)}
                        >
                          <span>{sale.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(sale.timestamp).toLocaleTimeString()}
                          </span>
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead className="text-right">Qty</TableHead>
                    <TableHead className="text-right">Price</TableHead>
                    <TableHead className="w-8"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cart.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">${(item.price * item.quantity).toFixed(2)}</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => removeFromCart(item.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
          <Separator />
          <CardFooter className="flex flex-col p-4">
            <div className="mb-4 w-full space-y-2">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax (8%)</span>
                <span>${tax.toFixed(2)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>
            <div className="grid w-full grid-cols-3 gap-2">
              <Button variant="outline" size="sm" onClick={handleHoldSale} disabled={cart.length === 0}>
                <Save className="mr-1 h-4 w-4" />
                Hold
              </Button>
              <Button variant="outline" size="sm" disabled={cart.length === 0}>
                <Printer className="mr-1 h-4 w-4" />
                Receipt
              </Button>
              <Button size="sm" onClick={handlePayment} disabled={cart.length === 0}>
                <CreditCard className="mr-1 h-4 w-4" />
                Pay
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}

