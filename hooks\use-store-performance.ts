"use client"

import { useEffect, useRef } from "react"
import { useStore } from "@/lib/store"

export function useStorePerformance(componentName: string, selector: (state: any) => any) {
  const renderCount = useRef(0)
  const previousState = useRef<any>()

  const state = useStore(selector)

  useEffect(() => {
    // Only enable in development
    if (process.env.NODE_ENV !== "development") {
      return
    }

    renderCount.current += 1

    const changes = Object.entries(state).filter(([key, value]) => {
      return previousState.current && previousState.current[key] !== value
    })

    if (changes.length > 0) {
      console.group(`[Store Performance] ${componentName} re-rendered (${renderCount.current} times)`)
      console.log("Changed state:", changes)
      console.groupEnd()
    }

    previousState.current = state
  }, [state])

  return state
}

