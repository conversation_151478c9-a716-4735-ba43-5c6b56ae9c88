"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { CuboidIcon as Cube, Smartphone, AlertCircle } from "lucide-react"
import { arVisualization, type ProductModel } from "@/lib/ar/ar-visualization"
import { useToast } from "@/hooks/use-toast"

interface ARProductViewerProps {
  product: {
    id: string
    name: string
    modelUrl?: string
  }
}

export default function ARProductViewer({ product }: ARProductViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isSupported, setIsSupported] = useState<boolean | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isARActive, setIsARActive] = useState(false)
  const [isModelLoaded, setIsModelLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Check if WebXR is supported
  useEffect(() => {
    const checkARSupport = async () => {
      try {
        if (!("xr" in navigator)) {
          setIsSupported(false)
          return
        }

        const isSupported = await (navigator as any).xr.isSessionSupported("immersive-ar")
        setIsSupported(isSupported)
      } catch (error) {
        console.error("Error checking AR support:", error)
        setIsSupported(false)
      }
    }

    checkARSupport()
  }, [])

  // Initialize AR visualization when the component mounts
  useEffect(() => {
    if (containerRef.current && isSupported && !isInitialized) {
      try {
        const success = arVisualization.initialize({
          container: containerRef.current,
          onARStarted: () => {
            setIsARActive(true)
            toast({
              title: "AR Mode Active",
              description: "Tap on a surface to place the product",
              variant: "default",
            })
          },
          onAREnded: () => {
            setIsARActive(false)
          },
          onError: (error) => {
            setError(error.message)
            toast({
              title: "AR Error",
              description: error.message,
              variant: "destructive",
            })
          },
        })

        setIsInitialized(success)

        if (!success) {
          setError("Failed to initialize AR visualization")
        }
      } catch (error) {
        console.error("Error initializing AR:", error)
        setError(error instanceof Error ? error.message : "Unknown error")
      }
    }

    return () => {
      if (isInitialized) {
        arVisualization.dispose()
      }
    }
  }, [containerRef, isSupported, isInitialized, toast])

  // Load the product model when AR is initialized
  useEffect(() => {
    const loadModel = async () => {
      if (isInitialized && product.modelUrl) {
        try {
          const productModel: ProductModel = {
            id: product.id,
            name: product.name,
            modelUrl: product.modelUrl,
            scale: 1.0,
            position: { x: 0, y: 0, z: -1 },
            rotation: { x: 0, y: 0, z: 0 },
          }

          const success = await arVisualization.loadModel(productModel)
          setIsModelLoaded(success)

          if (!success) {
            setError("Failed to load 3D model")
          }
        } catch (error) {
          console.error("Error loading model:", error)
          setError(error instanceof Error ? error.message : "Unknown error")
        }
      }
    }

    loadModel()
  }, [isInitialized, product])

  if (isSupported === null) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isSupported === false) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center gap-4 py-8">
            <AlertCircle className="h-12 w-12 text-muted-foreground" />
            <div className="text-center">
              <h3 className="text-lg font-medium">AR Not Supported</h3>
              <p className="text-sm text-muted-foreground">
                Your device or browser does not support Augmented Reality.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!product.modelUrl) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center gap-4 py-8">
            <Cube className="h-12 w-12 text-muted-foreground" />
            <div className="text-center">
              <h3 className="text-lg font-medium">No 3D Model Available</h3>
              <p className="text-sm text-muted-foreground">
                This product does not have a 3D model for AR visualization.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">View in Augmented Reality</h3>
            <Smartphone className="h-6 w-6 text-primary" />
          </div>

          {error && (
            <div className="rounded-md bg-destructive/10 p-3 text-sm text-destructive">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            </div>
          )}

          <div ref={containerRef} className="relative h-[300px] w-full rounded-md bg-muted">
            {!isInitialized && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              </div>
            )}
          </div>

          <div className="text-center text-sm text-muted-foreground">
            {isARActive
              ? "Tap on a surface to place the product"
              : "Click the AR button to view this product in your space"}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

