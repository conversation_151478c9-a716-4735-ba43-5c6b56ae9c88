"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>oot<PERSON>, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import { createDataSubjectRequest } from "@/app/actions/data-protection"
import { RequestType, RequestStatus, type DataSubjectRequest } from "@/lib/privacy/data-subject-request"
import { toast } from "@/components/ui/use-toast"

interface DataDeletionRequestProps {
  userId: string
  existingRequests: DataSubjectRequest[]
}

export function DataDeletionRequest({ userId, existingRequests }: DataDeletionRequestProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [reason, setReason] = useState("")

  // Check if there's a pending deletion request
  const pendingDeletionRequest = existingRequests.find(
    (req) =>
      req.requestType === RequestType.ERASURE &&
      (req.status === RequestStatus.PENDING || req.status === RequestStatus.IN_PROGRESS),
  )

  const handleSubmit = async () => {
    if (!confirm("Are you sure you want to request account deletion? This action cannot be undone once processed.")) {
      return
    }

    setIsSubmitting(true)
    try {
      await createDataSubjectRequest(RequestType.ERASURE, reason)

      toast({
        title: "Request Submitted",
        description: "Your account deletion request has been submitted and will be processed within 30 days.",
      })

      // Reset form
      setReason("")

      // Refresh the page to show the pending request
      window.location.reload()
    } catch (error) {
      console.error("Error submitting deletion request:", error)
      toast({
        title: "Error",
        description: "Failed to submit account deletion request. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Delete Your Account</CardTitle>
        <CardDescription>Request permanent deletion of your account and associated data</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {pendingDeletionRequest ? (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Deletion Request Pending</AlertTitle>
            <AlertDescription>
              Your account deletion request is currently being processed. This may take up to 30 days to complete.
            </AlertDescription>
          </Alert>
        ) : (
          <>
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Warning: This action is irreversible</AlertTitle>
              <AlertDescription>
                Deleting your account will permanently remove all your data from our systems. This action cannot be
                undone once processed.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <label htmlFor="deletionReason" className="text-sm font-medium">
                Reason for deletion (optional)
              </label>
              <Textarea
                id="deletionReason"
                placeholder="Please let us know why you're deleting your account..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={3}
              />
            </div>
          </>
        )}
      </CardContent>
      <CardFooter>
        <Button
          variant="destructive"
          onClick={handleSubmit}
          disabled={isSubmitting || !!pendingDeletionRequest}
          className="w-full"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting Request...
            </>
          ) : pendingDeletionRequest ? (
            "Request Pending"
          ) : (
            <>
              <Trash2 className="mr-2 h-4 w-4" />
              Request Account Deletion
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

