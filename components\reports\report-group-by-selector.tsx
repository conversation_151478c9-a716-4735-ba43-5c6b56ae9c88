"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Trash2, Group } from "lucide-react"

interface Field {
  id: string
  name: string
  label: string
  type: string
  category?: string
}

interface ReportGroupByProps {
  availableFields: Field[]
  selectedFields: string[]
  onFieldsChange: (fields: string[]) => void
}

export function ReportGroupBySelector({
  availableFields = [],
  selectedFields = [],
  onFieldsChange,
}: ReportGroupByProps) {
  // Filter available fields to only include groupable types
  // Typically text, date, boolean, and categorical fields
  const groupableFields = availableFields.filter(
    (field) => field.type === "string" || field.type === "date" || field.type === "boolean" || field.type === "enum",
  )

  // Toggle field selection
  const toggleField = (fieldId: string) => {
    if (selectedFields.includes(fieldId)) {
      onFieldsChange(selectedFields.filter((id) => id !== fieldId))
    } else {
      onFieldsChange([...selectedFields, fieldId])
    }
  }

  // Get field label by id
  const getFieldLabel = (fieldId: string) => {
    const field = availableFields.find((f) => f.id === fieldId)
    return field?.label || fieldId
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Badge variant="outline" className="px-2 py-1">
          {selectedFields.length} grouping fields selected
        </Badge>
      </div>

      {groupableFields.length > 0 ? (
        <Card>
          <CardContent className="p-4">
            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Select fields to group your report data. Grouping will aggregate numeric values and count records for
                  each unique combination of the selected fields.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {groupableFields.map((field) => (
                    <div key={field.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`group-${field.id}`}
                        checked={selectedFields.includes(field.id)}
                        onCheckedChange={() => toggleField(field.id)}
                      />
                      <Label htmlFor={`group-${field.id}`} className="text-sm cursor-pointer">
                        {field.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      ) : (
        <div className="text-center py-8 border rounded-md">
          <Group className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-muted-foreground">No groupable fields available</p>
          <p className="text-sm text-muted-foreground">
            The selected data source doesn't have fields suitable for grouping
          </p>
        </div>
      )}

      {selectedFields.length > 0 && (
        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Selected Grouping Fields</h3>
          <div className="flex flex-wrap gap-2">
            {selectedFields.map((fieldId) => (
              <Badge key={fieldId} variant="secondary" className="flex items-center gap-1">
                {getFieldLabel(fieldId)}
                <Button variant="ghost" size="icon" className="h-4 w-4 p-0 ml-1" onClick={() => toggleField(fieldId)}>
                  <Trash2 className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

