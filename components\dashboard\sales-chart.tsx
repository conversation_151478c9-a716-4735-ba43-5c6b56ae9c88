"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { useSocket } from "@/components/socket-provider"
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"

interface SalesData {
  daily: {
    date: string
    sales: number
  }[]
  weekly: {
    date: string
    sales: number
  }[]
  monthly: {
    date: string
    sales: number
  }[]
}

export function SalesChart() {
  const [data, setData] = useState<SalesData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { emit, socket, status } = useSocket()

  useEffect(() => {
    if (status === "connected") {
      setIsLoading(true)

      const syncId = `sales_chart_sync_${Date.now()}`

      const handleSyncResponse = (response: any) => {
        if (response.id === syncId) {
          setData(response.data)
          setIsLoading(false)

          // Remove the event listener
          socket?.off("sync_response", handleSyncResponse)
        }
      }

      // Listen for the response
      socket?.on("sync_response", handleSyncResponse)

      // Request sales data
      emit("sync_request", {
        id: syncId,
        timestamp: Date.now(),
        type: "sales_chart",
      })

      // Fallback in case we don't get a response
      const timeout = setTimeout(() => {
        if (isLoading) {
          // Use mock data if we don't get a response
          setData({
            daily: [
              { date: "Mon", sales: 420.65 },
              { date: "Tue", sales: 380.12 },
              { date: "Wed", sales: 450.3 },
              { date: "Thu", sales: 520.8 },
              { date: "Fri", sales: 580.25 },
              { date: "Sat", sales: 620.5 },
              { date: "Sun", sales: 350.9 },
            ],
            weekly: [
              { date: "Week 1", sales: 2800.65 },
              { date: "Week 2", sales: 3200.12 },
              { date: "Week 3", sales: 2950.3 },
              { date: "Week 4", sales: 3400.8 },
            ],
            monthly: [
              { date: "Jan", sales: 12500.65 },
              { date: "Feb", sales: 11800.12 },
              { date: "Mar", sales: 13200.3 },
              { date: "Apr", sales: 12800.8 },
              { date: "May", sales: 14500.25 },
              { date: "Jun", sales: 13900.5 },
            ],
          })
          setIsLoading(false)
          socket?.off("sync_response", handleSyncResponse)
        }
      }, 3000)

      return () => {
        clearTimeout(timeout)
        socket?.off("sync_response", handleSyncResponse)
      }
    }
  }, [status, emit, socket, isLoading])

  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle>Sales Overview</CardTitle>
        <CardDescription>View your sales performance over time</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="w-full h-[300px] flex items-center justify-center">
            <Skeleton className="h-[300px] w-full" />
          </div>
        ) : (
          <Tabs defaultValue="daily">
            <TabsList className="mb-4">
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
            </TabsList>

            <TabsContent value="daily" className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={data?.daily}
                  margin={{
                    top: 10,
                    right: 30,
                    left: 0,
                    bottom: 0,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, "Sales"]} />
                  <Area type="monotone" dataKey="sales" stroke="#8884d8" fill="#8884d8" />
                </AreaChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="weekly" className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={data?.weekly}
                  margin={{
                    top: 10,
                    right: 30,
                    left: 0,
                    bottom: 0,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, "Sales"]} />
                  <Area type="monotone" dataKey="sales" stroke="#82ca9d" fill="#82ca9d" />
                </AreaChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="monthly" className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={data?.monthly}
                  margin={{
                    top: 10,
                    right: 30,
                    left: 0,
                    bottom: 0,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, "Sales"]} />
                  <Area type="monotone" dataKey="sales" stroke="#ffc658" fill="#ffc658" />
                </AreaChart>
              </ResponsiveContainer>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

