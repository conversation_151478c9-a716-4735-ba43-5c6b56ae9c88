import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const { status, notes } = body

    // Validate the order exists
    const order = await prisma.order.findUnique({
      where: { id: params.id },
      include: {
        customer: true,
      },
    })

    if (!order) {
      return NextResponse.json({ error: "Order not found" }, { status: 404 })
    }

    // Update the order in a transaction
    const updatedOrder = await prisma.$transaction(async (tx) => {
      // Update the order status
      const orderRecord = await tx.order.update({
        where: { id: params.id },
        data: {
          status,
        },
      })

      // Add status history
      await tx.orderStatusHistory.create({
        data: {
          orderId: params.id,
          status,
          notes: notes || `Status updated to ${status}`,
        },
      })

      // Create notification for the status update
      await tx.notification.create({
        data: {
          title: "Order Status Updated",
          message: `Order #${order.orderNumber} status has been updated to ${status.replace(/_/g, " ")}.`,
          type: "INFO",
          userId: session.user.id,
        },
      })

      // If status is CANCELLED, update inventory
      if (status === "CANCELLED" && order.status !== "CANCELLED") {
        // Get order items
        const orderItems = await tx.orderItem.findMany({
          where: { orderId: params.id },
          include: {
            product: true,
            variant: true,
          },
        })

        // Update inventory for each item
        for (const item of orderItems) {
          if (item.variant) {
            await tx.productVariant.update({
              where: { id: item.variantId! },
              data: {
                inventoryLevel: {
                  increment: item.quantity,
                },
              },
            })
          } else {
            await tx.product.update({
              where: { id: item.productId },
              data: {
                inventoryLevel: {
                  increment: item.quantity,
                },
              },
            })
          }

          // Add inventory history record
          await tx.inventoryHistory.create({
            data: {
              productId: item.productId,
              quantity: item.quantity,
              type: "ADJUSTMENT",
              reference: params.id,
              notes: `Order #${order.orderNumber} cancelled`,
            },
          })
        }
      }

      return orderRecord
    })

    return NextResponse.json(updatedOrder)
  } catch (error) {
    console.error("Error updating order status:", error)
    return NextResponse.json({ error: "Failed to update order status" }, { status: 500 })
  }
}

