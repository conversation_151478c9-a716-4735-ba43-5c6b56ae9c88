import { z } from "zod"

export const returnItemSchema = z.object({
  orderItemId: z.string().min(1, "Order item is required"),
  productId: z.string().min(1, "Product is required"),
  variantId: z.string().optional(),
  quantity: z.coerce.number().int("Quantity must be a whole number").positive("Quantity must be positive"),
  reason: z.string().min(1, "Reason is required"),
  condition: z.string().min(1, "Condition is required"),
  refundAmount: z.coerce.number().optional(),
})

export const returnSchema = z.object({
  orderId: z.string().min(1, "Order is required"),
  customerId: z.string().min(1, "Customer is required"),
  reason: z.string().min(1, "Reason is required"),
  notes: z.string().optional(),
  items: z.array(returnItemSchema).min(1, "Return must contain at least one item"),
})

export type ReturnFormValues = z.infer<typeof returnSchema>
export type ReturnItemFormValues = z.infer<typeof returnItemSchema>

