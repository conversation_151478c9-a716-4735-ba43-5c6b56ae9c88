import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// GET /api/user/notification-settings - Get user notification settings
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user with notification settings
    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        notificationSettings: true,
      },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // If user doesn't have notification settings yet, return defaults
    if (!user.notificationSettings) {
      return NextResponse.json({
        emailNotifications: true,
        pushNotifications: true,
        lowStockAlerts: true,
        newOrderAlerts: true,
        orderStatusChanges: true,
        inventoryUpdates: false,
        dailySummary: true,
      })
    }

    return NextResponse.json(user.notificationSettings)
  } catch (error) {
    console.error("Error fetching notification settings:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PUT /api/user/notification-settings - Update user notification settings
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()

    // Update user notification settings
    await prisma.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        notificationSettings: body,
      },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error updating notification settings:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

