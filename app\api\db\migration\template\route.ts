import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { MigrationService } from "@/lib/services/migration-service"
import { ApiError } from "@/lib/api-error"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const searchParams = req.nextUrl.searchParams
    const entityType = searchParams.get("entityType")
    const format = searchParams.get("format") || "csv"

    if (!entityType) {
      throw ApiError.badRequest("No entity type provided")
    }

    // Generate template
    const template = MigrationService.generateImportTemplate(entityType as any, format as any)

    // Return template
    return new NextResponse(template, {
      headers: {
        "Content-Type": format === "json" ? "application/json" : "text/csv",
        "Content-Disposition": `attachment; filename="${entityType}-template.${format}"`,
      },
    })
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Template generation error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

