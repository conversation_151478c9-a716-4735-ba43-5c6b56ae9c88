import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { db } from "../db"

export interface SessionDevice {
  id: string
  userId: string
  userAgent: string
  ipAddress: string
  lastActive: Date
  createdAt: Date
  isCurrentDevice: boolean
}

export class SessionManager {
  /**
   * Get the current session from the server
   */
  static async getCurrentSession() {
    return getServerSession(authOptions)
  }

  /**
   * Record a new session device
   */
  static async recordSessionDevice(
    userId: string,
    sessionId: string,
    userAgent: string,
    ipAddress: string,
  ): Promise<void> {
    // Check if this device already exists
    const existingDevice = await db.sessionDevices.findFirst({
      where: {
        userId,
        userAgent,
        ipAddress,
      },
    })

    if (existingDevice) {
      // Update the existing device
      await db.sessionDevices.update({
        where: { id: existingDevice.id },
        data: {
          sessionId,
          lastActive: new Date(),
        },
      })
    } else {
      // Create a new device record
      await db.sessionDevices.create({
        data: {
          userId,
          sessionId,
          userAgent,
          ipAddress,
          lastActive: new Date(),
          createdAt: new Date(),
        },
      })
    }
  }

  /**
   * Get all devices for a user
   */
  static async getUserDevices(userId: string, currentSessionId?: string): Promise<SessionDevice[]> {
    const devices = await db.sessionDevices.findMany({
      where: { userId },
      orderBy: { lastActive: "desc" },
    })

    return devices.map((device) => ({
      ...device,
      isCurrentDevice: device.sessionId === currentSessionId,
    }))
  }

  /**
   * Revoke a specific session device
   */
  static async revokeDevice(deviceId: string): Promise<void> {
    await db.sessionDevices.delete({
      where: { id: deviceId },
    })

    // Also delete the associated session
    const device = await db.sessionDevices.findUnique({
      where: { id: deviceId },
    })

    if (device) {
      await db.sessions.delete({
        where: { id: device.sessionId },
      })
    }
  }

  /**
   * Revoke all sessions for a user except the current one
   */
  static async revokeAllOtherSessions(userId: string, currentSessionId: string): Promise<void> {
    // Get all other devices
    const otherDevices = await db.sessionDevices.findMany({
      where: {
        userId,
        NOT: {
          sessionId: currentSessionId,
        },
      },
    })

    // Delete all other devices
    await db.sessionDevices.deleteMany({
      where: {
        userId,
        NOT: {
          sessionId: currentSessionId,
        },
      },
    })

    // Delete all associated sessions
    for (const device of otherDevices) {
      await db.sessions.delete({
        where: { id: device.sessionId },
      })
    }
  }

  /**
   * Update the last active timestamp for a session
   */
  static async updateSessionActivity(sessionId: string): Promise<void> {
    await db.sessionDevices.updateMany({
      where: { sessionId },
      data: { lastActive: new Date() },
    })
  }

  /**
   * Clean up expired sessions (older than the specified days)
   */
  static async cleanupExpiredSessions(maxAgeDays = 30): Promise<void> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - maxAgeDays)

    const expiredDevices = await db.sessionDevices.findMany({
      where: {
        lastActive: {
          lt: cutoffDate,
        },
      },
    })

    // Delete expired devices
    await db.sessionDevices.deleteMany({
      where: {
        lastActive: {
          lt: cutoffDate,
        },
      },
    })

    // Delete associated sessions
    for (const device of expiredDevices) {
      await db.sessions.delete({
        where: { id: device.sessionId },
      })
    }
  }
}

