import { Suspense } from "react"
import dynamic from "next/dynamic"
import { Skeleton } from "@/components/ui/skeleton"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardStatsSkeleton } from "@/components/dashboard/dashboard-stats-skeleton"

// Dynamically import heavy components
const DashboardStats = dynamic(() => import("@/components/dashboard/dashboard-stats"), {
  loading: () => <DashboardStatsSkeleton />,
  ssr: false,
})

const SalesChart = dynamic(() => import("@/components/dashboard/sales-chart"), {
  loading: () => (
    <div className="h-[300px] rounded-lg border bg-card p-6">
      <Skeleton className="h-full w-full" />
    </div>
  ),
  ssr: false,
})

const RecentOrders = dynamic(() => import("@/components/dashboard/recent-orders"), {
  loading: () => (
    <div className="rounded-lg border bg-card">
      <div className="p-6">
        <Skeleton className="h-6 w-1/3" />
      </div>
      <div className="p-6">
        <Skeleton className="h-20 w-full" />
      </div>
    </div>
  ),
  ssr: false,
})

const TopProducts = dynamic(() => import("@/components/dashboard/top-products"), {
  loading: () => (
    <div className="rounded-lg border bg-card">
      <div className="p-6">
        <Skeleton className="h-6 w-1/3" />
      </div>
      <div className="p-6">
        <Skeleton className="h-20 w-full" />
      </div>
    </div>
  ),
  ssr: false,
})

export default function DashboardPage() {
  return (
    <div className="space-y-6 p-6">
      <DashboardHeader />

      <Suspense fallback={<DashboardStatsSkeleton />}>
        <DashboardStats />
      </Suspense>

      <div className="grid gap-6 md:grid-cols-2">
        <Suspense fallback={<Skeleton className="h-[300px]" />}>
          <SalesChart />
        </Suspense>

        <div className="grid gap-6">
          <Suspense fallback={<Skeleton className="h-[200px]" />}>
            <RecentOrders />
          </Suspense>

          <Suspense fallback={<Skeleton className="h-[200px]" />}>
            <TopProducts />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

