import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { prisma } from "./prisma"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date) {
  return new Intl.DateTimeFormat("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  }).format(new Date(date))
}

export function formatCurrency(amount: number) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount)
}

export function truncate(str: string, length: number) {
  return str.length > length ? `${str.substring(0, length)}...` : str
}

export function getInitials(name = "") {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
}

export async function generateOrderNumber() {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, "0")
  const day = date.getDate().toString().padStart(2, "0")

  const prefix = `ORD-${year}${month}${day}-`

  // Get the latest order number with this prefix
  const latestOrder = await prisma.order.findFirst({
    where: {
      orderNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      orderNumber: "desc",
    },
  })

  let sequence = 1
  if (latestOrder) {
    const latestSequence = Number.parseInt(latestOrder.orderNumber.split("-")[2])
    sequence = latestSequence + 1
  }

  return `${prefix}${sequence.toString().padStart(4, "0")}`
}

export async function generateReturnNumber() {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, "0")
  const day = date.getDate().toString().padStart(2, "0")

  const prefix = `RET-${year}${month}${day}-`

  // Get the latest return number with this prefix
  const latestReturn = await prisma.return.findFirst({
    where: {
      returnNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      returnNumber: "desc",
    },
  })

  let sequence = 1
  if (latestReturn) {
    const latestSequence = Number.parseInt(latestReturn.returnNumber.split("-")[2])
    sequence = latestSequence + 1
  }

  return `${prefix}${sequence.toString().padStart(4, "0")}`
}

export async function generateLayawayNumber() {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, "0")

  const prefix = `LAY-${year}${month}-`

  // Get the latest layaway number with this prefix
  const latestLayaway = await prisma.layaway.findFirst({
    where: {
      layawayNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      layawayNumber: "desc",
    },
  })

  let sequence = 1
  if (latestLayaway) {
    const latestSequence = Number.parseInt(latestLayaway.layawayNumber.split("-")[2])
    sequence = latestSequence + 1
  }

  return `${prefix}${sequence.toString().padStart(4, "0")}`
}

