import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const tags = await prisma.customerTag.findMany({
      orderBy: {
        name: "asc",
      },
    })

    return NextResponse.json(tags)
  } catch (error) {
    console.error("[CUSTOMER_TAGS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { name, color } = body

    if (!name) {
      return new NextResponse("Name is required", { status: 400 })
    }

    const tag = await prisma.customerTag.create({
      data: {
        name,
        color,
      },
    })

    return NextResponse.json(tag)
  } catch (error) {
    console.error("[CUSTOMER_TAGS_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

