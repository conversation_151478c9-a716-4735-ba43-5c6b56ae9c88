import { z } from "zod"

export const recurringOrderItemSchema = z.object({
  productId: z.string().min(1, "Product is required"),
  variantId: z.string().optional(),
  quantity: z.coerce.number().int("Quantity must be a whole number").positive("Quantity must be positive"),
  unitPrice: z.coerce.number().min(0, "Price cannot be negative"),
})

export const recurringOrderSchema = z.object({
  customerId: z.string().min(1, "Customer is required"),
  frequency: z.enum(["DAILY", "WEEKLY", "BIWEEKLY", "MONTHLY", "QUARTERLY", "BIANNUALLY", "ANNUALLY"], {
    invalid_type_error: "Invalid frequency",
  }),
  nextOrderDate: z.date({
    required_error: "Next order date is required",
    invalid_type_error: "Invalid date format",
  }),
  endDate: z
    .date({
      invalid_type_error: "Invalid date format",
    })
    .optional()
    .nullable(),
  paymentMethod: z.enum(
    ["CASH", "CREDIT_CARD", "DEBIT_CARD", "BANK_TRANSFER", "CHECK", "PAYPAL", "STORE_CREDIT", "GIFT_CARD", "OTHER"],
    {
      invalid_type_error: "Invalid payment method",
    },
  ),
  shippingAddress: z.string().optional(),
  billingAddress: z.string().optional(),
  notes: z.string().optional(),
  items: z.array(recurringOrderItemSchema).min(1, "Recurring order must contain at least one item"),
})

export type RecurringOrderFormValues = z.infer<typeof recurringOrderSchema>
export type RecurringOrderItemFormValues = z.infer<typeof recurringOrderItemSchema>

