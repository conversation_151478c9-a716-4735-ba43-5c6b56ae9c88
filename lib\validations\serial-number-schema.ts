import { z } from "zod"

export const serialNumberSchema = z.object({
  serialNumber: z.string().min(1, "Serial number is required"),
  status: z.enum(["in_stock", "sold", "reserved", "defective", "returned"]),
  notes: z.string().optional(),
  purchaseDate: z.string().optional().nullable(),
  productId: z.string().min(1, "Product is required"),
  variantId: z.string().optional().nullable(),
})

export const serialNumberBulkImportSchema = z.object({
  productId: z.string().min(1, "Product is required"),
  variantId: z.string().optional().nullable(),
  serialNumbers: z.array(z.string().min(1, "Serial number is required")),
  status: z.enum(["in_stock", "sold", "reserved", "defective", "returned"]).default("in_stock"),
  purchaseDate: z.string().optional().nullable(),
})

export type SerialNumberFormValues = z.infer<typeof serialNumberSchema>
export type SerialNumberBulkImportValues = z.infer<typeof serialNumberBulkImportSchema>

