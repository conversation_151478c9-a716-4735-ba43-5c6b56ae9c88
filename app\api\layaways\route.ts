import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { generateLayawayNumber } from "@/lib/utils"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const customerId = searchParams.get("customerId")
    const status = searchParams.get("status")

    const where: any = {}

    if (customerId) where.customerId = customerId
    if (status) where.status = status

    const layaways = await prisma.layaway.findMany({
      where,
      include: {
        customer: true,
        items: {
          include: {
            product: true,
            variant: true,
          },
        },
        payments: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(layaways)
  } catch (error) {
    console.error("Error fetching layaways:", error)
    return NextResponse.json({ error: "Failed to fetch layaways" }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()

    const { customerId, totalAmount, depositAmount, expiryDate, notes, items, paymentMethod } = body

    // Generate a unique layaway number
    const layawayNumber = await generateLayawayNumber()

    // Calculate balance due
    const balanceDue = totalAmount - depositAmount

    // Create the layaway and its items in a transaction
    const newLayaway = await prisma.$transaction(async (tx) => {
      // Create the layaway
      const layaway = await tx.layaway.create({
        data: {
          layawayNumber,
          status: "ACTIVE",
          totalAmount,
          depositAmount,
          balanceDue,
          expiryDate: new Date(expiryDate),
          notes,
          customerId,
        },
      })

      // Create layaway items
      for (const item of items) {
        await tx.layawayItem.create({
          data: {
            layawayId: layaway.id,
            productId: item.productId,
            variantId: item.variantId || null,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            total: item.quantity * item.unitPrice,
          },
        })

        // Reserve inventory
        if (item.variantId) {
          await tx.productVariant.update({
            where: { id: item.variantId },
            data: {
              inventoryLevel: {
                decrement: item.quantity,
              },
            },
          })
        } else {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              inventoryLevel: {
                decrement: item.quantity,
              },
            },
          })
        }

        // Add inventory history record
        await tx.inventoryHistory.create({
          data: {
            productId: item.productId,
            quantity: -item.quantity,
            type: "LAYAWAY",
            reference: layaway.id,
            notes: `Layaway #${layawayNumber}`,
          },
        })
      }

      // Create initial payment record if deposit is provided
      if (depositAmount > 0) {
        await tx.layawayPayment.create({
          data: {
            layawayId: layaway.id,
            amount: depositAmount,
            paymentMethod,
            notes: "Initial deposit",
          },
        })
      }

      // Create notification for the layaway
      await tx.notification.create({
        data: {
          title: "New Layaway Created",
          message: `Layaway #${layawayNumber} has been created for ${depositAmount > 0 ? "with an initial deposit of " + depositAmount : "without a deposit"}.`,
          type: "INFO",
          userId: session.user.id,
        },
      })

      return layaway
    })

    return NextResponse.json(newLayaway, { status: 201 })
  } catch (error) {
    console.error("Error creating layaway:", error)
    return NextResponse.json({ error: "Failed to create layaway" }, { status: 500 })
  }
}

