"use client"

import { useState, useEffect } from "react"
import { useInView } from "react-intersection-observer"
import { Loader2 } from "lucide-react"
import type { Product } from "@/types"
import { ProductCard } from "@/components/products/product-card"
import { Button } from "@/components/ui/button"

interface VirtualizedProductListProps {
  initialProducts: Product[]
  totalCount: number
  pageSize?: number
}

export function VirtualizedProductList({ initialProducts, totalCount, pageSize = 20 }: VirtualizedProductListProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts)
  const [page, setPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(initialProducts.length < totalCount)

  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: "200px",
  })

  const loadMoreProducts = async () => {
    if (loading || !hasMore) return

    setLoading(true)
    const nextPage = page + 1

    try {
      const response = await fetch(`/api/products?page=${nextPage}&limit=${pageSize}`)
      const data = await response.json()

      if (data.products.length > 0) {
        setProducts((prev) => [...prev, ...data.products])
        setPage(nextPage)
        setHasMore(products.length + data.products.length < totalCount)
      } else {
        setHasMore(false)
      }
    } catch (error) {
      console.error("Error loading more products:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (inView && hasMore) {
      loadMoreProducts()
    }
  }, [inView])

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {hasMore && (
        <div ref={ref} className="flex justify-center py-4">
          {loading ? (
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          ) : (
            <Button variant="outline" onClick={loadMoreProducts} className="w-full max-w-xs">
              Load More Products
            </Button>
          )}
        </div>
      )}
    </div>
  )
}

