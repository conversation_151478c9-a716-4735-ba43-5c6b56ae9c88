@layer utilities {
  /* Touch-specific utilities */
  @media (pointer: coarse) {
    .touch\:p-4 {
      padding: 1rem;
    }

    .touch\:py-3 {
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
    }

    .touch\:px-4 {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .touch\:text-base {
      font-size: 1rem;
      line-height: 1.5rem;
    }

    .touch\:min-h-\[48px\] {
      min-height: 48px;
    }

    .touch\:gap-4 {
      gap: 1rem;
    }

    .touch\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
      --tw-space-y-reverse: 0;
      margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
      margin-bottom: calc(1rem * var(--tw-space-y-reverse));
    }
  }

  /* Safe area insets for modern mobile browsers */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-specific animations */
  .animate-pull-to-refresh {
    animation: pull-to-refresh 1s ease-in-out infinite;
  }

  @keyframes pull-to-refresh {
    0% {
      transform: translateY(0) rotate(0);
    }
    50% {
      transform: translateY(10px) rotate(180deg);
    }
    100% {
      transform: translateY(0) rotate(360deg);
    }
  }
}

