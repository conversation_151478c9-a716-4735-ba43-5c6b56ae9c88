import { z } from "zod"

export const generalSettingsSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  address: z.string().optional(),
  currency: z.string().min(1, "Currency is required"),
  taxRate: z.coerce.number().min(0, "Tax rate cannot be negative").max(100, "Tax rate cannot exceed 100%"),
  logo: z.any().optional(),
})

export const notificationSettingsSchema = z.object({
  emailNotifications: z.boolean(),
  stockAlerts: z.boolean(),
  orderUpdates: z.boolean(),
  securityAlerts: z.boolean(),
  marketingEmails: z.boolean(),
})

export const backupSettingsSchema = z.object({
  autoBackup: z.boolean(),
  backupFrequency: z.enum(["daily", "weekly", "monthly"]),
  backupTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  retentionCount: z.coerce.number().int().min(1, "Must keep at least 1 backup").max(100, "Cannot exceed 100 backups"),
})

export type GeneralSettingsFormValues = z.infer<typeof generalSettingsSchema>
export type NotificationSettingsFormValues = z.infer<typeof notificationSettingsSchema>
export type BackupSettingsFormValues = z.infer<typeof backupSettingsSchema>

