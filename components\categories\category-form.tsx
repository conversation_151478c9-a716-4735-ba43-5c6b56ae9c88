"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { categorySchema, type CategoryFormValues } from "@/lib/validations/category-schema"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useCachedData } from "@/lib/hooks/use-cached-data"
import { fetchApi } from "@/lib/api-client"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface CategoryFormProps {
  initialData?: CategoryFormValues & { id?: string }
  isEditing?: boolean
}

export function CategoryForm({ initialData, isEditing = false }: CategoryFormProps) {
  const { toast } = useToast()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [serverError, setServerError] = useState<string | null>(null)

  const { data: categories } = useCachedData<{ id: string; name: string }[]>({
    key: "categories",
    fetcher: () => fetchApi("/api/categories"),
  })

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categorySchema),
    defaultValues: initialData || {
      name: "",
      description: "",
      color: "#6366F1",
      parentId: "",
    },
    mode: "onBlur",
  })

  const onSubmit = async (values: CategoryFormValues) => {
    setIsSubmitting(true)
    setServerError(null)

    try {
      if (isEditing && initialData?.id) {
        await fetchApi(`/api/categories/${initialData.id}`, {
          method: "PUT",
          body: JSON.stringify(values),
        })

        toast({
          title: "Category updated",
          description: "The category has been successfully updated",
        })
      } else {
        await fetchApi("/api/categories", {
          method: "POST",
          body: JSON.stringify(values),
        })

        toast({
          title: "Category created",
          description: "The category has been successfully created",
        })

        form.reset()
      }

      router.push("/dashboard/categories")
      router.refresh()
    } catch (error) {
      setServerError(error instanceof Error ? error.message : "An unexpected error occurred")
      toast({
        title: isEditing ? "Failed to update category" : "Failed to create category",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {serverError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{serverError}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter category name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="color"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Color</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input type="color" {...field} className="w-12 h-10 p-1" />
                  </FormControl>
                  <FormControl>
                    <Input {...field} placeholder="#RRGGBB" />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="parentId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Parent Category (Optional)</FormLabel>
                <FormControl>
                  <select
                    className="w-full h-10 rounded-md border border-input bg-background px-3 py-2"
                    {...field}
                    value={field.value || ""}
                  >
                    <option value="">None (Top Level)</option>
                    {categories
                      ?.filter((cat) => cat.id !== initialData?.id)
                      .map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                  </select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter category description"
                  className="min-h-32"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : isEditing ? "Update Category" : "Create Category"}
          </Button>
        </div>
      </form>
    </Form>
  )
}

