/**
 * StockSync Permission System
 *
 * This module defines a granular permission system for StockSync
 * following the principle of least privilege.
 */

// Resource types in the system
export enum Resource {
  PRODUCT = "product",
  CATEGORY = "category",
  SUPPLIER = "supplier",
  ORDER = "order",
  CUSTOMER = "customer",
  USER = "user",
  ROLE = "role",
  REPORT = "report",
  SETTING = "setting",
  INVENTORY = "inventory",
  TRANSACTION = "transaction",
}

// Actions that can be performed on resources
export enum Action {
  CREATE = "create",
  READ = "read",
  UPDATE = "update",
  DELETE = "delete",
  EXPORT = "export",
  IMPORT = "import",
  APPROVE = "approve",
  REJECT = "reject",
  ASSIGN = "assign",
  MANAGE = "manage", // Special action for managing settings, roles, etc.
}

// Permission format: resource:action
export type Permission = `${Resource}:${Action}`

// Predefined permission sets
export const PERMISSIONS = {
  // Product permissions
  PRODUCT_CREATE: "product:create" as Permission,
  PRODUCT_READ: "product:read" as Permission,
  PRODUCT_UPDATE: "product:update" as Permission,
  PRODUCT_DELETE: "product:delete" as Permission,
  PRODUCT_EXPORT: "product:export" as Permission,
  PRODUCT_IMPORT: "product:import" as Permission,

  // Category permissions
  CATEGORY_CREATE: "category:create" as Permission,
  CATEGORY_READ: "category:read" as Permission,
  CATEGORY_UPDATE: "category:update" as Permission,
  CATEGORY_DELETE: "category:delete" as Permission,

  // Supplier permissions
  SUPPLIER_CREATE: "supplier:create" as Permission,
  SUPPLIER_READ: "supplier:read" as Permission,
  SUPPLIER_UPDATE: "supplier:update" as Permission,
  SUPPLIER_DELETE: "supplier:delete" as Permission,

  // Order permissions
  ORDER_CREATE: "order:create" as Permission,
  ORDER_READ: "order:read" as Permission,
  ORDER_UPDATE: "order:update" as Permission,
  ORDER_DELETE: "order:delete" as Permission,
  ORDER_APPROVE: "order:approve" as Permission,
  ORDER_REJECT: "order:reject" as Permission,

  // Customer permissions
  CUSTOMER_CREATE: "customer:create" as Permission,
  CUSTOMER_READ: "customer:read" as Permission,
  CUSTOMER_UPDATE: "customer:update" as Permission,
  CUSTOMER_DELETE: "customer:delete" as Permission,

  // User permissions
  USER_CREATE: "user:create" as Permission,
  USER_READ: "user:read" as Permission,
  USER_UPDATE: "user:update" as Permission,
  USER_DELETE: "user:delete" as Permission,

  // Role permissions
  ROLE_CREATE: "role:create" as Permission,
  ROLE_READ: "role:read" as Permission,
  ROLE_UPDATE: "role:update" as Permission,
  ROLE_DELETE: "role:delete" as Permission,
  ROLE_ASSIGN: "role:assign" as Permission,

  // Report permissions
  REPORT_CREATE: "report:create" as Permission,
  REPORT_READ: "report:read" as Permission,
  REPORT_EXPORT: "report:export" as Permission,

  // Setting permissions
  SETTING_READ: "setting:read" as Permission,
  SETTING_MANAGE: "setting:manage" as Permission,

  // Inventory permissions
  INVENTORY_READ: "inventory:read" as Permission,
  INVENTORY_UPDATE: "inventory:update" as Permission,

  // Transaction permissions
  TRANSACTION_CREATE: "transaction:create" as Permission,
  TRANSACTION_READ: "transaction:read" as Permission,
}

// Predefined roles with associated permissions
export const PREDEFINED_ROLES = {
  ADMIN: {
    name: "Administrator",
    description: "Full access to all system features",
    permissions: Object.values(PERMISSIONS),
  },
  MANAGER: {
    name: "Manager",
    description: "Manage inventory, orders, and reports",
    permissions: [
      // Product permissions
      PERMISSIONS.PRODUCT_CREATE,
      PERMISSIONS.PRODUCT_READ,
      PERMISSIONS.PRODUCT_UPDATE,
      PERMISSIONS.PRODUCT_EXPORT,
      PERMISSIONS.PRODUCT_IMPORT,

      // Category permissions
      PERMISSIONS.CATEGORY_CREATE,
      PERMISSIONS.CATEGORY_READ,
      PERMISSIONS.CATEGORY_UPDATE,

      // Supplier permissions
      PERMISSIONS.SUPPLIER_CREATE,
      PERMISSIONS.SUPPLIER_READ,
      PERMISSIONS.SUPPLIER_UPDATE,

      // Order permissions
      PERMISSIONS.ORDER_CREATE,
      PERMISSIONS.ORDER_READ,
      PERMISSIONS.ORDER_UPDATE,
      PERMISSIONS.ORDER_APPROVE,
      PERMISSIONS.ORDER_REJECT,

      // Customer permissions
      PERMISSIONS.CUSTOMER_CREATE,
      PERMISSIONS.CUSTOMER_READ,
      PERMISSIONS.CUSTOMER_UPDATE,

      // Report permissions
      PERMISSIONS.REPORT_CREATE,
      PERMISSIONS.REPORT_READ,
      PERMISSIONS.REPORT_EXPORT,

      // Inventory permissions
      PERMISSIONS.INVENTORY_READ,
      PERMISSIONS.INVENTORY_UPDATE,

      // Transaction permissions
      PERMISSIONS.TRANSACTION_CREATE,
      PERMISSIONS.TRANSACTION_READ,
    ],
  },
  SALES: {
    name: "Sales Associate",
    description: "Process sales and manage customers",
    permissions: [
      // Product permissions
      PERMISSIONS.PRODUCT_READ,

      // Category permissions
      PERMISSIONS.CATEGORY_READ,

      // Order permissions
      PERMISSIONS.ORDER_CREATE,
      PERMISSIONS.ORDER_READ,

      // Customer permissions
      PERMISSIONS.CUSTOMER_CREATE,
      PERMISSIONS.CUSTOMER_READ,
      PERMISSIONS.CUSTOMER_UPDATE,

      // Transaction permissions
      PERMISSIONS.TRANSACTION_CREATE,
      PERMISSIONS.TRANSACTION_READ,
    ],
  },
  INVENTORY: {
    name: "Inventory Clerk",
    description: "Manage product inventory",
    permissions: [
      // Product permissions
      PERMISSIONS.PRODUCT_READ,
      PERMISSIONS.PRODUCT_UPDATE,

      // Category permissions
      PERMISSIONS.CATEGORY_READ,

      // Supplier permissions
      PERMISSIONS.SUPPLIER_READ,

      // Inventory permissions
      PERMISSIONS.INVENTORY_READ,
      PERMISSIONS.INVENTORY_UPDATE,
    ],
  },
  READONLY: {
    name: "Read Only",
    description: "View-only access to system data",
    permissions: [
      PERMISSIONS.PRODUCT_READ,
      PERMISSIONS.CATEGORY_READ,
      PERMISSIONS.SUPPLIER_READ,
      PERMISSIONS.ORDER_READ,
      PERMISSIONS.CUSTOMER_READ,
      PERMISSIONS.REPORT_READ,
      PERMISSIONS.INVENTORY_READ,
      PERMISSIONS.TRANSACTION_READ,
    ],
  },
}

// Helper functions for permission checking
export function hasPermission(userPermissions: Permission[], requiredPermission: Permission): boolean {
  return userPermissions.includes(requiredPermission)
}

export function hasAnyPermission(userPermissions: Permission[], requiredPermissions: Permission[]): boolean {
  return requiredPermissions.some((permission) => userPermissions.includes(permission))
}

export function hasAllPermissions(userPermissions: Permission[], requiredPermissions: Permission[]): boolean {
  return requiredPermissions.every((permission) => userPermissions.includes(permission))
}

