import { notFound } from "next/navigation"
import { RecurringOrderDetails } from "@/components/recurring-orders/recurring-order-details"
import prisma from "@/lib/prisma"

interface RecurringOrderPageProps {
  params: {
    id: string
  }
}

async function getRecurringOrder(id: string) {
  const recurringOrder = await prisma.recurringOrder.findUnique({
    where: { id },
    include: {
      customer: true,
      items: {
        include: {
          product: true,
          variant: true,
        },
      },
      orderHistory: {
        orderBy: {
          scheduledDate: "desc",
        },
      },
    },
  })

  if (!recurringOrder) {
    notFound()
  }

  return recurringOrder
}

export default async function RecurringOrderPage({ params }: RecurringOrderPageProps) {
  const recurringOrder = await getRecurringOrder(params.id)

  return (
    <div>
      <RecurringOrderDetails recurringOrder={recurringOrder} />
    </div>
  )
}

