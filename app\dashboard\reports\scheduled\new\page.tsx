import type { Metadata } from "next"
import { ReportScheduler } from "@/components/reports/report-scheduler"

export const metadata: Metadata = {
  title: "Create Schedule",
  description: "Create a new scheduled report",
}

export default function NewSchedulePage({ searchParams }: { searchParams: { reportId?: string } }) {
  const reportId = searchParams.reportId

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Create Schedule</h2>
        <p className="text-muted-foreground">Set up automated report delivery via email</p>
      </div>

      <ReportScheduler reportId={reportId} />
    </div>
  )
}

