import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { parseISO, startOfDay, endOfDay } from "date-fns"

export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const fromDate = searchParams.get("from")
    const toDate = searchParams.get("to")
    const category = searchParams.get("category")
    const store = searchParams.get("store")
    const product = searchParams.get("product")
    const groupBy = searchParams.get("groupBy") || "category"

    if (!fromDate || !toDate) {
      return NextResponse.json({ error: "Missing date range parameters" }, { status: 400 })
    }

    // Parse dates
    const from = startOfDay(parseISO(fromDate))
    const to = endOfDay(parseISO(toDate))

    // Build query filters
    const filters: any = {}

    if (category) {
      filters.categoryId = category
    }

    if (store) {
      filters.storeId = store
    }

    if (product) {
      filters.id = product
    }

    // Fetch products
    const products = await prisma.product.findMany({
      where: filters,
      include: {
        category: true,
        stockMovements: {
          where: {
            createdAt: {
              gte: from,
              lte: to,
            },
          },
        },
      },
    })

    // Calculate inventory metrics
    const totalProducts = products.length
    const lowStockItems = products.filter((p) => p.stockQuantity <= (p.reorderPoint || 10)).length
    const outOfStockItems = products.filter((p) => p.stockQuantity <= 0).length
    const inventoryValue = products.reduce((sum, p) => sum + Number(p.price) * p.stockQuantity, 0)

    // Calculate stock turnover (simplified)
    const totalSold = products.reduce((sum, p) => {
      const soldQuantity = p.stockMovements.filter((m) => m.type === "sale").reduce((q, m) => q + m.quantity, 0)
      return sum + soldQuantity
    }, 0)

    const averageInventory = products.reduce((sum, p) => sum + p.stockQuantity, 0) / products.length
    const stockTurnover = averageInventory > 0 ? totalSold / averageInventory : 0

    // Generate chart data based on groupBy parameter
    let chartData: any[] = []

    if (groupBy === "category") {
      // Group by category
      const categoryData = new Map()

      products.forEach((product) => {
        const categoryName = product.category.name
        const existing = categoryData.get(categoryName) || {
          name: categoryName,
          stockQuantity: 0,
          value: 0,
          products: 0,
          lowStock: 0,
        }

        existing.stockQuantity += product.stockQuantity
        existing.value += Number(product.price) * product.stockQuantity
        existing.products += 1
        existing.lowStock += product.stockQuantity <= (product.reorderPoint || 10) ? 1 : 0

        categoryData.set(categoryName, existing)
      })

      chartData = Array.from(categoryData.values())
    } else {
      // Individual products
      chartData = products.map((p) => ({
        name: p.name,
        stockQuantity: p.stockQuantity,
        value: Number(p.price) * p.stockQuantity,
        reorderPoint: p.reorderPoint || 10,
        category: p.category.name,
      }))
    }

    // Generate pie chart data
    const pieData = Array.from(
      products.reduce((map, product) => {
        const categoryName = product.category.name
        const existing = map.get(categoryName) || { name: categoryName, value: 0 }
        existing.value += Number(product.price) * product.stockQuantity
        map.set(categoryName, existing)
        return map
      }, new Map()),
    ).map(([_, value]) => value)

    // Generate table data
    const tableData = products.map((p) => ({
      id: p.id,
      name: p.name,
      sku: p.sku,
      category: p.category.name,
      price: Number(p.price),
      stockQuantity: p.stockQuantity,
      value: Number(p.price) * p.stockQuantity,
      reorderPoint: p.reorderPoint || 10,
      status:
        p.stockQuantity <= 0 ? "Out of Stock" : p.stockQuantity <= (p.reorderPoint || 10) ? "Low Stock" : "In Stock",
    }))

    // Calculate previous period for comparison
    // This is simplified - in a real app you'd need to fetch historical data
    const inventoryValueChange = 0 // Placeholder

    return NextResponse.json({
      metrics: {
        totalProducts,
        lowStockItems,
        outOfStockItems,
        outOfStockPercentage: (outOfStockItems / totalProducts) * 100,
        inventoryValue,
        inventoryValueChange,
        stockTurnover,
      },
      chartData,
      pieData,
      tableData,
    })
  } catch (error) {
    console.error("Error generating inventory report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

