import type { StateCreator } from "zustand"
import type { StoreState } from "./index"
import { produce } from "immer"

export interface CartItem {
  id: string
  name: string
  price: number
  quantity: number
  image?: string | null
  sku?: string
  barcode?: string
}

export interface HeldSale {
  id: string
  timestamp: number
  cart: CartItem[]
  customerName?: string
  notes?: string
}

export interface POSSlice {
  cart: CartItem[]
  addToCart: (item: CartItem) => void
  removeFromCart: (itemId: string) => void
  updateCartItem: (itemId: string, quantity: number) => void
  clearCart: () => void
  heldSales: HeldSale[]
  holdCurrentSale: (customerName?: string, notes?: string) => void
  resumeHeldSale: (saleId: string) => void
  removeHeldSale: (saleId: string) => void
}

export const createPOSSlice: StateCreator<StoreState, [], [], POSSlice> = (set) => ({
  cart: [],
  addToCart: (item) =>
    set(
      produce((state: POSSlice) => {
        const existingItemIndex = state.cart.findIndex((i) => i.id === item.id)

        if (existingItemIndex >= 0) {
          state.cart[existingItemIndex].quantity += item.quantity || 1
        } else {
          state.cart.push({ ...item, quantity: item.quantity || 1 })
        }
      }),
    ),
  removeFromCart: (itemId) =>
    set(
      produce((state: POSSlice) => {
        state.cart = state.cart.filter((item) => item.id !== itemId)
      }),
    ),
  updateCartItem: (itemId, quantity) =>
    set(
      produce((state: POSSlice) => {
        if (quantity <= 0) {
          state.cart = state.cart.filter((item) => item.id !== itemId)
        } else {
          const itemIndex = state.cart.findIndex((item) => item.id === itemId)
          if (itemIndex >= 0) {
            state.cart[itemIndex].quantity = quantity
          }
        }
      }),
    ),
  clearCart: () => set({ cart: [] }),
  heldSales: [],
  holdCurrentSale: (customerName, notes) =>
    set(
      produce((state: POSSlice) => {
        if (state.cart.length === 0) return

        const newHeldSale: HeldSale = {
          id: `sale_${Date.now()}`,
          timestamp: Date.now(),
          cart: [...state.cart],
          customerName,
          notes,
        }

        state.heldSales.push(newHeldSale)
        state.cart = []
      }),
    ),
  resumeHeldSale: (saleId) =>
    set(
      produce((state: POSSlice) => {
        const saleToResume = state.heldSales.find((sale) => sale.id === saleId)

        if (!saleToResume) return

        state.cart = [...saleToResume.cart]
        state.heldSales = state.heldSales.filter((sale) => sale.id !== saleId)
      }),
    ),
  removeHeldSale: (saleId) =>
    set(
      produce((state: POSSlice) => {
        state.heldSales = state.heldSales.filter((sale) => sale.id !== saleId)
      }),
    ),
})

