"use client"

import { useEffect, useState } from "react"
import { useSocket } from "./socket-provider"
import { useOfflineStore } from "@/lib/store"
import { AlertTriangle, WifiOff } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export function OfflineIndicator() {
  const { status, reconnect } = useSocket()
  const { isOffline, offlineTransactions } = useOfflineStore()
  const [showDialog, setShowDialog] = useState(false)

  // Show dialog when going offline with pending transactions
  useEffect(() => {
    if (isOffline && offlineTransactions.length > 0 && status === "disconnected") {
      setShowDialog(true)
    } else if (status === "connected") {
      setShowDialog(false)
    }
  }, [isOffline, offlineTransactions.length, status])

  if (!isOffline) return null

  return (
    <>
      <div className="fixed bottom-4 right-4 z-50">
        <Button variant="destructive" size="sm" className="flex items-center gap-2" onClick={() => setShowDialog(true)}>
          <WifiOff className="h-4 w-4" />
          <span>Offline Mode</span>
          {offlineTransactions.length > 0 && (
            <span className="ml-1 rounded-full bg-background px-1.5 text-xs text-foreground">
              {offlineTransactions.length}
            </span>
          )}
        </Button>
      </div>

      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Operating in Offline Mode
            </DialogTitle>
            <DialogDescription>
              You are currently working offline. Your changes will be synchronized when your connection is restored.
            </DialogDescription>
          </DialogHeader>

          {offlineTransactions.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium">Pending transactions:</p>
              <div className="rounded-md bg-muted p-3 text-sm">
                <ul className="list-disc pl-5 space-y-1">
                  {offlineTransactions.length > 5 ? (
                    <>
                      {offlineTransactions.slice(0, 5).map((tx, i) => (
                        <li key={i}>
                          {tx.type === "order" ? "Order" : tx.type === "inventory" ? "Inventory update" : "Customer"} -{" "}
                          {new Date(tx.timestamp).toLocaleTimeString()}
                        </li>
                      ))}
                      <li>And {offlineTransactions.length - 5} more...</li>
                    </>
                  ) : (
                    offlineTransactions.map((tx, i) => (
                      <li key={i}>
                        {tx.type === "order" ? "Order" : tx.type === "inventory" ? "Inventory update" : "Customer"} -{" "}
                        {new Date(tx.timestamp).toLocaleTimeString()}
                      </li>
                    ))
                  )}
                </ul>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Dismiss
            </Button>
            <Button
              onClick={() => {
                reconnect()
                setShowDialog(false)
              }}
            >
              Try to Reconnect
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

