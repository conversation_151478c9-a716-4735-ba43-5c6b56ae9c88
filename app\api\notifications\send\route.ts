import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

// This would be replaced with your actual FCM service account
import admin from "firebase-admin"

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
    }),
  })
}

export async function POST(req: NextRequest) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if user has permission to send notifications
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    })

    if (!user || !["ADMIN", "MANAGER"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Get notification data from request body
    const { title, body, data = {}, userIds = [], channel = "system", priority = "default" } = await req.json()

    if (!title || !body) {
      return NextResponse.json(
        {
          error: "Title and body are required",
        },
        { status: 400 },
      )
    }

    // Get device tokens for the specified users or all users if none specified
    const deviceTokens = await prisma.deviceToken.findMany({
      where: userIds.length > 0 ? { userId: { in: userIds } } : undefined,
      select: { token: true },
    })

    const tokens = deviceTokens.map((dt) => dt.token)

    if (tokens.length === 0) {
      return NextResponse.json(
        {
          message: "No device tokens found",
        },
        { status: 200 },
      )
    }

    // Prepare notification message
    const message = {
      notification: {
        title,
        body,
      },
      data: {
        ...data,
        channel,
        sentAt: new Date().toISOString(),
      },
      android: {
        priority: priority === "high" ? "high" : "normal",
        notification: {
          clickAction: "FLUTTER_NOTIFICATION_CLICK",
        },
      },
      apns: {
        payload: {
          aps: {
            sound: "default",
            badge: 1,
          },
        },
      },
      tokens,
    }

    // Send the notification
    const response = await admin.messaging().sendMulticast(message)

    // Log the notification
    await prisma.notificationLog.create({
      data: {
        title,
        body,
        channel,
        sentBy: session.user.id,
        recipientCount: tokens.length,
        successCount: response.successCount,
        failureCount: response.failureCount,
      },
    })

    return NextResponse.json({
      success: true,
      sent: response.successCount,
      failed: response.failureCount,
    })
  } catch (error) {
    console.error("Error sending notification:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

