"use client"

import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { useTranslation } from "next-i18next"

interface ErrorAlertProps {
  title?: string
  error: string | Error | null | unknown
  retry?: () => void
  className?: string
}

export function ErrorAlert({ title, error, retry, className = "" }: ErrorAlertProps) {
  const { t } = useTranslation("common")

  if (!error) return null

  const errorMessage =
    error instanceof Error ? error.message : typeof error === "string" ? error : t("error.unexpected")

  return (
    <Alert variant="destructive" className={className}>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{title || t("error.title")}</AlertTitle>
      <div className="flex items-start justify-between gap-2">
        <AlertDescription>{errorMessage}</AlertDescription>
        {retry && (
          <Button
            variant="destructive"
            size="sm"
            className="mt-2 bg-destructive/20 hover:bg-destructive/30"
            onClick={retry}
          >
            {t("error.retry")}
          </Button>
        )}
      </div>
    </Alert>
  )
}

