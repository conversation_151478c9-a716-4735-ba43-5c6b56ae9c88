"use client"

import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { AlertTriangle } from "lucide-react"
import { useTranslation } from "next-i18next"

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const { t } = useTranslation("common")

  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Global error:", error)
  }, [error])

  return (
    <html>
      <body>
        <div className="flex flex-col items-center justify-center min-h-screen p-6">
          <div className="w-full max-w-md p-8 border rounded-lg shadow-lg bg-background">
            <div className="flex flex-col items-center text-center">
              <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
              <h1 className="text-2xl font-bold mb-2">{t("error.appError")}</h1>
              <p className="text-muted-foreground mb-6">{t("error.appErrorDescription")}</p>
              <div className="flex gap-4">
                <Button onClick={() => reset()} variant="default">
                  {t("error.tryAgain")}
                </Button>
                <Button onClick={() => (window.location.href = "/")} variant="outline">
                  {t("error.returnHome")}
                </Button>
              </div>
              {process.env.NODE_ENV === "development" && (
                <details className="mt-6 text-left w-full">
                  <summary className="cursor-pointer font-medium">{t("error.details")}</summary>
                  <pre className="mt-2 text-xs overflow-auto p-2 bg-muted rounded">
                    {error.message}
                    {error.stack}
                  </pre>
                </details>
              )}
            </div>
          </div>
        </div>
      </body>
    </html>
  )
}

