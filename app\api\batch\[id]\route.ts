import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { ApiError } from "@/lib/api-error"

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const batchOperation = await prisma.batchOperation.findUnique({
      where: { id: params.id },
    })

    if (!batchOperation) {
      throw ApiError.notFound("Batch operation not found")
    }

    // Check if user has permission to view this batch operation
    if (batchOperation.userId !== session.user.id && session.user.role !== "ADMIN") {
      throw ApiError.forbidden("You do not have permission to view this batch operation")
    }

    return NextResponse.json(batchOperation)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Batch operation error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

