"use client"

import { useState, useCallback, useEffect } from "react"
import { createUndoRedoManager, type UndoRedoState } from "@/lib/undo-redo-manager"

export function useUndoRedo<T>(
  initialState: T,
  options?: {
    maxHistorySize?: number
    onUndo?: (state: T) => void
    onRedo?: (state: T) => void
    onChange?: (state: T) => void
  },
) {
  const [manager] = useState(() => createUndoRedoManager<T>(initialState, options?.maxHistorySize))
  const [state, setState] = useState<UndoRedoState<T>>(manager[0])
  const actions = manager[1]

  // Update the local state when the manager state changes
  useEffect(() => {
    setState(manager[0])
  }, [manager])

  const undo = useCallback(() => {
    actions.undo()
    setState({ ...manager[0] })
    if (options?.onUndo) {
      options.onUndo(manager[0].present)
    }
    if (options?.onChange) {
      options.onChange(manager[0].present)
    }
  }, [actions, manager, options])

  const redo = useCallback(() => {
    actions.redo()
    setState({ ...manager[0] })
    if (options?.onRedo) {
      options.onRedo(manager[0].present)
    }
    if (options?.onChange) {
      options.onChange(manager[0].present)
    }
  }, [actions, manager, options])

  const update = useCallback(
    (newState: T) => {
      actions.update(newState)
      setState({ ...manager[0] })
      if (options?.onChange) {
        options.onChange(newState)
      }
    },
    [actions, manager, options],
  )

  const reset = useCallback(
    (newState: T) => {
      actions.reset(newState)
      setState({ ...manager[0] })
      if (options?.onChange) {
        options.onChange(newState)
      }
    },
    [actions, manager, options],
  )

  // Add keyboard shortcut support
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if the target is an input or textarea
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      // Undo: Ctrl/Cmd + Z
      if ((e.ctrlKey || e.metaKey) && e.key === "z" && !e.shiftKey) {
        e.preventDefault()
        if (actions.canUndo) {
          undo()
        }
      }

      // Redo: Ctrl/Cmd + Shift + Z or Ctrl/Cmd + Y
      if (((e.ctrlKey || e.metaKey) && e.key === "z" && e.shiftKey) || ((e.ctrlKey || e.metaKey) && e.key === "y")) {
        e.preventDefault()
        if (actions.canRedo) {
          redo()
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [actions.canRedo, actions.canUndo, redo, undo])

  return {
    state: state.present,
    history: state,
    canUndo: actions.canUndo,
    canRedo: actions.canRedo,
    undo,
    redo,
    update,
    reset,
  }
}

