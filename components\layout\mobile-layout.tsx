"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Menu, ChevronLeft, Home, Package, Users, ShoppingCart, BarChart2, Settings } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useMobile } from "@/hooks/use-mobile"

interface MobileNavItem {
  href: string
  label: string
  icon: React.ReactNode
}

export function MobileLayout({ children }: { children: React.ReactNode }) {
  const isMobile = useMobile()
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  const navItems: MobileNavItem[] = [
    { href: "/dashboard", label: "Dashboard", icon: <Home className="h-5 w-5" /> },
    { href: "/products", label: "Products", icon: <Package className="h-5 w-5" /> },
    { href: "/customers", label: "Customers", icon: <Users className="h-5 w-5" /> },
    { href: "/pos", label: "Point of Sale", icon: <ShoppingCart className="h-5 w-5" /> },
    { href: "/reports", label: "Reports", icon: <BarChart2 className="h-5 w-5" /> },
    { href: "/settings", label: "Settings", icon: <Settings className="h-5 w-5" /> },
  ]

  // Close the menu when navigating
  useEffect(() => {
    setIsOpen(false)
  }, [pathname])

  if (!isMobile) {
    return <>{children}</>
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="sticky top-0 z-40 bg-background border-b">
        <div className="flex h-16 items-center px-4">
          {pathname !== "/dashboard" && (
            <Button
              variant="ghost"
              size="icon"
              className="mr-2"
              onClick={() => window.history.back()}
              aria-label="Go back"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
          )}

          <div className="flex-1 text-center font-semibold">
            {navItems.find((item) => pathname.startsWith(item.href))?.label || "StockSync"}
          </div>

          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" aria-label="Menu">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[250px] sm:w-[300px]">
              <nav className="flex flex-col gap-4 mt-8">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 px-3 py-4 text-base rounded-md",
                      pathname.startsWith(item.href) ? "bg-primary/10 text-primary font-medium" : "hover:bg-muted",
                    )}
                  >
                    {item.icon}
                    {item.label}
                  </Link>
                ))}
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      <main className="flex-1 p-4 pb-20">{children}</main>

      <nav className="fixed bottom-0 left-0 right-0 z-40 bg-background border-t">
        <div className="flex justify-around items-center h-16">
          {navItems.slice(0, 5).map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center w-full h-full",
                pathname.startsWith(item.href) ? "text-primary" : "text-muted-foreground",
              )}
              aria-label={item.label}
            >
              {item.icon}
              <span className="text-xs mt-1">{item.label}</span>
            </Link>
          ))}
        </div>
      </nav>
    </div>
  )
}

