"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  Pie,
  Pie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
} from "@/components/ui/chart"

interface CustomerReportChartProps {
  data: any
  isLoading: boolean
}

export function CustomerReportChart({ data, isLoading }: CustomerReportChartProps) {
  const [chartType, setChartType] = useState<"line" | "bar" | "pie">("bar")

  if (isLoading) {
    return <Skeleton className="w-full h-full min-h-[300px]" />
  }

  if (!data?.chartData || data.chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-full min-h-[300px] text-muted-foreground">
        No data available for the selected period
      </div>
    )
  }

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8", "#82ca9d"]

  return (
    <div className="space-y-4">
      <Tabs value={chartType} onValueChange={(value) => setChartType(value as any)}>
        <TabsList className="grid grid-cols-3 w-[300px]">
          <TabsTrigger value="bar">Bar</TabsTrigger>
          <TabsTrigger value="line">Line</TabsTrigger>
          <TabsTrigger value="pie">Pie</TabsTrigger>
        </TabsList>

        <TabsContent value="bar" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="orders" fill="#3b82f6" name="Orders" />
              <Bar dataKey="spend" fill="#10b981" name="Total Spend ($)" />
            </BarChart>
          </ResponsiveContainer>
        </TabsContent>

        <TabsContent value="line" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data.timeSeriesData || data.chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="newCustomers" stroke="#3b82f6" name="New Customers" />
              <Line type="monotone" dataKey="activeCustomers" stroke="#10b981" name="Active Customers" />
            </LineChart>
          </ResponsiveContainer>
        </TabsContent>

        <TabsContent value="pie" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={
                  data.pieData || [
                    { name: "New Customers", value: data.metrics.newCustomers },
                    { name: "Returning Customers", value: data.metrics.totalCustomers - data.metrics.newCustomers },
                  ]
                }
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {(data.pieData || []).map((entry: any, index: number) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </TabsContent>
      </Tabs>
    </div>
  )
}

