"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useSession } from "next-auth/react"
import type { Role } from "@prisma/client"

export function MainNav() {
  const pathname = usePathname()
  const { data: session } = useSession()

  const userRole = session?.user?.role as Role | undefined

  const isAdmin = userRole === "ADMIN"
  const isManagerOrAdmin = userRole === "ADMIN" || userRole === "MANAGER"

  const routes = [
    {
      href: "/dashboard",
      label: "Dashboard",
      active: pathname === "/dashboard",
    },
    {
      href: "/pos",
      label: "Point of Sale",
      active: pathname === "/pos",
    },
    {
      href: "/inventory",
      label: "Inventory",
      active: pathname.startsWith("/inventory"),
    },
    {
      href: "/dashboard/reports",
      label: "Reports",
      active: pathname.startsWith("/dashboard/reports"),
      show: isManagerOrAdmin,
    },
    {
      href: "/dashboard/users",
      label: "Users",
      active: pathname.startsWith("/dashboard/users"),
      show: isAdmin,
    },
    {
      href: "/dashboard/settings",
      label: "Settings",
      active: pathname.startsWith("/dashboard/settings"),
      show: isAdmin,
    },
  ]

  return (
    <nav className="flex items-center space-x-4 lg:space-x-6">
      {routes
        .filter((route) => route.show !== false)
        .map((route) => (
          <Link
            key={route.href}
            href={route.href}
            className={cn(
              "text-sm font-medium transition-colors hover:text-primary",
              route.active ? "text-primary" : "text-muted-foreground",
            )}
          >
            {route.label}
          </Link>
        ))}
    </nav>
  )
}

