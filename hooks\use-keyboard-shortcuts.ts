"use client"

import { useEffect, useCallback, useState } from "react"
import { useToast } from "@/hooks/use-toast"
import { useTranslation } from "next-i18next"

export type KeyboardShortcut = {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  action: () => void
  description: string
  scope?: string
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[], enabled = true) {
  const { toast } = useToast()
  const { t } = useTranslation("common")
  const [showShortcutsHelp, setShowShortcutsHelp] = useState(false)

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return

      // Don't trigger shortcuts when typing in input fields
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return
      }

      // Special case for help shortcut (?)
      if (event.key === "?" && !event.ctrlKey && !event.altKey && !event.metaKey) {
        event.preventDefault()
        setShowShortcutsHelp(true)
        return
      }

      for (const shortcut of shortcuts) {
        if (
          event.key.toLowerCase() === shortcut.key.toLowerCase() &&
          !!event.ctrlKey === !!shortcut.ctrlKey &&
          !!event.altKey === !!shortcut.altKey &&
          !!event.shiftKey === !!shortcut.shiftKey &&
          !!event.metaKey === !!shortcut.metaKey
        ) {
          event.preventDefault()
          shortcut.action()

          // Show toast notification for the executed shortcut
          toast({
            title: t("shortcuts.executed"),
            description: shortcut.description,
            duration: 2000,
          })

          return
        }
      }
    },
    [shortcuts, enabled, toast, t],
  )

  useEffect(() => {
    if (enabled) {
      window.addEventListener("keydown", handleKeyDown)
    }

    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [handleKeyDown, enabled])

  // Format shortcuts for display
  const getFormattedShortcuts = useCallback(() => {
    return shortcuts.map((shortcut) => {
      const keys = []

      if (shortcut.ctrlKey) keys.push("Ctrl")
      if (shortcut.altKey) keys.push("Alt")
      if (shortcut.shiftKey) keys.push("Shift")
      if (shortcut.metaKey) keys.push("⌘")

      keys.push(shortcut.key.toUpperCase())

      return {
        keys: keys.join(" + "),
        description: shortcut.description,
        scope: shortcut.scope || t("shortcuts.global"),
      }
    })
  }, [shortcuts, t])

  // Add the help shortcut
  const allShortcuts = [
    ...shortcuts,
    {
      key: "?",
      action: () => setShowShortcutsHelp(true),
      description: t("shortcuts.showHelp"),
      scope: t("shortcuts.global"),
    },
  ]

  return {
    shortcuts: allShortcuts,
    formattedShortcuts: getFormattedShortcuts(),
    showShortcutsHelp,
    setShowShortcutsHelp,
  }
}

