"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"
import { returnSchema, type ReturnFormValues } from "@/lib/validations/return-schema"
import { formatCurrency } from "@/lib/utils"

interface ReturnFormProps {
  order: any
  onSuccess?: () => void
}

export function ReturnForm({ order, onSuccess }: ReturnFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [returnItems, setReturnItems] = useState<any[]>([])

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ReturnFormValues>({
    resolver: zodResolver(returnSchema),
    defaultValues: {
      orderId: order.id,
      customerId: order.customerId,
      reason: "",
      notes: "",
      items: [],
    },
  })

  // Initialize available items from order
  useEffect(() => {
    if (order && order.items) {
      const availableItems = order.items.map((item: any) => ({
        orderItemId: item.id,
        productId: item.productId,
        variantId: item.variantId,
        productName: item.product.name,
        variantName: item.variant ? `${item.variant.sku}` : null,
        maxQuantity: item.quantity,
        unitPrice: item.unitPrice,
        quantity: 0,
        reason: "",
        condition: "GOOD",
        refundAmount: 0,
      }))
      setReturnItems(availableItems)
    }
  }, [order])

  // Update form value when returnItems change
  useEffect(() => {
    const validItems = returnItems.filter((item) => item.quantity > 0)
    setValue("items", validItems)
  }, [returnItems, setValue])

  const addReturnItem = (index: number, quantity: number) => {
    const updatedItems = [...returnItems]
    updatedItems[index].quantity = quantity
    setReturnItems(updatedItems)
  }

  const updateReturnItemReason = (index: number, reason: string) => {
    const updatedItems = [...returnItems]
    updatedItems[index].reason = reason
    setReturnItems(updatedItems)
  }

  const updateReturnItemCondition = (index: number, condition: string) => {
    const updatedItems = [...returnItems]
    updatedItems[index].condition = condition
    setReturnItems(updatedItems)
  }

  const updateReturnItemRefund = (index: number, refundAmount: number) => {
    const updatedItems = [...returnItems]
    updatedItems[index].refundAmount = refundAmount
    setReturnItems(updatedItems)
  }

  const onSubmit = async (data: ReturnFormValues) => {
    try {
      setIsSubmitting(true)

      // Filter out items with quantity 0
      data.items = data.items.filter((item) => item.quantity > 0)

      if (data.items.length === 0) {
        toast({
          title: "Error",
          description: "Please add at least one item to return",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      const response = await fetch("/api/returns", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to create return")
      }

      const result = await response.json()

      toast({
        title: "Return Created",
        description: `Return #${result.returnNumber} has been created successfully.`,
      })

      if (onSuccess) {
        onSuccess()
      } else {
        router.push(`/dashboard/returns/${result.id}`)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create return",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        <CardHeader>
          <CardTitle>Create Return</CardTitle>
          <CardDescription>Create a return for Order #{order.orderNumber}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">Return Reason</Label>
              <Select onValueChange={(value) => setValue("reason", value)} defaultValue="">
                <SelectTrigger id="reason" className="w-full">
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DAMAGED">Damaged Product</SelectItem>
                  <SelectItem value="DEFECTIVE">Defective Product</SelectItem>
                  <SelectItem value="WRONG_ITEM">Wrong Item Received</SelectItem>
                  <SelectItem value="NOT_AS_DESCRIBED">Not As Described</SelectItem>
                  <SelectItem value="UNWANTED">Unwanted Product</SelectItem>
                  <SelectItem value="OTHER">Other</SelectItem>
                </SelectContent>
              </Select>
              {errors.reason && <p className="text-sm text-red-500 mt-1">{errors.reason.message}</p>}
            </div>

            <div>
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea id="notes" placeholder="Additional details about the return" {...register("notes")} />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Return Items</h3>
            <div className="space-y-4">
              {returnItems.map((item, index) => (
                <div key={index} className="border rounded-md p-4">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h4 className="font-medium">{item.productName}</h4>
                      {item.variantName && <p className="text-sm text-muted-foreground">Variant: {item.variantName}</p>}
                      <p className="text-sm">Unit Price: {formatCurrency(item.unitPrice)}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Label htmlFor={`quantity-${index}`} className="sr-only">
                        Quantity
                      </Label>
                      <Input
                        id={`quantity-${index}`}
                        type="number"
                        min="0"
                        max={item.maxQuantity}
                        value={item.quantity}
                        onChange={(e) => addReturnItem(index, Number.parseInt(e.target.value))}
                        className="w-20"
                      />
                      <span className="text-sm text-muted-foreground">/ {item.maxQuantity}</span>
                    </div>
                  </div>

                  {item.quantity > 0 && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor={`reason-${index}`}>Item Reason</Label>
                        <Select onValueChange={(value) => updateReturnItemReason(index, value)} defaultValue="">
                          <SelectTrigger id={`reason-${index}`}>
                            <SelectValue placeholder="Select reason" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DAMAGED">Damaged</SelectItem>
                            <SelectItem value="DEFECTIVE">Defective</SelectItem>
                            <SelectItem value="WRONG_ITEM">Wrong Item</SelectItem>
                            <SelectItem value="NOT_AS_DESCRIBED">Not As Described</SelectItem>
                            <SelectItem value="UNWANTED">Unwanted</SelectItem>
                            <SelectItem value="OTHER">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor={`condition-${index}`}>Item Condition</Label>
                        <Select onValueChange={(value) => updateReturnItemCondition(index, value)} defaultValue="GOOD">
                          <SelectTrigger id={`condition-${index}`}>
                            <SelectValue placeholder="Select condition" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="NEW">New/Unopened</SelectItem>
                            <SelectItem value="GOOD">Good Condition</SelectItem>
                            <SelectItem value="USED">Used</SelectItem>
                            <SelectItem value="DAMAGED">Damaged</SelectItem>
                            <SelectItem value="DEFECTIVE">Defective</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor={`refund-${index}`}>Refund Amount</Label>
                        <Input
                          id={`refund-${index}`}
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.refundAmount}
                          onChange={(e) => updateReturnItemRefund(index, Number.parseFloat(e.target.value))}
                          className="w-full"
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {returnItems.length === 0 && (
                <p className="text-center text-muted-foreground py-4">No items available for return</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Return
          </Button>
        </CardFooter>
      </Card>
    </form>
  )
}

