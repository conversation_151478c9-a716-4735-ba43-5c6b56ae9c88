"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Smartphone, Mail, Shield } from "lucide-react"
import Image from "next/image"

interface MfaSetupProps {
  userId: string
  onComplete: () => void
}

export function MfaSetup({ userId, onComplete }: MfaSetupProps) {
  const [activeTab, setActiveTab] = useState("authenticator")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // Authenticator app state
  const [qrCode, setQrCode] = useState("")
  const [secret, setSecret] = useState("")
  const [authCode, setAuthCode] = useState("")

  // SMS state
  const [phoneNumber, setPhoneNumber] = useState("")
  const [smsCode, setSmsCode] = useState("")
  const [smsSent, setSmsSent] = useState(false)

  // Email state
  const [emailCode, setEmailCode] = useState("")
  const [emailSent, setEmailSent] = useState(false)

  // Recovery codes state
  const [recoveryCodes, setRecoveryCodes] = useState<string[]>([])
  const [showRecoveryCodes, setShowRecoveryCodes] = useState(false)

  // Generate authenticator setup
  useEffect(() => {
    if (activeTab === "authenticator") {
      generateAuthenticatorSetup()
    }
  }, [activeTab])

  const generateAuthenticatorSetup = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/auth/mfa/setup/authenticator", {
        method: "POST",
      })

      if (!response.ok) {
        throw new Error("Failed to generate authenticator setup")
      }

      const data = await response.json()
      setQrCode(data.qrCode)
      setSecret(data.secret)
    } catch (err) {
      setError("Failed to generate authenticator setup")
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const verifyAuthenticator = async () => {
    try {
      setLoading(true)
      setError("")

      const response = await fetch("/api/auth/mfa/verify/authenticator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: authCode,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to verify code")
      }

      setSuccess("Authenticator app verified successfully")
      generateRecoveryCodes()
    } catch (err: any) {
      setError(err.message || "Failed to verify code")
    } finally {
      setLoading(false)
    }
  }

  const sendSmsCode = async () => {
    try {
      setLoading(true)
      setError("")

      const response = await fetch("/api/auth/mfa/setup/sms", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          phoneNumber,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to send SMS code")
      }

      setSmsSent(true)
    } catch (err: any) {
      setError(err.message || "Failed to send SMS code")
    } finally {
      setLoading(false)
    }
  }

  const verifySmsCode = async () => {
    try {
      setLoading(true)
      setError("")

      const response = await fetch("/api/auth/mfa/verify/sms", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: smsCode,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to verify SMS code")
      }

      setSuccess("Phone number verified successfully")
      generateRecoveryCodes()
    } catch (err: any) {
      setError(err.message || "Failed to verify SMS code")
    } finally {
      setLoading(false)
    }
  }

  const sendEmailCode = async () => {
    try {
      setLoading(true)
      setError("")

      const response = await fetch("/api/auth/mfa/setup/email", {
        method: "POST",
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to send email code")
      }

      setEmailSent(true)
    } catch (err: any) {
      setError(err.message || "Failed to send email code")
    } finally {
      setLoading(false)
    }
  }

  const verifyEmailCode = async () => {
    try {
      setLoading(true)
      setError("")

      const response = await fetch("/api/auth/mfa/verify/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: emailCode,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to verify email code")
      }

      setSuccess("Email verified successfully")
      generateRecoveryCodes()
    } catch (err: any) {
      setError(err.message || "Failed to verify email code")
    } finally {
      setLoading(false)
    }
  }

  const generateRecoveryCodes = async () => {
    try {
      setLoading(true)

      const response = await fetch("/api/auth/mfa/recovery-codes", {
        method: "POST",
      })

      if (!response.ok) {
        throw new Error("Failed to generate recovery codes")
      }

      const data = await response.json()
      setRecoveryCodes(data.codes)
      setShowRecoveryCodes(true)
    } catch (err) {
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const handleComplete = () => {
    onComplete()
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Set Up Two-Factor Authentication</CardTitle>
        <CardDescription>Add an extra layer of security to your account</CardDescription>
      </CardHeader>
      <CardContent>
        {showRecoveryCodes ? (
          <div className="space-y-4">
            <Alert>
              <AlertDescription>
                <p className="font-bold">Save these recovery codes!</p>
                <p>If you lose access to your authentication method, you can use these codes to sign in.</p>
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-2 gap-2">
              {recoveryCodes.map((code, index) => (
                <code key={index} className="bg-muted p-2 rounded text-center font-mono">
                  {code}
                </code>
              ))}
            </div>

            <Button onClick={handleComplete} className="w-full">
              I've saved my recovery codes
            </Button>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="authenticator">
                <Shield className="h-4 w-4 mr-2" />
                App
              </TabsTrigger>
              <TabsTrigger value="sms">
                <Smartphone className="h-4 w-4 mr-2" />
                SMS
              </TabsTrigger>
              <TabsTrigger value="email">
                <Mail className="h-4 w-4 mr-2" />
                Email
              </TabsTrigger>
            </TabsList>

            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="mb-4">
                <AlertDescription>{success}</AlertDescription>
              </Alert>
            )}

            <TabsContent value="authenticator" className="space-y-4">
              <div className="text-center">
                {qrCode ? (
                  <div className="mx-auto w-48 h-48 relative mb-4">
                    <Image
                      src={qrCode || "/placeholder.svg"}
                      alt="QR Code for authenticator app"
                      fill
                      className="object-contain"
                    />
                  </div>
                ) : (
                  <div className="flex justify-center items-center w-48 h-48 bg-muted mx-auto mb-4">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                )}

                {secret && (
                  <div className="mb-4">
                    <p className="text-sm text-muted-foreground mb-1">
                      If you can't scan the QR code, enter this code manually:
                    </p>
                    <code className="bg-muted p-2 rounded text-sm font-mono">{secret}</code>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="authCode">Enter the 6-digit code from your app</Label>
                <Input
                  id="authCode"
                  value={authCode}
                  onChange={(e) => setAuthCode(e.target.value)}
                  maxLength={6}
                  inputMode="numeric"
                  pattern="[0-9]*"
                />
              </div>

              <Button onClick={verifyAuthenticator} className="w-full" disabled={loading || authCode.length !== 6}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  "Verify"
                )}
              </Button>
            </TabsContent>

            <TabsContent value="sms" className="space-y-4">
              {!smsSent ? (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      placeholder="+****************"
                    />
                    <p className="text-sm text-muted-foreground">Enter your phone number including country code</p>
                  </div>

                  <Button onClick={sendSmsCode} className="w-full" disabled={loading || !phoneNumber}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      "Send Code"
                    )}
                  </Button>
                </>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="smsCode">Enter the 6-digit code sent to your phone</Label>
                    <Input
                      id="smsCode"
                      value={smsCode}
                      onChange={(e) => setSmsCode(e.target.value)}
                      maxLength={6}
                      inputMode="numeric"
                      pattern="[0-9]*"
                    />
                  </div>

                  <Button onClick={verifySmsCode} className="w-full" disabled={loading || smsCode.length !== 6}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      "Verify"
                    )}
                  </Button>

                  <Button variant="outline" onClick={() => setSmsSent(false)} className="w-full">
                    Change Phone Number
                  </Button>
                </>
              )}
            </TabsContent>

            <TabsContent value="email" className="space-y-4">
              {!emailSent ? (
                <>
                  <p className="text-sm text-muted-foreground mb-4">
                    We'll send a verification code to your email address on file.
                  </p>

                  <Button onClick={sendEmailCode} className="w-full" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      "Send Code"
                    )}
                  </Button>
                </>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="emailCode">Enter the 6-digit code sent to your email</Label>
                    <Input
                      id="emailCode"
                      value={emailCode}
                      onChange={(e) => setEmailCode(e.target.value)}
                      maxLength={6}
                      inputMode="numeric"
                      pattern="[0-9]*"
                    />
                  </div>

                  <Button onClick={verifyEmailCode} className="w-full" disabled={loading || emailCode.length !== 6}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      "Verify"
                    )}
                  </Button>

                  <Button variant="outline" onClick={() => setEmailSent(false)} className="w-full">
                    Resend Code
                  </Button>
                </>
              )}
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

