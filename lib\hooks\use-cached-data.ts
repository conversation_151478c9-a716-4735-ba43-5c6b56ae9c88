"use client"

import { useState, useEffect, useCallback } from "react"
import { useOfflineStore } from "@/lib/store"

interface CacheOptions<T> {
  key: string
  fetcher: () => Promise<T>
  initialData?: T
  revalidateOnFocus?: boolean
  revalidateOnReconnect?: boolean
  dedupingInterval?: number
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
}

interface CachedData<T> {
  data: T | undefined
  error: Error | null
  isLoading: boolean
  isValidating: boolean
  mutate: (data?: T | ((current: T | undefined) => T), shouldRevalidate?: boolean) => Promise<void>
}

// Simple in-memory cache
const CACHE = new Map<string, { data: any; timestamp: number }>()

export function useCachedData<T>({
  key,
  fetcher,
  initialData,
  revalidateOnFocus = true,
  revalidateOnReconnect = true,
  dedupingInterval = 2000, // 2 seconds
  onSuccess,
  onError,
}: CacheOptions<T>): CachedData<T> {
  const [data, setData] = useState<T | undefined>(initialData)
  const [error, setError] = useState<Error | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(!initialData)
  const [isValidating, setIsValidating] = useState<boolean>(false)
  const { isOffline } = useOfflineStore()

  const fetchData = useCallback(
    async (shouldUpdateLoading = true) => {
      // Skip fetching if offline
      if (isOffline) return

      // Check cache first
      const cached = CACHE.get(key)
      const now = Date.now()

      if (cached && now - cached.timestamp < dedupingInterval) {
        setData(cached.data)
        setIsLoading(false)
        return
      }

      if (shouldUpdateLoading) {
        setIsValidating(true)
      }

      try {
        const newData = await fetcher()

        // Update cache
        CACHE.set(key, { data: newData, timestamp: Date.now() })

        setData(newData)
        setError(null)

        if (onSuccess) {
          onSuccess(newData)
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)))

        if (onError) {
          onError(err instanceof Error ? err : new Error(String(err)))
        }
      } finally {
        setIsLoading(false)
        setIsValidating(false)
      }
    },
    [key, fetcher, dedupingInterval, isOffline, onSuccess, onError],
  )

  // Initial fetch
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Revalidate on focus
  useEffect(() => {
    if (!revalidateOnFocus) return

    const onFocus = () => {
      fetchData(false)
    }

    window.addEventListener("focus", onFocus)

    return () => {
      window.removeEventListener("focus", onFocus)
    }
  }, [fetchData, revalidateOnFocus])

  // Revalidate on reconnect
  useEffect(() => {
    if (!revalidateOnReconnect) return

    const onOnline = () => {
      fetchData(false)
    }

    window.addEventListener("online", onOnline)

    return () => {
      window.removeEventListener("online", onOnline)
    }
  }, [fetchData, revalidateOnReconnect])

  // Mutate function for optimistic updates
  const mutate = useCallback(
    async (dataOrUpdater?: T | ((current: T | undefined) => T), shouldRevalidate = true) => {
      if (typeof dataOrUpdater === "function") {
        const updater = dataOrUpdater as (current: T | undefined) => T
        const newData = updater(data)
        setData(newData)

        // Update cache
        CACHE.set(key, { data: newData, timestamp: Date.now() })
      } else if (dataOrUpdater !== undefined) {
        setData(dataOrUpdater)

        // Update cache
        CACHE.set(key, { data: dataOrUpdater, timestamp: Date.now() })
      }

      if (shouldRevalidate) {
        await fetchData(false)
      }
    },
    [data, fetchData, key],
  )

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
  }
}

