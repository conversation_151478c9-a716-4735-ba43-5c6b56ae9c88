"use client"

import { useSession } from "next-auth/react"
import type { ReactNode } from "react"
import type { Permission } from "@/lib/auth/permissions"

interface PermissionGuardProps {
  children: ReactNode
  permission: Permission
  fallback?: ReactNode
}

export function PermissionGuard({ children, permission, fallback = null }: PermissionGuardProps) {
  const { data: session } = useSession()

  if (!session?.user) {
    return fallback
  }

  const hasPermission = session.user.permissions?.includes(permission)

  if (!hasPermission) {
    return fallback
  }

  return <>{children}</>
}

