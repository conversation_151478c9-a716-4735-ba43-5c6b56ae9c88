import crypto from "crypto"
import { db } from "../db"

/**
 * Encryption algorithm and settings
 */
const ALGORITHM = "aes-256-gcm"
const IV_LENGTH = 16 // For AES, this is always 16 bytes
const KEY_LENGTH = 32 // 256 bits
const AUTH_TAG_LENGTH = 16

export interface EncryptionKey {
  id: string
  key: string
  createdAt: Date
  active: boolean
}

export class EncryptionService {
  /**
   * Initialize the encryption system
   */
  static async initialize(): Promise<void> {
    // Check if we have any encryption keys
    const keys = await db.encryptionKeys.findMany({
      where: { active: true },
    })

    if (keys.length === 0) {
      // Generate a new encryption key
      await this.generateNewKey()
    }
  }

  /**
   * Generate a new encryption key
   */
  static async generateNewKey(setAsActive = true): Promise<EncryptionKey> {
    // Generate a random key
    const key = crypto.randomBytes(KEY_LENGTH).toString("hex")

    // If this is the active key, deactivate all other keys
    if (setAsActive) {
      await db.encryptionKeys.updateMany({
        where: { active: true },
        data: { active: false },
      })
    }

    // Store the key in the database
    return db.encryptionKeys.create({
      data: {
        key,
        createdAt: new Date(),
        active: setAsActive,
      },
    })
  }

  /**
   * Get the active encryption key
   */
  static async getActiveKey(): Promise<EncryptionKey> {
    const key = await db.encryptionKeys.findFirst({
      where: { active: true },
    })

    if (!key) {
      throw new Error("No active encryption key found")
    }

    return key
  }

  /**
   * Get a specific encryption key by ID
   */
  static async getKeyById(id: string): Promise<EncryptionKey | null> {
    return db.encryptionKeys.findUnique({
      where: { id },
    })
  }

  /**
   * Encrypt data using the active key
   */
  static async encrypt(data: string): Promise<{
    encryptedData: string
    iv: string
    authTag: string
    keyId: string
  }> {
    const activeKey = await this.getActiveKey()

    // Generate a random initialization vector
    const iv = crypto.randomBytes(IV_LENGTH)

    // Create cipher
    const cipher = crypto.createCipheriv(ALGORITHM, Buffer.from(activeKey.key, "hex"), iv)

    // Encrypt the data
    let encrypted = cipher.update(data, "utf8", "hex")
    encrypted += cipher.final("hex")

    // Get the authentication tag
    const authTag = cipher.getAuthTag().toString("hex")

    return {
      encryptedData: encrypted,
      iv: iv.toString("hex"),
      authTag,
      keyId: activeKey.id,
    }
  }

  /**
   * Decrypt data using the specified key
   */
  static async decrypt(encryptedData: string, iv: string, authTag: string, keyId: string): Promise<string> {
    // Get the key
    const key = await this.getKeyById(keyId)

    if (!key) {
      throw new Error(`Encryption key with ID ${keyId} not found`)
    }

    // Create decipher
    const decipher = crypto.createDecipheriv(ALGORITHM, Buffer.from(key.key, "hex"), Buffer.from(iv, "hex"))

    // Set the authentication tag
    decipher.setAuthTag(Buffer.from(authTag, "hex"))

    // Decrypt the data
    let decrypted = decipher.update(encryptedData, "hex", "utf8")
    decrypted += decipher.final("utf8")

    return decrypted
  }

  /**
   * Rotate encryption keys - generate a new key and re-encrypt data
   */
  static async rotateKeys(): Promise<void> {
    // Generate a new key
    const newKey = await this.generateNewKey()

    // Re-encrypt all sensitive data with the new key
    // This would involve fetching all encrypted data, decrypting it,
    // and re-encrypting it with the new key

    // For example, re-encrypt customer PII
    const customers = await db.customers.findMany({
      select: {
        id: true,
        encryptedEmail: true,
        emailIv: true,
        emailAuthTag: true,
        emailKeyId: true,
        encryptedPhone: true,
        phoneIv: true,
        phoneAuthTag: true,
        phoneKeyId: true,
        // Add other encrypted fields
      },
    })

    for (const customer of customers) {
      // Re-encrypt email if it exists
      if (customer.encryptedEmail) {
        const email = await this.decrypt(
          customer.encryptedEmail,
          customer.emailIv,
          customer.emailAuthTag,
          customer.emailKeyId,
        )

        const {
          encryptedData: newEncryptedEmail,
          iv: newEmailIv,
          authTag: newEmailAuthTag,
          keyId: newEmailKeyId,
        } = await this.encrypt(email)

        await db.customers.update({
          where: { id: customer.id },
          data: {
            encryptedEmail: newEncryptedEmail,
            emailIv: newEmailIv,
            emailAuthTag: newEmailAuthTag,
            emailKeyId: newEmailKeyId,
          },
        })
      }

      // Re-encrypt phone if it exists
      if (customer.encryptedPhone) {
        const phone = await this.decrypt(
          customer.encryptedPhone,
          customer.phoneIv,
          customer.phoneAuthTag,
          customer.phoneKeyId,
        )

        const {
          encryptedData: newEncryptedPhone,
          iv: newPhoneIv,
          authTag: newPhoneAuthTag,
          keyId: newPhoneKeyId,
        } = await this.encrypt(phone)

        await db.customers.update({
          where: { id: customer.id },
          data: {
            encryptedPhone: newEncryptedPhone,
            phoneIv: newPhoneIv,
            phoneAuthTag: newPhoneAuthTag,
            phoneKeyId: newPhoneKeyId,
          },
        })
      }

      // Re-encrypt other sensitive fields...
    }

    // Similarly, re-encrypt other sensitive data types
    // ...

    console.log(`Key rotation complete. New key ID: ${newKey.id}`)
  }
}

