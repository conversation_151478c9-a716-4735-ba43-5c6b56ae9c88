"use client"

import type React from "react"
import { useMobile } from "@/hooks/use-mobile"
import ProductSearch from "./product-search"
import ProductGrid from "./product-grid"
import Cart from "./cart"
import { Button } from "@/components/ui/button"
import { ShoppingCart, X } from "lucide-react"
import { useState } from "react"

interface PosLayoutProps {
  handleAddToCart: (productId: string) => void
}

const PosLayout: React.FC<PosLayoutProps> = ({ handleAddToCart }) => {
  const isMobile = useMobile()
  const [isCartOpen, setIsCartOpen] = useState(false)

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen)
  }

  if (isMobile) {
    return (
      <div className="flex flex-col h-full relative">
        <div className="w-full p-4 pb-20">
          <div className="mb-4">
            <ProductSearch onProductSelect={handleAddToCart} />
          </div>
          <ProductGrid onProductSelect={handleAddToCart} />
        </div>

        {/* Floating cart button */}
        <Button className="fixed bottom-4 right-4 z-10 rounded-full h-14 w-14 shadow-lg" onClick={toggleCart}>
          <ShoppingCart className="h-6 w-6" />
        </Button>

        {/* Mobile cart slide-in */}
        <div
          className={`
            fixed inset-0 bg-background/80 backdrop-blur-sm z-20 transition-opacity duration-300
            ${isCartOpen ? "opacity-100" : "opacity-0 pointer-events-none"}
          `}
          onClick={toggleCart}
        />

        <div
          className={`
            fixed inset-y-0 right-0 w-full max-w-md bg-card z-30 shadow-xl
            transform transition-transform duration-300 ease-in-out
            ${isCartOpen ? "translate-x-0" : "translate-x-full"}
          `}
        >
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-lg font-semibold">Shopping Cart</h2>
            <Button variant="ghost" size="icon" onClick={toggleCart}>
              <X className="h-5 w-5" />
            </Button>
          </div>
          <div className="h-full overflow-auto">
            <Cart />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col md:flex-row h-full">
      <div className="w-full md:w-2/3 p-4 overflow-auto">
        <div className="mb-4">
          <ProductSearch onProductSelect={handleAddToCart} />
        </div>
        <ProductGrid onProductSelect={handleAddToCart} />
      </div>
      <div className="w-full md:w-1/3 border-l bg-card">
        <Cart />
      </div>
    </div>
  )
}

export default PosLayout

