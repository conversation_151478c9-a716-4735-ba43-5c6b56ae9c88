"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Clock,
  FileText,
  FileSpreadsheet,
  FileIcon as FilePdf,
  Loader2,
  CheckCircle,
  XCircle,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"

export function ScheduledReportsList() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [schedules, setSchedules] = useState<any[]>([])

  useEffect(() => {
    fetchSchedules()
  }, [])

  const fetchSchedules = async () => {
    setIsLoading(true)
    try {
      const data = await fetchApi("/api/reports/scheduled")
      setSchedules(data)
    } catch (error) {
      console.error("Error fetching schedules:", error)
      toast({
        title: "Error",
        description: "Failed to load scheduled reports",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteSchedule = async (scheduleId: string) => {
    if (!confirm("Are you sure you want to delete this schedule?")) {
      return
    }

    try {
      await fetchApi(`/api/reports/scheduled/${scheduleId}`, {
        method: "DELETE",
      })

      toast({
        title: "Success",
        description: "Schedule deleted successfully",
      })

      fetchSchedules()
    } catch (error) {
      console.error("Error deleting schedule:", error)
      toast({
        title: "Error",
        description: "Failed to delete schedule",
        variant: "destructive",
      })
    }
  }

  // Format frequency display
  const formatFrequency = (schedule: any) => {
    const { frequency, dayOfWeek, dayOfMonth, hour, minute } = schedule

    const time = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`

    if (frequency === "daily") {
      return `Daily at ${time}`
    }

    if (frequency === "weekly") {
      const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
      return `Every ${days[dayOfWeek]} at ${time}`
    }

    if (frequency === "monthly") {
      return `Monthly on day ${dayOfMonth} at ${time}`
    }

    if (frequency === "quarterly") {
      return `Quarterly on day ${dayOfMonth} at ${time}`
    }

    return frequency
  }

  // Format format icon
  const formatIcon = (format: string) => {
    switch (format) {
      case "csv":
        return <FileText className="h-4 w-4" />
      case "excel":
        return <FileSpreadsheet className="h-4 w-4" />
      case "pdf":
        return <FilePdf className="h-4 w-4" />
      default:
        return null
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button asChild>
          <Link href="/dashboard/reports/scheduled/new">
            <Plus className="mr-2 h-4 w-4" />
            New Schedule
          </Link>
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : schedules.length > 0 ? (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Schedule Name</TableHead>
                <TableHead>Report</TableHead>
                <TableHead>Frequency</TableHead>
                <TableHead>Format</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {schedules.map((schedule) => (
                <TableRow key={schedule.id}>
                  <TableCell>
                    <div className="font-medium">{schedule.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {schedule.recipients.length} recipient{schedule.recipients.length !== 1 ? "s" : ""}
                    </div>
                  </TableCell>
                  <TableCell>{schedule.report?.name || "Unknown Report"}</TableCell>
                  <TableCell>{formatFrequency(schedule)}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {formatIcon(schedule.format)}
                      <span className="ml-2 capitalize">{schedule.format}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {schedule.active ? (
                      <Badge variant="default" className="flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        Active
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <XCircle className="h-3 w-3" />
                        Inactive
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/reports/scheduled/${schedule.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteSchedule(schedule.id)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>No Scheduled Reports</CardTitle>
            <CardDescription>You haven't created any scheduled reports yet</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center py-6">
            <Clock className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-center text-muted-foreground mb-4">
              Scheduled reports allow you to automatically generate and email reports on a regular basis.
            </p>
            <Button asChild>
              <Link href="/dashboard/reports/scheduled/new">
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Schedule
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

