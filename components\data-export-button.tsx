"use client"

// Create a data export button component

import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Download } from "lucide-react"
import { exportToCSV, exportToExcel, exportToPDF } from "@/lib/utils/export-data"

interface DataExportButtonProps<T extends Record<string, any>> {
  data: T[]
  filename: string
  headers?: Record<keyof T, string>
  title?: string
}

export function DataExportButton<T extends Record<string, any>>({
  data,
  filename,
  headers,
  title,
}: DataExportButtonProps<T>) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => exportToCSV(data, filename, headers)}>Export as CSV</DropdownMenuItem>
        <DropdownMenuItem onClick={() => exportToExcel(data, filename, headers)}>Export as Excel</DropdownMenuItem>
        <DropdownMenuItem onClick={() => exportToPDF(data, filename, headers, title)}>Export as PDF</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

