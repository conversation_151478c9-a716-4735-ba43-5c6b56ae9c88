"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { ConsentType } from "@/lib/privacy/consent-manager"
import { updateConsent } from "@/app/actions/data-protection"
import { toast } from "@/components/ui/use-toast"

interface ConsentManagerProps {
  consents: {
    [key in ConsentType]?: boolean
  }
}

export function ConsentManager({ consents }: ConsentManagerProps) {
  const [formData, setFormData] = useState({
    [ConsentType.MARKETING_EMAIL]: consents[ConsentType.MARKETING_EMAIL] ?? false,
    [ConsentType.MARKETING_SMS]: consents[ConsentType.MARKETING_SMS] ?? false,
    [ConsentType.DATA_ANALYTICS]: consents[ConsentType.DATA_ANALYTICS] ?? false,
    [ConsentType.THIRD_PARTY_SHARING]: consents[ConsentType.THIRD_PARTY_SHARING] ?? false,
    [ConsentType.COOKIES_ANALYTICS]: consents[ConsentType.COOKIES_ANALYTICS] ?? false,
    [ConsentType.COOKIES_MARKETING]: consents[ConsentType.COOKIES_MARKETING] ?? false,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentConsent, setCurrentConsent] = useState<ConsentType | null>(null)

  const handleToggle = async (consentType: ConsentType) => {
    const newValue = !formData[consentType]

    setCurrentConsent(consentType)
    setIsSubmitting(true)

    try {
      await updateConsent(consentType, newValue)

      setFormData((prev) => ({
        ...prev,
        [consentType]: newValue,
      }))

      toast({
        title: "Consent Updated",
        description: `Your consent preferences for ${formatConsentType(consentType)} have been updated.`,
      })
    } catch (error) {
      console.error("Error updating consent:", error)
      toast({
        title: "Error",
        description: "Failed to update consent preferences. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setCurrentConsent(null)
    }
  }

  const formatConsentType = (type: ConsentType): string => {
    switch (type) {
      case ConsentType.MARKETING_EMAIL:
        return "Marketing Emails"
      case ConsentType.MARKETING_SMS:
        return "Marketing SMS"
      case ConsentType.DATA_ANALYTICS:
        return "Data Analytics"
      case ConsentType.THIRD_PARTY_SHARING:
        return "Third-Party Data Sharing"
      case ConsentType.COOKIES_ANALYTICS:
        return "Analytics Cookies"
      case ConsentType.COOKIES_MARKETING:
        return "Marketing Cookies"
      default:
        return type
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Consent Management</CardTitle>
        <CardDescription>Control how we use your data and communicate with you</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="marketingEmail">Marketing Emails</Label>
            <p className="text-sm text-muted-foreground">Receive promotional emails about our products and services</p>
          </div>
          <Switch
            id="marketingEmail"
            checked={formData[ConsentType.MARKETING_EMAIL]}
            onCheckedChange={() => handleToggle(ConsentType.MARKETING_EMAIL)}
            disabled={isSubmitting && currentConsent === ConsentType.MARKETING_EMAIL}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="marketingSms">Marketing SMS</Label>
            <p className="text-sm text-muted-foreground">
              Receive promotional text messages about our products and services
            </p>
          </div>
          <Switch
            id="marketingSms"
            checked={formData[ConsentType.MARKETING_SMS]}
            onCheckedChange={() => handleToggle(ConsentType.MARKETING_SMS)}
            disabled={isSubmitting && currentConsent === ConsentType.MARKETING_SMS}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="dataAnalytics">Data Analytics</Label>
            <p className="text-sm text-muted-foreground">Allow us to analyze your usage data to improve our services</p>
          </div>
          <Switch
            id="dataAnalytics"
            checked={formData[ConsentType.DATA_ANALYTICS]}
            onCheckedChange={() => handleToggle(ConsentType.DATA_ANALYTICS)}
            disabled={isSubmitting && currentConsent === ConsentType.DATA_ANALYTICS}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="thirdPartySharing">Third-Party Sharing</Label>
            <p className="text-sm text-muted-foreground">Allow us to share your data with trusted partners</p>
          </div>
          <Switch
            id="thirdPartySharing"
            checked={formData[ConsentType.THIRD_PARTY_SHARING]}
            onCheckedChange={() => handleToggle(ConsentType.THIRD_PARTY_SHARING)}
            disabled={isSubmitting && currentConsent === ConsentType.THIRD_PARTY_SHARING}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="cookiesAnalytics">Analytics Cookies</Label>
            <p className="text-sm text-muted-foreground">Allow cookies that help us analyze how you use our website</p>
          </div>
          <Switch
            id="cookiesAnalytics"
            checked={formData[ConsentType.COOKIES_ANALYTICS]}
            onCheckedChange={() => handleToggle(ConsentType.COOKIES_ANALYTICS)}
            disabled={isSubmitting && currentConsent === ConsentType.COOKIES_ANALYTICS}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="cookiesMarketing">Marketing Cookies</Label>
            <p className="text-sm text-muted-foreground">
              Allow cookies that are used to deliver advertisements relevant to you
            </p>
          </div>
          <Switch
            id="cookiesMarketing"
            checked={formData[ConsentType.COOKIES_MARKETING]}
            onCheckedChange={() => handleToggle(ConsentType.COOKIES_MARKETING)}
            disabled={isSubmitting && currentConsent === ConsentType.COOKIES_MARKETING}
          />
        </div>
      </CardContent>
    </Card>
  )
}

