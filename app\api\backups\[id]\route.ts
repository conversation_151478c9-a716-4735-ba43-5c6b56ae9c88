import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { deleteBackup, restoreBackup } from "@/lib/services/backup-service"
import fs from "fs"
import path from "path"
import { prisma } from "@/lib/prisma"

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params
    const result = await deleteBackup(id)

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 })
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error in delete backup API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params
    const result = await restoreBackup(id)

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 })
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error in restore backup API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params

    // Get backup metadata
    const backup = await prisma.backup.findUnique({
      where: { id },
    })

    if (!backup) {
      return NextResponse.json({ error: "Backup not found" }, { status: 404 })
    }

    // Check if file exists
    if (!fs.existsSync(backup.filePath)) {
      return NextResponse.json({ error: "Backup file not found" }, { status: 404 })
    }

    // Set headers for file download
    const filename = path.basename(backup.filePath)
    const headers = new Headers()
    headers.set("Content-Disposition", `attachment; filename=${filename}`)
    headers.set("Content-Type", "application/gzip")

    // Stream the file
    const fileStream = fs.createReadStream(backup.filePath)

    return new NextResponse(fileStream as any, {
      headers,
    })
  } catch (error) {
    console.error("Error in download backup API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

