import { notFound } from "next/navigation"
import { ReturnForm } from "@/components/returns/return-form"
import prisma from "@/lib/prisma"

interface CreateReturnPageProps {
  params: {
    id: string
  }
}

async function getOrder(id: string) {
  const order = await prisma.order.findUnique({
    where: { id },
    include: {
      customer: true,
      items: {
        include: {
          product: true,
          variant: true,
        },
      },
    },
  })

  if (!order) {
    notFound()
  }

  return order
}

export default async function CreateReturnPage({ params }: CreateReturnPageProps) {
  const order = await getOrder(params.id)

  return (
    <div className="space-y-4">
      <h2 className="text-3xl font-bold tracking-tight">Create Return</h2>
      <ReturnForm order={order} />
    </div>
  )
}

