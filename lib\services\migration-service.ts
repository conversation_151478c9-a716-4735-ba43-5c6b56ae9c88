import { prisma } from "@/lib/prisma"
import { CsvProcessor } from "@/lib/utils/csv-processor"
import {
  productImportSchema,
  customerImportSchema,
  supplierImportSchema,
  categoryImportSchema,
  orderImportSchema,
} from "@/lib/validations/import-schemas"
import { exportToCSV, exportToExcel, exportToPDF } from "@/lib/utils/export-data"
import type { Readable } from "stream"

// Supported entity types for import/export
export type EntityType = "products" | "customers" | "suppliers" | "categories" | "orders"

// Supported file formats
export type FileFormat = "csv" | "excel" | "json" | "pdf"

// Import options
export interface ImportOptions {
  entityType: EntityType
  file: File | Buffer | Readable
  userId: string
  options?: {
    updateExisting?: boolean
    skipErrors?: boolean
    batchSize?: number
    validateOnly?: boolean
  }
}

// Export options
export interface ExportOptions {
  entityType: EntityType
  format: FileFormat
  filters?: Record<string, any>
  fields?: string[]
  userId: string
}

/**
 * Service for handling data migration (import/export)
 */
export class MigrationService {
  /**
   * Import data from a file
   */
  static async importData(options: ImportOptions) {
    const { entityType, file, userId, options: importOptions = {} } = options
    const { updateExisting = true, skipErrors = false, batchSize = 100, validateOnly = false } = importOptions

    // Create a batch operation record
    const batchOperation = await prisma.batchOperation.create({
      data: {
        name: `Import ${entityType}`,
        type: "IMPORT",
        status: "PENDING",
        userId,
      },
    })

    try {
      // Update batch operation status
      await prisma.batchOperation.update({
        where: { id: batchOperation.id },
        data: { status: "PROCESSING" },
      })

      // Get the appropriate schema based on entity type
      const schema = this.getSchemaForEntity(entityType)

      // Process the file
      const csvProcessor = new CsvProcessor({
        schema,
        chunkSize: batchSize,
        onProgress: async (progress) => {
          // Update progress
          await prisma.batchOperation.update({
            where: { id: batchOperation.id },
            data: {
              processedItems: Math.floor(progress * 100),
            },
          })
        },
      })

      // Parse the file
      const result = await csvProcessor.parseFile(file as File)

      // If validate only, return validation results
      if (validateOnly) {
        await prisma.batchOperation.update({
          where: { id: batchOperation.id },
          data: {
            status: "COMPLETED",
            totalItems: result.totalRows,
            processedItems: result.totalRows,
            successItems: result.data.length,
            errorItems: result.errors.length,
            completedAt: new Date(),
          },
        })

        return {
          success: true,
          batchOperationId: batchOperation.id,
          validItems: result.data.length,
          invalidItems: result.errors.length,
          errors: result.errors,
        }
      }

      // Process the data based on entity type
      const importResult = await this.processImportData(entityType, result.data, updateExisting, skipErrors)

      // Update batch operation with results
      await prisma.batchOperation.update({
        where: { id: batchOperation.id },
        data: {
          status: "COMPLETED",
          totalItems: result.totalRows,
          processedItems: result.totalRows,
          successItems: importResult.successCount,
          errorItems: importResult.errorCount + result.errors.length,
          errors: result.errors.length > 0 ? JSON.stringify(result.errors) : null,
          completedAt: new Date(),
        },
      })

      // Create notification for the user
      await prisma.notification.create({
        data: {
          userId,
          title: `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Import Completed`,
          message: `Imported ${importResult.successCount} ${entityType} successfully. ${importResult.errorCount + result.errors.length} errors.`,
          type: importResult.errorCount > 0 ? "warning" : "success",
          actionUrl: `/dashboard/${entityType}/batch/${batchOperation.id}`,
        },
      })

      return {
        success: true,
        batchOperationId: batchOperation.id,
        successCount: importResult.successCount,
        errorCount: importResult.errorCount + result.errors.length,
      }
    } catch (error) {
      console.error(`Error importing ${entityType}:`, error)

      // Update batch operation with error
      await prisma.batchOperation.update({
        where: { id: batchOperation.id },
        data: {
          status: "FAILED",
          errors: JSON.stringify([{ message: error instanceof Error ? error.message : "Unknown error" }]),
          completedAt: new Date(),
        },
      })

      // Create notification for the user
      await prisma.notification.create({
        data: {
          userId,
          title: `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Import Failed`,
          message: `There was an error processing your ${entityType} import.`,
          type: "error",
          actionUrl: `/dashboard/${entityType}/batch/${batchOperation.id}`,
        },
      })

      throw error
    }
  }

  /**
   * Export data to a file
   */
  static async exportData(options: ExportOptions) {
    const { entityType, format, filters = {}, fields = [], userId } = options

    try {
      // Create a batch operation record
      const batchOperation = await prisma.batchOperation.create({
        data: {
          name: `Export ${entityType} to ${format}`,
          type: "EXPORT",
          status: "PROCESSING",
          userId,
        },
      })

      // Fetch data based on entity type and filters
      const data = await this.fetchDataForExport(entityType, filters)

      // Transform data if needed (e.g., flatten nested objects)
      const transformedData = this.transformDataForExport(entityType, data, fields)

      // Generate file based on format
      let fileContent: string | Buffer
      let contentType: string
      let fileExtension: string

      switch (format) {
        case "csv":
          fileContent = await exportToCSV(transformedData, `${entityType}-export`)
          contentType = "text/csv"
          fileExtension = "csv"
          break
        case "excel":
          fileContent = await exportToExcel(transformedData, `${entityType}-export`)
          contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          fileExtension = "xlsx"
          break
        case "json":
          fileContent = JSON.stringify(transformedData, null, 2)
          contentType = "application/json"
          fileExtension = "json"
          break
        case "pdf":
          fileContent = await exportToPDF(transformedData, `${entityType}-export`)
          contentType = "application/pdf"
          fileExtension = "pdf"
          break
        default:
          throw new Error(`Unsupported export format: ${format}`)
      }

      // Update batch operation with results
      await prisma.batchOperation.update({
        where: { id: batchOperation.id },
        data: {
          status: "COMPLETED",
          totalItems: data.length,
          processedItems: data.length,
          successItems: data.length,
          errorItems: 0,
          completedAt: new Date(),
        },
      })

      return {
        success: true,
        batchOperationId: batchOperation.id,
        fileContent,
        contentType,
        fileName: `${entityType}-export-${new Date().toISOString().slice(0, 10)}.${fileExtension}`,
      }
    } catch (error) {
      console.error(`Error exporting ${entityType}:`, error)
      throw error
    }
  }

  /**
   * Generate a template file for importing data
   */
  static generateImportTemplate(entityType: EntityType, format: FileFormat = "csv") {
    // Get headers based on entity type
    const headers = this.getHeadersForEntity(entityType)

    // Generate template based on format
    switch (format) {
      case "csv":
        return CsvProcessor.generateTemplate(headers)
      case "excel":
        // For simplicity, we'll use CSV for Excel templates too
        return CsvProcessor.generateTemplate(headers)
      case "json":
        // Create a sample JSON object with empty values
        const sampleObject = Object.keys(headers).reduce((obj, key) => {
          obj[key] = ""
          return obj
        }, {})
        return JSON.stringify([sampleObject], null, 2)
      default:
        throw new Error(`Unsupported template format: ${format}`)
    }
  }

  /**
   * Get the appropriate schema for validating imported data
   */
  private static getSchemaForEntity(entityType: EntityType) {
    switch (entityType) {
      case "products":
        return productImportSchema
      case "customers":
        return customerImportSchema
      case "suppliers":
        return supplierImportSchema
      case "categories":
        return categoryImportSchema
      case "orders":
        return orderImportSchema
      default:
        throw new Error(`Unsupported entity type for import: ${entityType}`)
    }
  }

  /**
   * Get headers for generating import templates
   */
  private static getHeadersForEntity(entityType: EntityType) {
    switch (entityType) {
      case "products":
        return {
          name: "Product Name",
          sku: "SKU",
          barcode: "Barcode",
          description: "Description",
          price: "Price",
          cost: "Cost",
          taxRate: "Tax Rate",
          stockQuantity: "Stock Quantity",
          reorderPoint: "Reorder Point",
          categoryName: "Category",
          supplierName: "Supplier",
        }
      case "customers":
        return {
          firstName: "First Name",
          lastName: "Last Name",
          email: "Email",
          phone: "Phone",
          address: "Address",
          city: "City",
          state: "State",
          postalCode: "Postal Code",
          country: "Country",
          notes: "Notes",
        }
      case "suppliers":
        return {
          name: "Supplier Name",
          contactName: "Contact Name",
          email: "Email",
          phone: "Phone",
          address: "Address",
          notes: "Notes",
        }
      case "categories":
        return {
          name: "Category Name",
          description: "Description",
          parentCategory: "Parent Category",
        }
      case "orders":
        return {
          orderNumber: "Order Number",
          customerEmail: "Customer Email",
          status: "Status",
          paymentStatus: "Payment Status",
          paymentMethod: "Payment Method",
          subtotal: "Subtotal",
          tax: "Tax",
          shipping: "Shipping",
          discount: "Discount",
          total: "Total",
          notes: "Notes",
        }
      default:
        throw new Error(`Unsupported entity type for template: ${entityType}`)
    }
  }

  /**
   * Process imported data based on entity type
   */
  private static async processImportData(
    entityType: EntityType,
    data: any[],
    updateExisting: boolean,
    skipErrors: boolean,
  ) {
    const successCount = 0
    const errorCount = 0

    switch (entityType) {
      case "products":
        return this.processProductImport(data, updateExisting, skipErrors)
      case "customers":
        return this.processCustomerImport(data, updateExisting, skipErrors)
      case "suppliers":
        return this.processSupplierImport(data, updateExisting, skipErrors)
      case "categories":
        return this.processCategoryImport(data, updateExisting, skipErrors)
      case "orders":
        return this.processOrderImport(data, updateExisting, skipErrors)
      default:
        throw new Error(`Unsupported entity type for import processing: ${entityType}`)
    }
  }

  /**
   * Process product import data
   */
  private static async processProductImport(data: any[], updateExisting: boolean, skipErrors: boolean) {
    let successCount = 0
    let errorCount = 0

    for (const productData of data) {
      try {
        // Find or create category
        const category = await prisma.category.upsert({
          where: { name: productData.categoryName },
          update: {},
          create: {
            name: productData.categoryName,
            slug: productData.categoryName.toLowerCase().replace(/\s+/g, "-"),
            userId: productData.userId || "system", // Default user ID
          },
        })

        // Find or create supplier if provided
        let supplierId = null
        if (productData.supplierName) {
          const supplier = await prisma.supplier.upsert({
            where: { name: productData.supplierName },
            update: {},
            create: {
              name: productData.supplierName,
              userId: productData.userId || "system", // Default user ID
            },
          })
          supplierId = supplier.id
        }

        // Create or update product
        if (updateExisting) {
          await prisma.product.upsert({
            where: { sku: productData.sku },
            update: {
              name: productData.name,
              barcode: productData.barcode || null,
              description: productData.description || null,
              price: productData.price,
              costPrice: productData.cost || null,
              taxable: productData.taxable !== undefined ? productData.taxable : true,
              inventoryLevel: productData.stockQuantity,
              reorderPoint: productData.reorderPoint || 10,
              categoryId: category.id,
              supplierId: supplierId,
            },
            create: {
              name: productData.name,
              sku: productData.sku,
              barcode: productData.barcode || null,
              description: productData.description || null,
              price: productData.price,
              costPrice: productData.cost || null,
              taxable: productData.taxable !== undefined ? productData.taxable : true,
              inventoryLevel: productData.stockQuantity,
              reorderPoint: productData.reorderPoint || 10,
              categoryId: category.id,
              supplierId: supplierId,
              userId: productData.userId || "system", // Default user ID
            },
          })
        } else {
          // Only create new products, skip existing ones
          const existingProduct = await prisma.product.findUnique({
            where: { sku: productData.sku },
          })

          if (!existingProduct) {
            await prisma.product.create({
              data: {
                name: productData.name,
                sku: productData.sku,
                barcode: productData.barcode || null,
                description: productData.description || null,
                price: productData.price,
                costPrice: productData.cost || null,
                taxable: productData.taxable !== undefined ? productData.taxable : true,
                inventoryLevel: productData.stockQuantity,
                reorderPoint: productData.reorderPoint || 10,
                categoryId: category.id,
                supplierId: supplierId,
                userId: productData.userId || "system", // Default user ID
              },
            })
          }
        }

        successCount++
      } catch (error) {
        console.error("Error processing product:", error)
        errorCount++

        if (!skipErrors) {
          throw error
        }
      }
    }

    return { successCount, errorCount }
  }

  /**
   * Process customer import data
   */
  private static async processCustomerImport(data: any[], updateExisting: boolean, skipErrors: boolean) {
    let successCount = 0
    let errorCount = 0

    for (const customerData of data) {
      try {
        // Create or update customer
        if (updateExisting) {
          await prisma.customer.upsert({
            where: {
              email: customerData.email,
            },
            update: {
              firstName: customerData.firstName,
              lastName: customerData.lastName,
              phone: customerData.phone || null,
              address: customerData.address || null,
              city: customerData.city || null,
              state: customerData.state || null,
              postalCode: customerData.postalCode || null,
              country: customerData.country || null,
              notes: customerData.notes || null,
            },
            create: {
              firstName: customerData.firstName,
              lastName: customerData.lastName,
              email: customerData.email,
              phone: customerData.phone || null,
              address: customerData.address || null,
              city: customerData.city || null,
              state: customerData.state || null,
              postalCode: customerData.postalCode || null,
              country: customerData.country || null,
              notes: customerData.notes || null,
              userId: customerData.userId || "system", // Default user ID
            },
          })
        } else {
          // Only create new customers, skip existing ones
          const existingCustomer = await prisma.customer.findUnique({
            where: { email: customerData.email },
          })

          if (!existingCustomer) {
            await prisma.customer.create({
              data: {
                firstName: customerData.firstName,
                lastName: customerData.lastName,
                email: customerData.email,
                phone: customerData.phone || null,
                address: customerData.address || null,
                city: customerData.city || null,
                state: customerData.state || null,
                postalCode: customerData.postalCode || null,
                country: customerData.country || null,
                notes: customerData.notes || null,
                userId: customerData.userId || "system", // Default user ID
              },
            })
          }
        }

        successCount++
      } catch (error) {
        console.error("Error processing customer:", error)
        errorCount++

        if (!skipErrors) {
          throw error
        }
      }
    }

    return { successCount, errorCount }
  }

  /**
   * Process supplier import data
   */
  private static async processSupplierImport(data: any[], updateExisting: boolean, skipErrors: boolean) {
    let successCount = 0
    let errorCount = 0

    for (const supplierData of data) {
      try {
        // Create or update supplier
        if (updateExisting) {
          await prisma.supplier.upsert({
            where: { name: supplierData.name },
            update: {
              contactName: supplierData.contactName || null,
              email: supplierData.email || null,
              phone: supplierData.phone || null,
              address: supplierData.address || null,
              notes: supplierData.notes || null,
            },
            create: {
              name: supplierData.name,
              contactName: supplierData.contactName || null,
              email: supplierData.email || null,
              phone: supplierData.phone || null,
              address: supplierData.address || null,
              notes: supplierData.notes || null,
              userId: supplierData.userId || "system", // Default user ID
            },
          })
        } else {
          // Only create new suppliers, skip existing ones
          const existingSupplier = await prisma.supplier.findUnique({
            where: { name: supplierData.name },
          })

          if (!existingSupplier) {
            await prisma.supplier.create({
              data: {
                name: supplierData.name,
                contactName: supplierData.contactName || null,
                email: supplierData.email || null,
                phone: supplierData.phone || null,
                address: supplierData.address || null,
                notes: supplierData.notes || null,
                userId: supplierData.userId || "system", // Default user ID
              },
            })
          }
        }

        successCount++
      } catch (error) {
        console.error("Error processing supplier:", error)
        errorCount++

        if (!skipErrors) {
          throw error
        }
      }
    }

    return { successCount, errorCount }
  }

  /**
   * Process category import data
   */
  private static async processCategoryImport(data: any[], updateExisting: boolean, skipErrors: boolean) {
    let successCount = 0
    let errorCount = 0

    for (const categoryData of data) {
      try {
        // Find parent category if provided
        let parentId = null
        if (categoryData.parentCategory) {
          const parentCategory = await prisma.category.findFirst({
            where: { name: categoryData.parentCategory },
          })

          if (parentCategory) {
            parentId = parentCategory.id
          }
        }

        // Create or update category
        if (updateExisting) {
          await prisma.category.upsert({
            where: { name: categoryData.name },
            update: {
              description: categoryData.description || null,
              parentId: parentId,
            },
            create: {
              name: categoryData.name,
              slug: categoryData.name.toLowerCase().replace(/\s+/g, "-"),
              description: categoryData.description || null,
              parentId: parentId,
              userId: categoryData.userId || "system", // Default user ID
            },
          })
        } else {
          // Only create new categories, skip existing ones
          const existingCategory = await prisma.category.findUnique({
            where: { name: categoryData.name },
          })

          if (!existingCategory) {
            await prisma.category.create({
              data: {
                name: categoryData.name,
                slug: categoryData.name.toLowerCase().replace(/\s+/g, "-"),
                description: categoryData.description || null,
                parentId: parentId,
                userId: categoryData.userId || "system", // Default user ID
              },
            })
          }
        }

        successCount++
      } catch (error) {
        console.error("Error processing category:", error)
        errorCount++

        if (!skipErrors) {
          throw error
        }
      }
    }

    return { successCount, errorCount }
  }

  /**
   * Process order import data
   */
  private static async processOrderImport(data: any[], updateExisting: boolean, skipErrors: boolean) {
    let successCount = 0
    let errorCount = 0

    for (const orderData of data) {
      try {
        // Find customer by email
        const customer = await prisma.customer.findUnique({
          where: { email: orderData.customerEmail },
        })

        if (!customer) {
          throw new Error(`Customer with email ${orderData.customerEmail} not found`)
        }

        // Create or update order
        if (updateExisting) {
          await prisma.order.upsert({
            where: { orderNumber: orderData.orderNumber },
            update: {
              status: orderData.status,
              paymentStatus: orderData.paymentStatus,
              paymentMethod: orderData.paymentMethod,
              subtotal: orderData.subtotal,
              tax: orderData.tax,
              shipping: orderData.shipping || 0,
              discount: orderData.discount || 0,
              total: orderData.total,
              notes: orderData.notes || null,
              shippingAddress: orderData.shippingAddress || null,
              billingAddress: orderData.billingAddress || null,
            },
            create: {
              orderNumber: orderData.orderNumber,
              status: orderData.status,
              paymentStatus: orderData.paymentStatus,
              paymentMethod: orderData.paymentMethod,
              subtotal: orderData.subtotal,
              tax: orderData.tax,
              shipping: orderData.shipping || 0,
              discount: orderData.discount || 0,
              total: orderData.total,
              notes: orderData.notes || null,
              shippingAddress: orderData.shippingAddress || null,
              billingAddress: orderData.billingAddress || null,
              customerId: customer.id,
              userId: orderData.userId || "system", // Default user ID
            },
          })
        } else {
          // Only create new orders, skip existing ones
          const existingOrder = await prisma.order.findUnique({
            where: { orderNumber: orderData.orderNumber },
          })

          if (!existingOrder) {
            await prisma.order.create({
              data: {
                orderNumber: orderData.orderNumber,
                status: orderData.status,
                paymentStatus: orderData.paymentStatus,
                paymentMethod: orderData.paymentMethod,
                subtotal: orderData.subtotal,
                tax: orderData.tax,
                shipping: orderData.shipping || 0,
                discount: orderData.discount || 0,
                total: orderData.total,
                notes: orderData.notes || null,
                shippingAddress: orderData.shippingAddress || null,
                billingAddress: orderData.billingAddress || null,
                customerId: customer.id,
                userId: orderData.userId || "system", // Default user ID
              },
            })
          }
        }

        successCount++
      } catch (error) {
        console.error("Error processing order:", error)
        errorCount++

        if (!skipErrors) {
          throw error
        }
      }
    }

    return { successCount, errorCount }
  }

  /**
   * Fetch data for export based on entity type and filters
   */
  private static async fetchDataForExport(entityType: EntityType, filters: Record<string, any>) {
    switch (entityType) {
      case "products":
        return prisma.product.findMany({
          where: filters,
          include: {
            category: true,
            supplier: true,
          },
        })
      case "customers":
        return prisma.customer.findMany({
          where: filters,
        })
      case "suppliers":
        return prisma.supplier.findMany({
          where: filters,
        })
      case "categories":
        return prisma.category.findMany({
          where: filters,
          include: {
            parent: true,
          },
        })
      case "orders":
        return prisma.order.findMany({
          where: filters,
          include: {
            customer: true,
            items: {
              include: {
                product: true,
              },
            },
          },
        })
      default:
        throw new Error(`Unsupported entity type for export: ${entityType}`)
    }
  }

  /**
   * Transform data for export based on entity type
   */
  private static transformDataForExport(entityType: EntityType, data: any[], fields: string[]) {
    switch (entityType) {
      case "products":
        return data.map((product) => ({
          id: product.id,
          name: product.name,
          sku: product.sku,
          barcode: product.barcode || "",
          description: product.description || "",
          price: product.price,
          cost: product.costPrice || 0,
          taxable: product.taxable,
          inventoryLevel: product.inventoryLevel,
          reorderPoint: product.reorderPoint || 0,
          category: product.category?.name || "",
          supplier: product.supplier?.name || "",
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        }))
      case "customers":
        return data.map((customer) => ({
          id: customer.id,
          firstName: customer.firstName,
          lastName: customer.lastName,
          email: customer.email || "",
          phone: customer.phone || "",
          address: customer.address || "",
          city: customer.city || "",
          state: customer.state || "",
          postalCode: customer.postalCode || "",
          country: customer.country || "",
          notes: customer.notes || "",
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt,
        }))
      case "suppliers":
        return data.map((supplier) => ({
          id: supplier.id,
          name: supplier.name,
          contactName: supplier.contactName || "",
          email: supplier.email || "",
          phone: supplier.phone || "",
          address: supplier.address || "",
          notes: supplier.notes || "",
          createdAt: supplier.createdAt,
          updatedAt: supplier.updatedAt,
        }))
      case "categories":
        return data.map((category) => ({
          id: category.id,
          name: category.name,
          description: category.description || "",
          parentCategory: category.parent?.name || "",
          createdAt: category.createdAt,
          updatedAt: category.updatedAt,
        }))
      case "orders":
        return data.map((order) => ({
          id: order.id,
          orderNumber: order.orderNumber,
          customer: `${order.customer.firstName} ${order.customer.lastName}`,
          customerEmail: order.customer.email || "",
          status: order.status,
          paymentStatus: order.paymentStatus,
          paymentMethod: order.paymentMethod || "",
          subtotal: order.subtotal,
          tax: order.tax,
          shipping: order.shipping,
          discount: order.discount,
          total: order.total,
          notes: order.notes || "",
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
        }))
      default:
        return data
    }
  }
}

