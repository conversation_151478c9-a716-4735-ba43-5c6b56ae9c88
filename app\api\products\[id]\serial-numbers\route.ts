import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { ApiError } from "@/lib/api-error"
import { serialNumberSchema, serialNumberBulkImportSchema } from "@/lib/validations/serial-number-schema"

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const searchParams = req.nextUrl.searchParams
    const status = searchParams.get("status")
    const variantId = searchParams.get("variantId")

    // Build filter conditions
    const where: any = {
      productId: params.id,
    }

    if (status) {
      where.status = status
    }

    if (variantId) {
      where.variantId = variantId
    }

    const serialNumbers = await prisma.serialNumber.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        variant: true,
      },
    })

    return NextResponse.json(serialNumbers)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Serial numbers fetch error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const body = await req.json()

    // Check if this is a bulk import
    if (body.serialNumbers && Array.isArray(body.serialNumbers)) {
      const validatedData = serialNumberBulkImportSchema.parse({
        ...body,
        productId: params.id,
      })

      // Check if product exists and supports serial numbers
      const product = await prisma.product.findUnique({
        where: { id: params.id },
      })

      if (!product) {
        throw ApiError.notFound("Product not found")
      }

      if (!product.trackSerialNumbers) {
        // Update product to track serial numbers
        await prisma.product.update({
          where: { id: params.id },
          data: { trackSerialNumbers: true },
        })
      }

      // Check for duplicate serial numbers
      const existingSerialNumbers = await prisma.serialNumber.findMany({
        where: {
          productId: params.id,
          serialNumber: {
            in: validatedData.serialNumbers,
          },
        },
        select: {
          serialNumber: true,
        },
      })

      const existingSet = new Set(existingSerialNumbers.map((sn) => sn.serialNumber))
      const newSerialNumbers = validatedData.serialNumbers.filter((sn) => !existingSet.has(sn))

      if (newSerialNumbers.length === 0) {
        throw ApiError.badRequest("All serial numbers already exist for this product")
      }

      // Create serial numbers
      const createdSerialNumbers = await prisma.$transaction(
        newSerialNumbers.map((serialNumber) =>
          prisma.serialNumber.create({
            data: {
              serialNumber,
              status: validatedData.status,
              purchaseDate: validatedData.purchaseDate ? new Date(validatedData.purchaseDate) : null,
              productId: params.id,
              variantId: validatedData.variantId || null,
            },
          }),
        ),
      )

      return NextResponse.json({
        success: true,
        created: createdSerialNumbers.length,
        duplicates: validatedData.serialNumbers.length - newSerialNumbers.length,
      })
    } else {
      // Single serial number creation
      const validatedData = serialNumberSchema.parse({
        ...body,
        productId: params.id,
      })

      // Check if product exists and supports serial numbers
      const product = await prisma.product.findUnique({
        where: { id: params.id },
      })

      if (!product) {
        throw ApiError.notFound("Product not found")
      }

      if (!product.trackSerialNumbers) {
        // Update product to track serial numbers
        await prisma.product.update({
          where: { id: params.id },
          data: { trackSerialNumbers: true },
        })
      }

      // Check if serial number already exists for this product
      const existingSerialNumber = await prisma.serialNumber.findFirst({
        where: {
          productId: params.id,
          serialNumber: validatedData.serialNumber,
        },
      })

      if (existingSerialNumber) {
        throw ApiError.conflict("This serial number already exists for this product")
      }

      // Create serial number
      const serialNumber = await prisma.serialNumber.create({
        data: {
          serialNumber: validatedData.serialNumber,
          status: validatedData.status,
          notes: validatedData.notes || null,
          purchaseDate: validatedData.purchaseDate ? new Date(validatedData.purchaseDate) : null,
          productId: params.id,
          variantId: validatedData.variantId || null,
        },
      })

      return NextResponse.json({
        success: true,
        serialNumber,
      })
    }
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Serial number creation error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

