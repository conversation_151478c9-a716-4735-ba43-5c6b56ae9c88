import { type NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { auth } from "@/lib/auth"

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "20")
    const search = searchParams.get("search") || ""
    const category = searchParams.get("category") || ""
    const sort = searchParams.get("sort") || "name"
    const order = searchParams.get("order") || "asc"

    // Calculate offset for pagination
    const offset = (page - 1) * limit

    // Build the query with proper indexing for performance
    const query = db.product.findMany({
      where: {
        userId: session.user.id,
        ...(search
          ? {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                { sku: { contains: search, mode: "insensitive" } },
                { description: { contains: search, mode: "insensitive" } },
              ],
            }
          : {}),
        ...(category ? { categoryId: category } : {}),
      },
      include: {
        category: true,
        supplier: true,
      },
      take: limit,
      skip: offset,
      orderBy: {
        [sort]: order,
      },
    })

    // Get total count for pagination info
    const countQuery = db.product.count({
      where: {
        userId: session.user.id,
        ...(search
          ? {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                { sku: { contains: search, mode: "insensitive" } },
                { description: { contains: search, mode: "insensitive" } },
              ],
            }
          : {}),
        ...(category ? { categoryId: category } : {}),
      },
    })

    // Execute both queries in parallel for efficiency
    const [products, totalCount] = await Promise.all([query, countQuery])

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasMore: offset + products.length < totalCount,
      },
    })
  } catch (error) {
    console.error("Error fetching products:", error)
    return NextResponse.json({ error: "Failed to fetch products" }, { status: 500 })
  }
}

