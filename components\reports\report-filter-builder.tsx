"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, Filter } from "lucide-react"

interface Field {
  id: string
  name: string
  label: string
  type: string
  category?: string
}

interface FilterCondition {
  id: string
  field: string
  operator: string
  value: string | number | boolean | null
}

interface ReportFilterBuilderProps {
  availableFields: Field[]
  filters: FilterCondition[]
  onFiltersChange: (filters: FilterCondition[]) => void
}

export function ReportFilterBuilder({ availableFields = [], filters = [], onFiltersChange }: ReportFilterBuilderProps) {
  // Add a new filter
  const addFilter = () => {
    const newFilter = {
      id: `filter-${Date.now()}`,
      field: availableFields.length > 0 ? availableFields[0].id : "",
      operator: "equals",
      value: "",
    }
    onFiltersChange([...filters, newFilter])
  }

  // Remove a filter
  const removeFilter = (filterId: string) => {
    onFiltersChange(filters.filter((filter) => filter.id !== filterId))
  }

  // Update a filter
  const updateFilter = (filterId: string, field: string, value: any) => {
    onFiltersChange(filters.map((filter) => (filter.id === filterId ? { ...filter, [field]: value } : filter)))
  }

  // Get field type by id
  const getFieldType = (fieldId: string) => {
    const field = availableFields.find((f) => f.id === fieldId)
    return field?.type || "string"
  }

  // Get available operators based on field type
  const getOperatorsForFieldType = (fieldType: string) => {
    switch (fieldType) {
      case "number":
        return [
          { value: "equals", label: "Equals" },
          { value: "notEquals", label: "Not Equals" },
          { value: "gt", label: "Greater Than" },
          { value: "gte", label: "Greater Than or Equal" },
          { value: "lt", label: "Less Than" },
          { value: "lte", label: "Less Than or Equal" },
          { value: "isNull", label: "Is Empty" },
          { value: "isNotNull", label: "Is Not Empty" },
        ]
      case "date":
        return [
          { value: "equals", label: "Equals" },
          { value: "notEquals", label: "Not Equals" },
          { value: "gt", label: "After" },
          { value: "gte", label: "On or After" },
          { value: "lt", label: "Before" },
          { value: "lte", label: "On or Before" },
          { value: "isNull", label: "Is Empty" },
          { value: "isNotNull", label: "Is Not Empty" },
        ]
      case "boolean":
        return [
          { value: "equals", label: "Equals" },
          { value: "notEquals", label: "Not Equals" },
        ]
      default:
        return [
          { value: "equals", label: "Equals" },
          { value: "notEquals", label: "Not Equals" },
          { value: "contains", label: "Contains" },
          { value: "notContains", label: "Does Not Contain" },
          { value: "startsWith", label: "Starts With" },
          { value: "endsWith", label: "Ends With" },
          { value: "isNull", label: "Is Empty" },
          { value: "isNotNull", label: "Is Not Empty" },
        ]
    }
  }

  // Render value input based on field type and operator
  const renderValueInput = (filter: FilterCondition) => {
    const fieldType = getFieldType(filter.field)

    // No value input needed for null/not null operators
    if (filter.operator === "isNull" || filter.operator === "isNotNull") {
      return null
    }

    switch (fieldType) {
      case "number":
        return (
          <Input
            type="number"
            value={filter.value as string}
            onChange={(e) => updateFilter(filter.id, "value", e.target.value)}
            className="w-full"
          />
        )
      case "date":
        return (
          <Input
            type="date"
            value={filter.value as string}
            onChange={(e) => updateFilter(filter.id, "value", e.target.value)}
            className="w-full"
          />
        )
      case "boolean":
        return (
          <Select
            value={filter.value?.toString()}
            onValueChange={(value) => updateFilter(filter.id, "value", value === "true")}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select value" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">True</SelectItem>
              <SelectItem value="false">False</SelectItem>
            </SelectContent>
          </Select>
        )
      default:
        return (
          <Input
            type="text"
            value={filter.value as string}
            onChange={(e) => updateFilter(filter.id, "value", e.target.value)}
            className="w-full"
          />
        )
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Badge variant="outline" className="px-2 py-1">
          {filters.length} filters applied
        </Badge>
        <Button variant="outline" size="sm" onClick={addFilter}>
          <Plus className="h-4 w-4 mr-2" />
          Add Filter
        </Button>
      </div>

      {filters.length > 0 ? (
        <div className="space-y-4">
          {filters.map((filter, index) => {
            const fieldType = getFieldType(filter.field)
            const operators = getOperatorsForFieldType(fieldType)

            return (
              <Card key={filter.id}>
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                    <div className="md:col-span-4">
                      <Select value={filter.field} onValueChange={(value) => updateFilter(filter.id, "field", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select field" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableFields.map((field) => (
                            <SelectItem key={field.id} value={field.id}>
                              {field.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="md:col-span-3">
                      <Select
                        value={filter.operator}
                        onValueChange={(value) => updateFilter(filter.id, "operator", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select operator" />
                        </SelectTrigger>
                        <SelectContent>
                          {operators.map((op) => (
                            <SelectItem key={op.value} value={op.value}>
                              {op.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="md:col-span-4">{renderValueInput(filter)}</div>

                    <div className="md:col-span-1 flex justify-end">
                      <Button variant="ghost" size="icon" onClick={() => removeFilter(filter.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      ) : (
        <div className="text-center py-8 border rounded-md">
          <Filter className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-muted-foreground">No filters applied</p>
          <p className="text-sm text-muted-foreground">Add filters to narrow down your report results</p>
        </div>
      )}
    </div>
  )
}

