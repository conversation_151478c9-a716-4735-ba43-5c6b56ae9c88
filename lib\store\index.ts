import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { shallow } from "zustand/shallow"
import { createPOSSlice, type POSSlice } from "./pos-slice"
import { createOfflineSlice, type OfflineSlice } from "./offline-slice"
import { createSettingsSlice, type SettingsSlice } from "./settings-slice"
import { createAuthSlice, type AuthSlice } from "./auth-slice"
import { createUISlice, type UISlice } from "./ui-slice"

// Root store type combining all slices
export type StoreState = POSSlice & OfflineSlice & SettingsSlice & AuthSlice & UISlice

// Create a non-persisted store for SSR
const createBaseStore = () => create<StoreState>()((...a) => ({
  ...createPOSSlice(...a),
  ...createOfflineSlice(...a),
  ...createSettingsSlice(...a),
  ...createAuthSlice(...a),
  ...createUISlice(...a),
}))

// Create the persisted store for client-side
const createPersistedStore = () => create<StoreState>()(
  persist(
    (...a) => ({
      ...createPOSSlice(...a),
      ...createOfflineSlice(...a),
      ...createSettingsSlice(...a),
      ...createAuthSlice(...a),
      ...createUISlice(...a),
    }),
    {
      name: "stocksync-store",
      storage: createJSONStorage(() => localStorage),
      // Only persist specific parts of the store
      partialize: (state) => ({
        cart: state.cart,
        heldSales: state.heldSales,
        offlineTransactions: state.offlineTransactions,
        settings: state.settings,
        lastSyncTimestamp: state.lastSyncTimestamp,
        // Don't persist sensitive auth data
        isAuthenticated: state.isAuthenticated,
        // Don't persist user password or token
        user: state.user
          ? {
              id: state.user.id,
              name: state.user.name,
              email: state.user.email,
              role: state.user.role,
            }
          : null,
        // Don't persist UI state
        // uiState is intentionally omitted
      }),
    },
  ),
)

// Export the appropriate store based on environment
export const useStore = typeof window !== "undefined" ? createPersistedStore() : createBaseStore()

// Create selector hooks for better performance
export const usePOSStore = () =>
  useStore(
    (state) => ({
      cart: state.cart,
      addToCart: state.addToCart,
      removeFromCart: state.removeFromCart,
      updateCartItem: state.updateCartItem,
      clearCart: state.clearCart,
      heldSales: state.heldSales,
      holdCurrentSale: state.holdCurrentSale,
      resumeHeldSale: state.resumeHeldSale,
      removeHeldSale: state.removeHeldSale,
    }),
    shallow,
  )

// More granular selectors for specific use cases
export const useCart = () => useStore((state) => state.cart, shallow)
export const useCartActions = () =>
  useStore(
    (state) => ({
      addToCart: state.addToCart,
      removeFromCart: state.removeFromCart,
      updateCartItem: state.updateCartItem,
      clearCart: state.clearCart,
    }),
    shallow,
  )

export const useHeldSales = () => useStore((state) => state.heldSales, shallow)
export const useHeldSalesActions = () =>
  useStore(
    (state) => ({
      holdCurrentSale: state.holdCurrentSale,
      resumeHeldSale: state.resumeHeldSale,
      removeHeldSale: state.removeHeldSale,
    }),
    shallow,
  )

export const useOfflineStore = () =>
  useStore(
    (state) => ({
      isOffline: state.isOffline,
      setIsOffline: state.setIsOffline,
      offlineTransactions: state.offlineTransactions,
      addOfflineTransaction: state.addOfflineTransaction,
      markTransactionSynced: state.markTransactionSynced,
      clearSyncedTransactions: state.clearSyncedTransactions,
      lastSyncTimestamp: state.lastSyncTimestamp,
      setLastSyncTimestamp: state.setLastSyncTimestamp,
    }),
    shallow,
  )

// More granular selectors for offline state
export const useOfflineStatus = () => useStore((state) => state.isOffline)
export const useOfflineTransactions = () => useStore((state) => state.offlineTransactions, shallow)

export const useSettingsStore = () =>
  useStore(
    (state) => ({
      settings: state.settings,
      updateSettings: state.updateSettings,
    }),
    shallow,
  )

// More granular selectors for settings
export const useCompanySettings = () => useStore((state) => state.settings.company, shallow)
export const useTaxSettings = () => useStore((state) => state.settings.tax, shallow)
export const useReceiptSettings = () => useStore((state) => state.settings.receipt, shallow)
export const useSystemSettings = () => useStore((state) => state.settings.system, shallow)

export const useAuthStore = () =>
  useStore(
    (state) => ({
      isAuthenticated: state.isAuthenticated,
      user: state.user,
      login: state.login,
      logout: state.logout,
    }),
    shallow,
  )

// More granular selectors for auth
export const useAuthStatus = () => useStore((state) => state.isAuthenticated)
export const useUser = () => useStore((state) => state.user, shallow)
export const useUserRole = () => useStore((state) => state.user?.role)

// UI state selectors
export const useUIStore = () =>
  useStore(
    (state) => ({
      sidebarOpen: state.sidebarOpen,
      setSidebarOpen: state.setSidebarOpen,
      currentTheme: state.currentTheme,
      setCurrentTheme: state.setCurrentTheme,
      activeModal: state.activeModal,
      setActiveModal: state.setActiveModal,
    }),
    shallow,
  )

export const useSidebarState = () =>
  useStore(
    (state) => ({
      sidebarOpen: state.sidebarOpen,
      setSidebarOpen: state.setSidebarOpen,
    }),
    shallow,
  )

export const useThemeState = () =>
  useStore(
    (state) => ({
      currentTheme: state.currentTheme,
      setCurrentTheme: state.setCurrentTheme,
    }),
    shallow,
  )

export const useModalState = () =>
  useStore(
    (state) => ({
      activeModal: state.activeModal,
      setActiveModal: state.setActiveModal,
    }),
    shallow,
  )

