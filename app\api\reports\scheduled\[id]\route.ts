import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const scheduleId = params.id

    // Fetch the scheduled report
    const scheduledReport = await prisma.scheduledReport.findUnique({
      where: { id: scheduleId },
      include: {
        report: {
          select: {
            name: true,
            entity: true,
            fields: true,
            filters: true,
          },
        },
      },
    })

    if (!scheduledReport) {
      return NextResponse.json({ error: "Scheduled report not found" }, { status: 404 })
    }

    // Check if user has access to this scheduled report
    if (scheduledReport.userId !== session.user.id) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json(scheduledReport)
  } catch (error) {
    console.error("Error fetching scheduled report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const scheduleId = params.id

    // Fetch the scheduled report to check ownership
    const scheduledReport = await prisma.scheduledReport.findUnique({
      where: { id: scheduleId },
    })

    if (!scheduledReport) {
      return NextResponse.json({ error: "Scheduled report not found" }, { status: 404 })
    }

    // Only the owner can delete a scheduled report
    if (scheduledReport.userId !== session.user.id) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Delete the scheduled report
    await prisma.scheduledReport.delete({
      where: { id: scheduleId },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting scheduled report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

