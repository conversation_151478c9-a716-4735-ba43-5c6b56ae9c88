"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, CheckCircle, XCircle, Clock, TruckIcon, PackageIcon, CreditCard, AlertTriangle } from "lucide-react"
import { formatDate } from "@/lib/utils"

interface OrderStatusWorkflowProps {
  order: any
}

export function OrderStatusWorkflow({ order }: OrderStatusWorkflowProps) {
  const router = useRouter()
  const [isUpdating, setIsUpdating] = useState(false)
  const [status, setStatus] = useState(order.status)
  const [notes, setNotes] = useState("")

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        )
      case "PROCESSING":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
            <PackageIcon className="mr-1 h-3 w-3" />
            Processing
          </Badge>
        )
      case "AWAITING_PAYMENT":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <CreditCard className="mr-1 h-3 w-3" />
            Awaiting Payment
          </Badge>
        )
      case "AWAITING_FULFILLMENT":
        return (
          <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200">
            <PackageIcon className="mr-1 h-3 w-3" />
            Awaiting Fulfillment
          </Badge>
        )
      case "AWAITING_SHIPMENT":
        return (
          <Badge variant="outline" className="bg-cyan-100 text-cyan-800 border-cyan-200">
            <PackageIcon className="mr-1 h-3 w-3" />
            Awaiting Shipment
          </Badge>
        )
      case "PARTIALLY_SHIPPED":
        return (
          <Badge variant="outline" className="bg-teal-100 text-teal-800 border-teal-200">
            <TruckIcon className="mr-1 h-3 w-3" />
            Partially Shipped
          </Badge>
        )
      case "SHIPPED":
        return (
          <Badge variant="outline" className="bg-emerald-100 text-emerald-800 border-emerald-200">
            <TruckIcon className="mr-1 h-3 w-3" />
            Shipped
          </Badge>
        )
      case "DELIVERED":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Delivered
          </Badge>
        )
      case "COMPLETED":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        )
      case "CANCELLED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Cancelled
          </Badge>
        )
      case "ON_HOLD":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
            <AlertTriangle className="mr-1 h-3 w-3" />
            On Hold
          </Badge>
        )
      case "BACKORDERED":
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Backordered
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  const updateOrderStatus = async () => {
    try {
      setIsUpdating(true)

      const response = await fetch(`/api/orders/${order.id}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
          notes,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update order status")
      }

      toast({
        title: "Order Updated",
        description: `Order #${order.orderNumber} status has been updated to ${status.replace("_", " ")}.`,
      })

      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update order status",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  // Get available next statuses based on current status
  const getAvailableStatuses = () => {
    const allStatuses = [
      "PENDING",
      "PROCESSING",
      "AWAITING_PAYMENT",
      "AWAITING_FULFILLMENT",
      "AWAITING_SHIPMENT",
      "PARTIALLY_SHIPPED",
      "SHIPPED",
      "DELIVERED",
      "COMPLETED",
      "CANCELLED",
      "ON_HOLD",
      "BACKORDERED",
    ]

    // Define status workflow rules
    const statusFlow: Record<string, string[]> = {
      PENDING: ["PROCESSING", "AWAITING_PAYMENT", "CANCELLED", "ON_HOLD"],
      AWAITING_PAYMENT: ["PROCESSING", "CANCELLED", "ON_HOLD"],
      PROCESSING: ["AWAITING_FULFILLMENT", "BACKORDERED", "CANCELLED", "ON_HOLD"],
      AWAITING_FULFILLMENT: ["AWAITING_SHIPMENT", "PARTIALLY_SHIPPED", "BACKORDERED", "CANCELLED", "ON_HOLD"],
      AWAITING_SHIPMENT: ["PARTIALLY_SHIPPED", "SHIPPED", "CANCELLED", "ON_HOLD"],
      PARTIALLY_SHIPPED: ["SHIPPED", "CANCELLED", "ON_HOLD"],
      SHIPPED: ["DELIVERED", "CANCELLED"],
      DELIVERED: ["COMPLETED"],
      COMPLETED: [],
      CANCELLED: ["PROCESSING"], // Allow reactivation
      ON_HOLD: ["PENDING", "PROCESSING", "AWAITING_PAYMENT", "AWAITING_FULFILLMENT", "CANCELLED"],
      BACKORDERED: ["PROCESSING", "AWAITING_FULFILLMENT", "CANCELLED"],
    }

    // Return current status and allowed next statuses
    return statusFlow[order.status] || allStatuses
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Order Status</CardTitle>
        <CardDescription>Manage the status of order #{order.orderNumber}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <span className="text-muted-foreground">Current Status:</span>
          <span>{getStatusBadge(order.status)}</span>
        </div>

        <Separator />

        <div>
          <h3 className="text-lg font-medium mb-4">Status History</h3>
          <div className="space-y-2">
            {order.statusHistory.map((history: any) => (
              <div key={history.id} className="flex items-center justify-between py-2">
                <div className="flex items-center">
                  {getStatusBadge(history.status)}
                  {history.notes && <span className="ml-4 text-sm text-muted-foreground">{history.notes}</span>}
                </div>
                <span className="text-sm text-muted-foreground">{formatDate(history.createdAt)}</span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        <div>
          <h3 className="text-lg font-medium mb-4">Update Status</h3>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">New Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableStatuses().map((statusOption) => (
                    <SelectItem key={statusOption} value={statusOption}>
                      {statusOption.replace(/_/g, " ")}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add notes about this status update"
              />
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={updateOrderStatus} disabled={isUpdating || status === order.status} className="ml-auto">
          {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Update Status
        </Button>
      </CardFooter>
    </Card>
  )
}

