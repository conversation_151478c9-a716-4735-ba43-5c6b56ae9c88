"use client"

import { useState, useEffect } from "react"
import { Camera, ShoppingCart, Plus, Minus, X, CreditCard, DollarSign, Scan, Package } from "lucide-react"
import { TouchButton } from "@/components/ui/touch-button"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>et, SheetContent, SheetHeader, SheetTitle, SheetFooter } from "@/components/ui/sheet"
import { useMobile } from "@/hooks/use-mobile"
import { useMobileData } from "@/hooks/use-mobile-data"
import { useToast } from "@/hooks/use-toast"
import { formatCurrency } from "@/lib/utils"

interface Product {
  id: string
  name: string
  price: number
  sku: string
  barcode: string | null
  image: string | null
  stock: number
}

interface CartItem {
  product: Product
  quantity: number
}

export default function MobilePOS() {
  const isMobile = useMobile()
  const { toast } = useToast()
  const [cart, setCart] = useState<CartItem[]>([])
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false)
  const [isScanning, setIsScanning] = useState(false)

  // Fetch popular products for quick access
  const { data: popularProducts, isLoading } = useMobileData<Product[]>({
    fetcher: async () => {
      const res = await fetch("/api/products/popular")
      if (!res.ok) throw new Error("Failed to fetch popular products")
      return res.json()
    },
    initialData: [],
  })

  // Calculate cart totals
  const subtotal = cart.reduce((sum, item) => sum + item.product.price * item.quantity, 0)
  const tax = subtotal * 0.08 // 8% tax rate
  const total = subtotal + tax

  // Add product to cart
  const addToCart = (product: Product) => {
    setCart((prevCart) => {
      const existingItem = prevCart.find((item) => item.product.id === product.id)

      if (existingItem) {
        return prevCart.map((item) =>
          item.product.id === product.id ? { ...item, quantity: item.quantity + 1 } : item,
        )
      } else {
        return [...prevCart, { product, quantity: 1 }]
      }
    })

    toast({
      title: "Added to cart",
      description: `${product.name} added to cart`,
      duration: 2000,
    })
  }

  // Update item quantity
  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(productId)
      return
    }

    setCart((prevCart) =>
      prevCart.map((item) => (item.product.id === productId ? { ...item, quantity: newQuantity } : item)),
    )
  }

  // Remove item from cart
  const removeFromCart = (productId: string) => {
    setCart((prevCart) => prevCart.filter((item) => item.product.id !== productId))
  }

  // Handle barcode scanning
  const handleScan = async (barcode: string) => {
    try {
      const res = await fetch(`/api/products/barcode/${barcode}`)
      if (!res.ok) throw new Error("Product not found")

      const product = await res.json()
      addToCart(product)
      setIsScanning(false)
    } catch (error) {
      toast({
        title: "Product not found",
        description: "No product found with this barcode",
        variant: "destructive",
      })
      setIsScanning(false)
    }
  }

  // Process payment
  const processPayment = async (paymentMethod: "card" | "cash") => {
    try {
      const res = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          items: cart.map((item) => ({
            productId: item.product.id,
            quantity: item.quantity,
            price: item.product.price,
          })),
          paymentMethod,
          subtotal,
          tax,
          total,
        }),
      })

      if (!res.ok) throw new Error("Failed to process order")

      const order = await res.json()

      toast({
        title: "Order completed",
        description: `Order #${order.id} has been processed`,
        variant: "success",
      })

      // Clear cart and close checkout
      setCart([])
      setIsCheckoutOpen(false)
      setIsCartOpen(false)
    } catch (error) {
      toast({
        title: "Order failed",
        description: "Failed to process the order. Please try again.",
        variant: "destructive",
      })
    }
  }

  // If not on mobile, redirect to regular POS
  useEffect(() => {
    if (!isMobile && typeof window !== "undefined") {
      window.location.href = "/pos"
    }
  }, [isMobile])

  // Mock barcode scanner for demo purposes
  const MockScanner = () => {
    const [mockBarcode, setMockBarcode] = useState("")

    return (
      <div className="flex flex-col items-center gap-4 p-4">
        <div className="relative w-full aspect-video bg-black rounded-lg overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <Camera className="h-16 w-16 text-white opacity-20" />
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-3/4 h-1 bg-red-500 animate-pulse"></div>
          </div>
        </div>

        <div className="w-full">
          <input
            type="text"
            value={mockBarcode}
            onChange={(e) => setMockBarcode(e.target.value)}
            placeholder="Enter barcode manually"
            className="w-full p-3 border rounded-md"
          />
        </div>

        <div className="flex gap-2 w-full">
          <Button variant="outline" className="flex-1" onClick={() => setIsScanning(false)}>
            Cancel
          </Button>
          <Button className="flex-1" onClick={() => handleScan(mockBarcode)} disabled={!mockBarcode}>
            Submit
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="pb-16">
      {/* Product Grid */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        {isLoading
          ? Array(6)
              .fill(0)
              .map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-3 h-32 flex flex-col justify-between">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </CardContent>
                </Card>
              ))
          : popularProducts?.map((product) => (
              <Card key={product.id} className="overflow-hidden" onClick={() => addToCart(product)}>
                <CardContent className="p-0 flex flex-col h-full">
                  {product.image ? (
                    <div className="h-24 bg-gray-100">
                      <img
                        src={product.image || "/placeholder.svg"}
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="h-24 bg-gray-100 flex items-center justify-center">
                      <Package className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  <div className="p-3">
                    <h3 className="font-medium text-sm line-clamp-1">{product.name}</h3>
                    <p className="text-sm font-bold">{formatCurrency(product.price)}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
      </div>

      {/* Action Buttons */}
      <div className="fixed bottom-20 right-4 flex flex-col gap-2">
        <TouchButton size="icon" className="h-14 w-14 rounded-full shadow-lg" onClick={() => setIsScanning(true)}>
          <Scan className="h-6 w-6" />
        </TouchButton>

        <TouchButton
          size="icon"
          className="h-14 w-14 rounded-full shadow-lg relative"
          onClick={() => setIsCartOpen(true)}
        >
          <ShoppingCart className="h-6 w-6" />
          {cart.length > 0 && (
            <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
              {cart.reduce((sum, item) => sum + item.quantity, 0)}
            </span>
          )}
        </TouchButton>
      </div>

      {/* Cart Sheet */}
      <Sheet open={isCartOpen} onOpenChange={setIsCartOpen}>
        <SheetContent side="bottom" className="h-[80vh] flex flex-col">
          <SheetHeader>
            <SheetTitle>Shopping Cart</SheetTitle>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto py-4">
            {cart.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground">
                <ShoppingCart className="h-12 w-12 mb-2 opacity-20" />
                <p>Your cart is empty</p>
              </div>
            ) : (
              <div className="space-y-4">
                {cart.map((item) => (
                  <div key={item.product.id} className="flex items-center gap-3 pb-3 border-b">
                    <div className="flex-1">
                      <h4 className="font-medium">{item.product.name}</h4>
                      <p className="text-sm text-muted-foreground">{formatCurrency(item.product.price)} each</p>
                    </div>

                    <div className="flex items-center gap-2">
                      <TouchButton
                        size="icon"
                        variant="outline"
                        className="h-8 w-8"
                        onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                      >
                        <Minus className="h-4 w-4" />
                      </TouchButton>

                      <span className="w-8 text-center">{item.quantity}</span>

                      <TouchButton
                        size="icon"
                        variant="outline"
                        className="h-8 w-8"
                        onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                      >
                        <Plus className="h-4 w-4" />
                      </TouchButton>

                      <TouchButton
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8 text-destructive"
                        onClick={() => removeFromCart(item.product.id)}
                      >
                        <X className="h-4 w-4" />
                      </TouchButton>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <SheetFooter className="border-t pt-4">
            <div className="w-full space-y-2">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{formatCurrency(subtotal)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax</span>
                <span>{formatCurrency(tax)}</span>
              </div>
              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>{formatCurrency(total)}</span>
              </div>

              <TouchButton
                className="w-full mt-4"
                size="lg"
                disabled={cart.length === 0}
                onClick={() => {
                  setIsCartOpen(false)
                  setIsCheckoutOpen(true)
                }}
              >
                Checkout
              </TouchButton>
            </div>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* Checkout Sheet */}
      <Sheet open={isCheckoutOpen} onOpenChange={setIsCheckoutOpen}>
        <SheetContent side="bottom" className="h-[60vh] flex flex-col">
          <SheetHeader>
            <SheetTitle>Checkout</SheetTitle>
          </SheetHeader>

          <div className="flex-1 py-6">
            <div className="space-y-6">
              <div className="bg-muted p-4 rounded-lg">
                <div className="flex justify-between mb-2">
                  <span>Items</span>
                  <span>{cart.reduce((sum, item) => sum + item.quantity, 0)}</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Subtotal</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Tax</span>
                  <span>{formatCurrency(tax)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg">
                  <span>Total</span>
                  <span>{formatCurrency(total)}</span>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-medium">Payment Method</h3>

                <TouchButton
                  className="w-full justify-start gap-3 h-16"
                  variant="outline"
                  onClick={() => processPayment("card")}
                >
                  <CreditCard className="h-5 w-5" />
                  <div className="text-left">
                    <div className="font-medium">Credit/Debit Card</div>
                    <div className="text-sm text-muted-foreground">Pay with card</div>
                  </div>
                </TouchButton>

                <TouchButton
                  className="w-full justify-start gap-3 h-16"
                  variant="outline"
                  onClick={() => processPayment("cash")}
                >
                  <DollarSign className="h-5 w-5" />
                  <div className="text-left">
                    <div className="font-medium">Cash</div>
                    <div className="text-sm text-muted-foreground">Pay with cash</div>
                  </div>
                </TouchButton>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Barcode Scanner Sheet */}
      <Sheet open={isScanning} onOpenChange={setIsScanning}>
        <SheetContent side="bottom" className="h-[60vh]">
          <SheetHeader>
            <SheetTitle>Scan Barcode</SheetTitle>
          </SheetHeader>
          <MockScanner />
        </SheetContent>
      </Sheet>
    </div>
  )
}

