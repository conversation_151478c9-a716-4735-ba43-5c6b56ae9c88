"use server"

import { revalidatePath } from "next/cache"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { EncryptionService } from "@/lib/encryption/encryption-service"
import { RetentionPolicyService, type DataCategory } from "@/lib/retention/retention-policy"
import { RetentionExecutor } from "@/lib/retention/retention-executor"
import { ConsentManager, type ConsentType } from "@/lib/privacy/consent-manager"
import { PrivacySettingsService } from "@/lib/privacy/privacy-settings"
import { DataSubjectRequestService, type RequestType } from "@/lib/privacy/data-subject-request"
import { EncryptedFieldHelper } from "@/lib/encryption/encrypted-field"
import { db } from "@/lib/db"

// Encryption actions
export async function rotateEncryptionKeys() {
  const session = await getServerSession(authOptions)
  if (!session?.user || !session.user.isAdmin) {
    throw new Error("Unauthorized")
  }

  await EncryptionService.rotateKeys()
  revalidatePath("/settings/security")

  return { success: true }
}

// Retention policy actions
export async function updateRetentionPolicy(
  dataCategory: DataCategory,
  retentionPeriodDays: number,
  archiveBeforeDelete: boolean,
) {
  const session = await getServerSession(authOptions)
  if (!session?.user || !session.user.isAdmin) {
    throw new Error("Unauthorized")
  }

  await RetentionPolicyService.updatePolicy(dataCategory, retentionPeriodDays, archiveBeforeDelete)

  revalidatePath("/settings/data-retention")

  return { success: true }
}

export async function executeRetentionPolicies() {
  const session = await getServerSession(authOptions)
  if (!session?.user || !session.user.isAdmin) {
    throw new Error("Unauthorized")
  }

  const results = await RetentionExecutor.executeAllPolicies()

  revalidatePath("/settings/data-retention")

  return { success: true, results }
}

// Consent management actions
export async function updateConsent(consentType: ConsentType, granted: boolean) {
  const session = await getServerSession(authOptions)
  if (!session?.user) {
    throw new Error("Unauthorized")
  }

  await ConsentManager.recordConsent(session.user.id, consentType, granted)

  revalidatePath("/settings/privacy")

  return { success: true }
}

// Privacy settings actions
export async function updatePrivacySettings(settings: {
  dataMinimization?: boolean
  anonymizeAnalytics?: boolean
  limitDataSharing?: boolean
  enhancedSecurity?: boolean
}) {
  const session = await getServerSession(authOptions)
  if (!session?.user) {
    throw new Error("Unauthorized")
  }

  await PrivacySettingsService.updateSettings(session.user.id, settings)

  revalidatePath("/settings/privacy")

  return { success: true }
}

// Data subject request actions
export async function createDataSubjectRequest(requestType: RequestType, details?: string) {
  const session = await getServerSession(authOptions)
  if (!session?.user) {
    throw new Error("Unauthorized")
  }

  const request = await DataSubjectRequestService.createRequest(session.user.id, requestType, details)

  revalidatePath("/settings/privacy")

  return { success: true, requestId: request.id }
}

export async function exportUserData(userId: string, format: "json" | "csv" = "json") {
  const session = await getServerSession(authOptions)
  if (!session?.user || (session.user.id !== userId && !session.user.isAdmin)) {
    throw new Error("Unauthorized")
  }

  const data = await DataSubjectRequestService.exportUserData(userId)

  if (format === "csv") {
    // Convert JSON to CSV (simplified)
    // In a real implementation, you would use a proper JSON to CSV converter
    const jsonData = JSON.parse(data)
    let csv = ""

    // Handle user data
    if (jsonData.user) {
      csv += "User Data\n"
      for (const [key, value] of Object.entries(jsonData.user)) {
        csv += `${key},${value}\n`
      }
      csv += "\n"
    }

    // Handle customer data
    if (jsonData.customer) {
      csv += "Customer Data\n"
      for (const [key, value] of Object.entries(jsonData.customer)) {
        csv += `${key},${value}\n`
      }
      csv += "\n"
    }

    // Handle orders
    if (jsonData.orders && jsonData.orders.length > 0) {
      csv += "Orders\n"
      // Get headers
      const headers = Object.keys(jsonData.orders[0])
      csv += headers.join(",") + "\n"

      // Get data
      for (const order of jsonData.orders) {
        const row = headers.map((header) => order[header]).join(",")
        csv += row + "\n"
      }
    }

    return csv
  }

  return data
}

// Customer data encryption actions
export async function updateCustomerWithEncryption(
  customerId: string,
  data: {
    name?: string
    email?: string
    phone?: string
    address?: string
  },
) {
  const session = await getServerSession(authOptions)
  if (!session?.user) {
    throw new Error("Unauthorized")
  }

  // Get the existing customer
  const customer = await db.customers.findUnique({
    where: { id: customerId },
  })

  if (!customer) {
    throw new Error("Customer not found")
  }

  // Check permissions
  if (customer.userId !== session.user.id && !session.user.isAdmin) {
    throw new Error("Unauthorized")
  }

  // Prepare update data
  const updateData: any = {}

  // Handle name (not encrypted)
  if (data.name !== undefined) {
    updateData.name = data.name
  }

  // Handle email (encrypted)
  if (data.email !== undefined) {
    const { encryptedData, iv, authTag, keyId, searchIndex } = await EncryptedFieldHelper.encrypt(data.email, true)

    updateData.encryptedEmail = encryptedData
    updateData.emailIv = iv
    updateData.emailAuthTag = authTag
    updateData.emailKeyId = keyId
    updateData.emailSearchIndex = searchIndex
  }

  // Handle phone (encrypted)
  if (data.phone !== undefined) {
    const { encryptedData, iv, authTag, keyId, searchIndex } = await EncryptedFieldHelper.encrypt(data.phone, true)

    updateData.encryptedPhone = encryptedData
    updateData.phoneIv = iv
    updateData.phoneAuthTag = authTag
    updateData.phoneKeyId = keyId
    updateData.phoneSearchIndex = searchIndex
  }

  // Handle address (encrypted)
  if (data.address !== undefined) {
    const { encryptedData, iv, authTag, keyId } = await EncryptedFieldHelper.encrypt(data.address)

    updateData.encryptedAddress = encryptedData
    updateData.addressIv = iv
    updateData.addressAuthTag = authTag
    updateData.addressKeyId = keyId
  }

  // Update the customer
  await db.customers.update({
    where: { id: customerId },
    data: updateData,
  })

  revalidatePath("/customers")

  return { success: true }
}

// Search for customers with encrypted fields
export async function searchCustomersByEmail(email: string) {
  const session = await getServerSession(authOptions)
  if (!session?.user) {
    throw new Error("Unauthorized")
  }

  // Create a search token
  const searchToken = EncryptedFieldHelper.createSearchToken(email)

  // Search for customers
  const customers = await db.customers.findMany({
    where: {
      emailSearchIndex: searchToken,
    },
    select: {
      id: true,
      name: true,
      // Don't include encrypted fields in the result
    },
  })

  return customers
}

