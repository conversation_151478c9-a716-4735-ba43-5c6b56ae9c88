import { z } from "zod"

export const layawayItemSchema = z.object({
  productId: z.string().min(1, "Product is required"),
  variantId: z.string().optional(),
  quantity: z.coerce.number().int("Quantity must be a whole number").positive("Quantity must be positive"),
  unitPrice: z.coerce.number().min(0, "Price cannot be negative"),
})

export const layawaySchema = z.object({
  customerId: z.string().min(1, "Customer is required"),
  totalAmount: z.coerce.number().min(0, "Total amount cannot be negative"),
  depositAmount: z.coerce.number().min(0, "Deposit amount cannot be negative"),
  expiryDate: z.date({
    required_error: "Expiry date is required",
    invalid_type_error: "Invalid date format",
  }),
  paymentMethod: z.enum(
    ["CASH", "CREDIT_CARD", "DEBIT_CARD", "BANK_TRANSFER", "CHECK", "PAYPAL", "STORE_CREDIT", "GIFT_CARD", "OTHER"],
    {
      invalid_type_error: "Invalid payment method",
    },
  ),
  notes: z.string().optional(),
  items: z.array(layawayItemSchema).min(1, "Layaway must contain at least one item"),
})

export const layawayPaymentSchema = z.object({
  amount: z.coerce.number().positive("Payment amount must be positive"),
  paymentMethod: z.enum(
    ["CASH", "CREDIT_CARD", "DEBIT_CARD", "BANK_TRANSFER", "CHECK", "PAYPAL", "STORE_CREDIT", "GIFT_CARD", "OTHER"],
    {
      invalid_type_error: "Invalid payment method",
    },
  ),
  notes: z.string().optional(),
})

export type LayawayFormValues = z.infer<typeof layawaySchema>
export type LayawayItemFormValues = z.infer<typeof layawayItemSchema>
export type LayawayPaymentFormValues = z.infer<typeof layawayPaymentSchema>

