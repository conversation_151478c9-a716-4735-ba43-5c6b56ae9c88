"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"
import { Loader2, Plus, Trash2, Edit, Users } from "lucide-react"

interface CustomerSegment {
  id: string
  name: string
  description: string | null
  criteria: string | null
  isAutomatic: boolean
  _count?: {
    customers: number
  }
}

export function CustomerSegments() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [segments, setSegments] = useState<CustomerSegment[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [newSegment, setNewSegment] = useState({
    name: "",
    description: "",
    criteria: "",
    isAutomatic: false,
  })
  const [editingSegment, setEditingSegment] = useState<CustomerSegment | null>(null)

  const fetchSegments = async () => {
    try {
      setIsLoading(true)
      const response = await fetchApi("/api/customer-segments")
      setSegments(response)
    } catch (error) {
      console.error("Failed to fetch customer segments:", error)
      toast({
        title: "Error",
        description: "Failed to load customer segments",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchSegments()
  }, [toast])

  const handleAddSegment = async () => {
    try {
      await fetchApi("/api/customer-segments", {
        method: "POST",
        body: JSON.stringify(newSegment),
      })

      toast({
        title: "Success",
        description: "Customer segment added successfully",
      })

      setIsAddDialogOpen(false)
      setNewSegment({
        name: "",
        description: "",
        criteria: "",
        isAutomatic: false,
      })

      fetchSegments()
    } catch (error) {
      console.error("Failed to add customer segment:", error)
      toast({
        title: "Error",
        description: "Failed to add customer segment",
        variant: "destructive",
      })
    }
  }

  const handleEditSegment = async () => {
    if (!editingSegment) return

    try {
      await fetchApi(`/api/customer-segments/${editingSegment.id}`, {
        method: "PUT",
        body: JSON.stringify({
          name: editingSegment.name,
          description: editingSegment.description,
          criteria: editingSegment.criteria,
          isAutomatic: editingSegment.isAutomatic,
        }),
      })

      toast({
        title: "Success",
        description: "Customer segment updated successfully",
      })

      setIsEditDialogOpen(false)
      setEditingSegment(null)

      fetchSegments()
    } catch (error) {
      console.error("Failed to update customer segment:", error)
      toast({
        title: "Error",
        description: "Failed to update customer segment",
        variant: "destructive",
      })
    }
  }

  const handleDeleteSegment = async (id: string) => {
    if (!confirm("Are you sure you want to delete this segment?")) return

    try {
      await fetchApi(`/api/customer-segments/${id}`, {
        method: "DELETE",
      })

      toast({
        title: "Success",
        description: "Customer segment deleted successfully",
      })

      fetchSegments()
    } catch (error) {
      console.error("Failed to delete customer segment:", error)
      toast({
        title: "Error",
        description: "Failed to delete customer segment",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Customer Segments</CardTitle>
            <CardDescription>Create and manage customer segments for targeted marketing</CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Segment
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Add Customer Segment</DialogTitle>
                <DialogDescription>Create a new segment for grouping customers.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={newSegment.name}
                    onChange={(e) => setNewSegment({ ...newSegment, name: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newSegment.description}
                    onChange={(e) => setNewSegment({ ...newSegment, description: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="criteria">Criteria (JSON)</Label>
                  <Textarea
                    id="criteria"
                    value={newSegment.criteria}
                    onChange={(e) => setNewSegment({ ...newSegment, criteria: e.target.value })}
                    placeholder='{"minOrders": 5, "minSpend": 500}'
                  />
                  <p className="text-sm text-muted-foreground">Enter JSON criteria for automatic segmentation</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="automatic"
                    checked={newSegment.isAutomatic}
                    onCheckedChange={(checked) => setNewSegment({ ...newSegment, isAutomatic: checked })}
                  />
                  <Label htmlFor="automatic">Automatic Segmentation</Label>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" onClick={handleAddSegment}>
                  Add Segment
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {segments.length > 0 ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Customers</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {segments.map((segment) => (
                  <TableRow key={segment.id}>
                    <TableCell className="font-medium">{segment.name}</TableCell>
                    <TableCell>{segment.description || "-"}</TableCell>
                    <TableCell>
                      <Badge variant={segment.isAutomatic ? "default" : "outline"}>
                        {segment.isAutomatic ? "Automatic" : "Manual"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                        {segment._count?.customers || 0}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setEditingSegment(segment)
                            setIsEditDialogOpen(true)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleDeleteSegment(segment.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No segments found. Create your first segment to get started.
          </div>
        )}
      </CardContent>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Customer Segment</DialogTitle>
            <DialogDescription>Update the segment details.</DialogDescription>
          </DialogHeader>
          {editingSegment && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Name</Label>
                <Input
                  id="edit-name"
                  value={editingSegment.name}
                  onChange={(e) => setEditingSegment({ ...editingSegment, name: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={editingSegment.description || ""}
                  onChange={(e) => setEditingSegment({ ...editingSegment, description: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-criteria">Criteria (JSON)</Label>
                <Textarea
                  id="edit-criteria"
                  value={editingSegment.criteria || ""}
                  onChange={(e) => setEditingSegment({ ...editingSegment, criteria: e.target.value })}
                  placeholder='{"minOrders": 5, "minSpend": 500}'
                />
                <p className="text-sm text-muted-foreground">Enter JSON criteria for automatic segmentation</p>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-automatic"
                  checked={editingSegment.isAutomatic}
                  onCheckedChange={(checked) => setEditingSegment({ ...editingSegment, isAutomatic: checked })}
                />
                <Label htmlFor="edit-automatic">Automatic Segmentation</Label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button type="button" onClick={handleEditSegment}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

