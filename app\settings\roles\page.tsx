"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, Trash, Edit, Save, X } from "lucide-react"
import { PermissionGuard } from "@/components/auth/permission-guard"
import { PERMISSIONS, type Permission } from "@/lib/auth/permissions"
import type { Role } from "@/lib/auth/role-models"

export default function RolesPage() {
  const { data: session } = useSession()
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // New role form state
  const [showNewRoleForm, setShowNewRoleForm] = useState(false)
  const [newRoleName, setNewRoleName] = useState("")
  const [newRoleDescription, setNewRoleDescription] = useState("")
  const [newRolePermissions, setNewRolePermissions] = useState<Permission[]>([])

  // Edit role state
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null)
  const [editRoleName, setEditRoleName] = useState("")
  const [editRoleDescription, setEditRoleDescription] = useState("")
  const [editRolePermissions, setEditRolePermissions] = useState<Permission[]>([])

  useEffect(() => {
    fetchRoles()
  }, [])

  const fetchRoles = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/roles")

      if (!response.ok) {
        throw new Error("Failed to fetch roles")
      }

      const data = await response.json()
      setRoles(data)
    } catch (err) {
      setError("Failed to load roles")
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateRole = async () => {
    try {
      setLoading(true)
      setError("")

      const response = await fetch("/api/roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newRoleName,
          description: newRoleDescription,
          permissions: newRolePermissions,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to create role")
      }

      const newRole = await response.json()
      setRoles([...roles, newRole])
      setSuccess(`Role "${newRoleName}" created successfully`)

      // Reset form
      setNewRoleName("")
      setNewRoleDescription("")
      setNewRolePermissions([])
      setShowNewRoleForm(false)
    } catch (err: any) {
      setError(err.message || "Failed to create role")
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateRole = async () => {
    if (!editingRoleId) return

    try {
      setLoading(true)
      setError("")

      const response = await fetch(`/api/roles/${editingRoleId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: editRoleName,
          description: editRoleDescription,
          permissions: editRolePermissions,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to update role")
      }

      const updatedRole = await response.json()
      setRoles(roles.map((role) => (role.id === editingRoleId ? updatedRole : role)))
      setSuccess(`Role "${editRoleName}" updated successfully`)

      // Reset edit state
      setEditingRoleId(null)
    } catch (err: any) {
      setError(err.message || "Failed to update role")
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteRole = async (roleId: string) => {
    if (!confirm("Are you sure you want to delete this role?")) {
      return
    }

    try {
      setLoading(true)
      setError("")

      const response = await fetch(`/api/roles/${roleId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to delete role")
      }

      setRoles(roles.filter((role) => role.id !== roleId))
      setSuccess("Role deleted successfully")
    } catch (err: any) {
      setError(err.message || "Failed to delete role")
    } finally {
      setLoading(false)
    }
  }

  const startEditRole = (role: Role) => {
    setEditingRoleId(role.id)
    setEditRoleName(role.name)
    setEditRoleDescription(role.description)
    setEditRolePermissions([...role.permissions])
  }

  const cancelEditRole = () => {
    setEditingRoleId(null)
  }

  const togglePermission = (permission: Permission, isChecked: boolean, isEdit: boolean) => {
    if (isEdit) {
      if (isChecked) {
        setEditRolePermissions([...editRolePermissions, permission])
      } else {
        setEditRolePermissions(editRolePermissions.filter((p) => p !== permission))
      }
    } else {
      if (isChecked) {
        setNewRolePermissions([...newRolePermissions, permission])
      } else {
        setNewRolePermissions(newRolePermissions.filter((p) => p !== permission))
      }
    }
  }

  // Group permissions by resource
  const groupedPermissions = Object.values(PERMISSIONS).reduce(
    (acc, permission) => {
      const [resource] = permission.split(":")
      if (!acc[resource]) {
        acc[resource] = []
      }
      acc[resource].push(permission)
      return acc
    },
    {} as Record<string, Permission[]>,
  )

  return (
    <PermissionGuard
      permission={PERMISSIONS.ROLE_MANAGE}
      fallback={
        <div className="p-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p>You don't have permission to manage roles.</p>
        </div>
      }
    >
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Role Management</h1>
          <Button onClick={() => setShowNewRoleForm(!showNewRoleForm)} disabled={loading}>
            {showNewRoleForm ? (
              <>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                New Role
              </>
            )}
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-6">
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {showNewRoleForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Create New Role</CardTitle>
              <CardDescription>Define a new role with custom permissions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="roleName">Role Name</Label>
                    <Input
                      id="roleName"
                      value={newRoleName}
                      onChange={(e) => setNewRoleName(e.target.value)}
                      placeholder="e.g., Store Manager"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="roleDescription">Description</Label>
                    <Input
                      id="roleDescription"
                      value={newRoleDescription}
                      onChange={(e) => setNewRoleDescription(e.target.value)}
                      placeholder="e.g., Manages store operations"
                    />
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">Permissions</Label>
                  <div className="border rounded-md p-4">
                    <Tabs defaultValue={Object.keys(groupedPermissions)[0]}>
                      <TabsList className="mb-4">
                        {Object.keys(groupedPermissions).map((resource) => (
                          <TabsTrigger key={resource} value={resource} className="capitalize">
                            {resource}
                          </TabsTrigger>
                        ))}
                      </TabsList>

                      {Object.entries(groupedPermissions).map(([resource, permissions]) => (
                        <TabsContent key={resource} value={resource}>
                          <div className="grid grid-cols-2 gap-4">
                            {permissions.map((permission) => {
                              const [, action] = permission.split(":")
                              return (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`new-${permission}`}
                                    checked={newRolePermissions.includes(permission)}
                                    onCheckedChange={(checked) =>
                                      togglePermission(permission, checked as boolean, false)
                                    }
                                  />
                                  <Label htmlFor={`new-${permission}`} className="capitalize">
                                    {action}
                                  </Label>
                                </div>
                              )
                            })}
                          </div>
                        </TabsContent>
                      ))}
                    </Tabs>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={handleCreateRole}
                    disabled={loading || !newRoleName || newRolePermissions.length === 0}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Role"
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {loading && roles.length === 0 ? (
            <div className="col-span-2 flex justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            roles.map((role) => (
              <Card key={role.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      {editingRoleId === role.id ? (
                        <Input
                          value={editRoleName}
                          onChange={(e) => setEditRoleName(e.target.value)}
                          className="font-bold text-xl mb-1"
                        />
                      ) : (
                        <CardTitle>{role.name}</CardTitle>
                      )}

                      {editingRoleId === role.id ? (
                        <Input
                          value={editRoleDescription}
                          onChange={(e) => setEditRoleDescription(e.target.value)}
                          className="text-sm text-muted-foreground"
                        />
                      ) : (
                        <CardDescription>{role.description}</CardDescription>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      {editingRoleId === role.id ? (
                        <>
                          <Button size="sm" variant="outline" onClick={cancelEditRole}>
                            <X className="h-4 w-4" />
                          </Button>
                          <Button size="sm" onClick={handleUpdateRole} disabled={loading}>
                            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                          </Button>
                        </>
                      ) : (
                        <>
                          {role.isCustom && (
                            <>
                              <Button size="sm" variant="outline" onClick={() => startEditRole(role)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => handleDeleteRole(role.id)}
                                disabled={loading}
                              >
                                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash className="h-4 w-4" />}
                              </Button>
                            </>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {editingRoleId === role.id ? (
                    <div className="border rounded-md p-4">
                      <Tabs defaultValue={Object.keys(groupedPermissions)[0]}>
                        <TabsList className="mb-4">
                          {Object.keys(groupedPermissions).map((resource) => (
                            <TabsTrigger key={resource} value={resource} className="capitalize">
                              {resource}
                            </TabsTrigger>
                          ))}
                        </TabsList>

                        {Object.entries(groupedPermissions).map(([resource, permissions]) => (
                          <TabsContent key={resource} value={resource}>
                            <div className="grid grid-cols-2 gap-4">
                              {permissions.map((permission) => {
                                const [, action] = permission.split(":")
                                return (
                                  <div key={permission} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`edit-${role.id}-${permission}`}
                                      checked={editRolePermissions.includes(permission)}
                                      onCheckedChange={(checked) =>
                                        togglePermission(permission, checked as boolean, true)
                                      }
                                    />
                                    <Label htmlFor={`edit-${role.id}-${permission}`} className="capitalize">
                                      {action}
                                    </Label>
                                  </div>
                                )
                              })}
                            </div>
                          </TabsContent>
                        ))}
                      </Tabs>
                    </div>
                  ) : (
                    <div>
                      <h3 className="text-sm font-medium mb-2">Permissions:</h3>
                      <div className="flex flex-wrap gap-2">
                        {role.permissions.map((permission) => {
                          const [resource, action] = permission.split(":")
                          return (
                            <span
                              key={permission}
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"
                            >
                              {resource}:{action}
                            </span>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </PermissionGuard>
  )
}

