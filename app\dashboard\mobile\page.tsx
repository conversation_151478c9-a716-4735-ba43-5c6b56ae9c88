"use client"

import { useState } from "react"
import {
  Package,
  Users,
  ShoppingCart,
  TrendingUp,
  AlertTriangle,
  BarChart2,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { TouchButton } from "@/components/ui/touch-button"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useMobile } from "@/hooks/use-mobile"
import { useMobileData } from "@/hooks/use-mobile-data"
import { formatCurrency } from "@/lib/utils"
import Link from "next/link"

interface DashboardStats {
  totalSales: number
  totalOrders: number
  averageOrderValue: number
  lowStockCount: number
  salesChange: number
  ordersChange: number
}

interface TopProduct {
  id: string
  name: string
  sales: number
  stock: number
}

interface RecentOrder {
  id: string
  customerName: string
  total: number
  status: string
  date: string
}

export default function MobileDashboard() {
  const isMobile = useMobile()
  const [activeTab, setActiveTab] = useState("overview")

  // Fetch dashboard stats
  const { data: stats, isLoading: statsLoading } = useMobileData<DashboardStats>({
    fetcher: async () => {
      const res = await fetch("/api/dashboard/stats")
      if (!res.ok) throw new Error("Failed to fetch dashboard stats")
      return res.json()
    },
    initialData: {
      totalSales: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      lowStockCount: 0,
      salesChange: 0,
      ordersChange: 0,
    },
  })

  // Fetch top products
  const { data: topProducts, isLoading: productsLoading } = useMobileData<TopProduct[]>({
    fetcher: async () => {
      const res = await fetch("/api/dashboard/top-products")
      if (!res.ok) throw new Error("Failed to fetch top products")
      return res.json()
    },
    initialData: [],
  })

  // Fetch recent orders
  const { data: recentOrders, isLoading: ordersLoading } = useMobileData<RecentOrder[]>({
    fetcher: async () => {
      const res = await fetch("/api/dashboard/recent-orders")
      if (!res.ok) throw new Error("Failed to fetch recent orders")
      return res.json()
    },
    initialData: [],
  })

  // If not on mobile, redirect to regular dashboard
  if (!isMobile && typeof window !== "undefined") {
    window.location.href = "/dashboard"
    return null
  }

  return (
    <div className="space-y-4 pb-16">
      {/* Welcome Card */}
      <Card className="bg-primary text-primary-foreground">
        <CardContent className="p-4">
          <h1 className="text-xl font-bold mb-1">Welcome back!</h1>
          <p className="text-sm opacity-90">Here's what's happening with your inventory today.</p>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-3">
        <Card>
          <CardContent className="p-3">
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Sales</span>
              <div className="flex items-baseline justify-between mt-1">
                <span className="text-xl font-bold">{statsLoading ? "..." : formatCurrency(stats.totalSales)}</span>
                <div
                  className={`flex items-center text-xs ${stats.salesChange >= 0 ? "text-green-500" : "text-red-500"}`}
                >
                  {stats.salesChange >= 0 ? (
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                  )}
                  {Math.abs(stats.salesChange)}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3">
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Orders</span>
              <div className="flex items-baseline justify-between mt-1">
                <span className="text-xl font-bold">{statsLoading ? "..." : stats.totalOrders}</span>
                <div
                  className={`flex items-center text-xs ${stats.ordersChange >= 0 ? "text-green-500" : "text-red-500"}`}
                >
                  {stats.ordersChange >= 0 ? (
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                  )}
                  {Math.abs(stats.ordersChange)}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3">
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Avg. Order</span>
              <span className="text-xl font-bold mt-1">
                {statsLoading ? "..." : formatCurrency(stats.averageOrderValue)}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className={stats.lowStockCount > 0 ? "border-amber-200 bg-amber-50" : ""}>
          <CardContent className="p-3">
            <div className="flex flex-col">
              <span className={`text-sm ${stats.lowStockCount > 0 ? "text-amber-700" : "text-muted-foreground"}`}>
                Low Stock
              </span>
              <div className="flex items-center justify-between mt-1">
                <span className={`text-xl font-bold ${stats.lowStockCount > 0 ? "text-amber-700" : ""}`}>
                  {statsLoading ? "..." : stats.lowStockCount}
                </span>
                {stats.lowStockCount > 0 && <AlertTriangle className="h-4 w-4 text-amber-500" />}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="pt-4 space-y-4">
          {/* Quick Actions */}
          <div className="grid grid-cols-2 gap-3">
            <TouchButton asChild className="h-16">
              <Link href="/pos/mobile">
                <ShoppingCart className="h-5 w-5 mb-1" />
                New Sale
              </Link>
            </TouchButton>

            <TouchButton asChild variant="outline" className="h-16">
              <Link href="/products/new">
                <Package className="h-5 w-5 mb-1" />
                Add Product
              </Link>
            </TouchButton>

            <TouchButton asChild variant="outline" className="h-16">
              <Link href="/customers/new">
                <Users className="h-5 w-5 mb-1" />
                Add Customer
              </Link>
            </TouchButton>

            <TouchButton asChild variant="outline" className="h-16">
              <Link href="/reports">
                <BarChart2 className="h-5 w-5 mb-1" />
                Reports
              </Link>
            </TouchButton>
          </div>

          {/* Sales Chart Placeholder */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Sales Overview</CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="h-32 bg-muted rounded-md flex items-center justify-center">
                <TrendingUp className="h-8 w-8 text-muted-foreground opacity-50" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="pt-4 space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Top Selling Products</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {productsLoading ? (
                <div className="animate-pulse p-4 space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="h-12 bg-gray-200 rounded"></div>
                  ))}
                </div>
              ) : (
                <div className="divide-y">
                  {topProducts.map((product) => (
                    <Link
                      key={product.id}
                      href={`/products/${product.id}/mobile`}
                      className="flex items-center justify-between p-3 hover:bg-muted/50"
                    >
                      <div>
                        <h3 className="font-medium">{product.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {product.sales} sold | Stock: {product.stock}
                        </p>
                      </div>
                      <Package className="h-5 w-5 text-muted-foreground" />
                    </Link>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Low Stock Products</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {productsLoading ? (
                <div className="animate-pulse p-4 space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="h-12 bg-gray-200 rounded"></div>
                  ))}
                </div>
              ) : (
                <div className="divide-y">
                  {topProducts.filter((p) => p.stock <= 5).length > 0 ? (
                    topProducts
                      .filter((p) => p.stock <= 5)
                      .map((product) => (
                        <Link
                          key={product.id}
                          href={`/products/${product.id}/mobile`}
                          className="flex items-center justify-between p-3 hover:bg-muted/50"
                        >
                          <div>
                            <h3 className="font-medium">{product.name}</h3>
                            <p className="text-sm text-amber-500">Only {product.stock} left in stock</p>
                          </div>
                          <AlertTriangle className="h-5 w-5 text-amber-500" />
                        </Link>
                      ))
                  ) : (
                    <div className="p-4 text-center text-muted-foreground">No low stock products</div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Button asChild className="w-full">
            <Link href="/products">View All Products</Link>
          </Button>
        </TabsContent>

        <TabsContent value="orders" className="pt-4 space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Recent Orders</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {ordersLoading ? (
                <div className="animate-pulse p-4 space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded"></div>
                  ))}
                </div>
              ) : (
                <div className="divide-y">
                  {recentOrders.map((order) => (
                    <Link
                      key={order.id}
                      href={`/orders/${order.id}`}
                      className="flex items-center justify-between p-3 hover:bg-muted/50"
                    >
                      <div>
                        <h3 className="font-medium">Order #{order.id}</h3>
                        <p className="text-sm text-muted-foreground">
                          {order.customerName} • {order.date}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(order.total)}</p>
                        <span
                          className={`text-xs px-2 py-0.5 rounded-full ${
                            order.status === "completed"
                              ? "bg-green-100 text-green-700"
                              : order.status === "pending"
                                ? "bg-amber-100 text-amber-700"
                                : "bg-gray-100 text-gray-700"
                          }`}
                        >
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Button asChild className="w-full">
            <Link href="/orders">View All Orders</Link>
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  )
}

