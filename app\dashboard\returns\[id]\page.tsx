import { notFound } from "next/navigation"
import { ReturnDetails } from "@/components/returns/return-details"
import prisma from "@/lib/prisma"

interface ReturnPageProps {
  params: {
    id: string
  }
}

async function getReturn(id: string) {
  const returnData = await prisma.return.findUnique({
    where: { id },
    include: {
      customer: true,
      order: {
        include: {
          items: true,
        },
      },
      items: {
        include: {
          product: true,
          variant: true,
          orderItem: true,
        },
      },
      statusHistory: {
        orderBy: {
          createdAt: "desc",
        },
      },
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      approvedBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  })

  if (!returnData) {
    notFound()
  }

  return returnData
}

export default async function ReturnPage({ params }: ReturnPageProps) {
  const returnData = await getReturn(params.id)

  return (
    <div>
      <ReturnDetails returnData={returnData} isAdmin={true} />
    </div>
  )
}

