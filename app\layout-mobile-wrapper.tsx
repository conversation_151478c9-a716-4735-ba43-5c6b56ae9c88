"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { MobileLayout } from "@/components/layout/mobile-layout"
import { PWASetup } from "./layout-pwa"
import { useMobile } from "@/hooks/use-mobile"

export function MobileWrapper({ children }: { children: React.ReactNode }) {
  const isMobile = useMobile()
  const [isClient, setIsClient] = useState(false)

  // Set isClient to true once component mounts
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Only render on client to avoid hydration mismatch
  if (!isClient) {
    return <>{children}</>
  }

  return (
    <>
      <PWASetup />
      {isMobile ? <MobileLayout>{children}</MobileLayout> : children}
    </>
  )
}

