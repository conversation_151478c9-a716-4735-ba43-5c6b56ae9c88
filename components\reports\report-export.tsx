"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Download, Loader2 } from "lucide-react"
import type { DateRange } from "react-day-picker"
import { format } from "date-fns"
import { useToast } from "@/hooks/use-toast"

interface ReportExportProps {
  data: any
  reportType: "sales" | "inventory" | "customers"
  dateRange?: DateRange
}

export function ReportExport({ data, reportType, dateRange }: ReportExportProps) {
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()

  const handleExport = async (format: "csv" | "excel" | "pdf") => {
    if (!data) {
      toast({
        title: "No data to export",
        description: "There is no data available to export",
        variant: "destructive",
      })
      return
    }

    setIsExporting(true)

    try {
      // Generate filename based on report type and date range
      const dateRangeStr =
        dateRange?.from && dateRange?.to
          ? `_${format(dateRange.from, "yyyy-MM-dd")}_to_${format(dateRange.to, "yyyy-MM-dd")}`
          : ""

      const filename = `${reportType}_report${dateRangeStr}`

      // Prepare data for export
      const exportData = data.tableData || []

      if (format === "csv") {
        await exportToCSV(exportData, filename)
      } else if (format === "excel") {
        await exportToExcel(exportData, filename)
      } else if (format === "pdf") {
        await exportToPDF(exportData, filename, reportType)
      }

      toast({
        title: "Export successful",
        description: `Report has been exported as ${format.toUpperCase()}`,
      })
    } catch (error) {
      console.error("Export error:", error)
      toast({
        title: "Export failed",
        description: "An error occurred while exporting the report",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Export to CSV
  const exportToCSV = async (data: any[], filename: string) => {
    if (!data.length) return

    // Get all unique keys from the data
    const allKeys = Array.from(new Set(data.flatMap((item) => Object.keys(item))))

    // Create CSV content
    const csvContent = [
      // Header row
      allKeys.join(","),
      // Data rows
      ...data.map((item) =>
        allKeys
          .map((key) => {
            const value = item[key]

            // Handle different value types
            if (value === null || value === undefined) {
              return ""
            } else if (typeof value === "string") {
              // Escape quotes and wrap in quotes
              return `"${value.replace(/"/g, '""')}"`
            } else if (typeof value === "object") {
              // Convert objects to JSON strings
              return `"${JSON.stringify(value).replace(/"/g, '""')}"`
            } else {
              return String(value)
            }
          })
          .join(","),
      ),
    ].join("\n")

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")

    link.setAttribute("href", url)
    link.setAttribute("download", `${filename}.csv`)
    link.style.visibility = "hidden"

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Export to Excel (using CSV for simplicity)
  const exportToExcel = async (data: any[], filename: string) => {
    // For Excel export, we'll use the CSV function for simplicity
    // In a real app, you might want to use a library like xlsx
    return exportToCSV(data, filename)
  }

  // Export to PDF
  const exportToPDF = async (data: any[], filename: string, reportType: string) => {
    // For PDF export, we'd typically use a library like jsPDF
    // This is a simplified implementation
    const { jsPDF } = await import("jspdf")
    const { autoTable } = await import("jspdf-autotable")

    const doc = new jsPDF()

    // Add title
    const title = `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`
    doc.text(title, 14, 15)

    // Add date range if available
    if (dateRange?.from && dateRange?.to) {
      const dateRangeText = `Period: ${format(dateRange.from, "MMM dd, yyyy")} to ${format(dateRange.to, "MMM dd, yyyy")}`
      doc.text(dateRangeText, 14, 22)
    }

    // Get all unique keys from the data
    const allKeys = Array.from(new Set(data.flatMap((item) => Object.keys(item))))

    // Format headers
    const headers = allKeys.map((key) => key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, " $1"))

    // Prepare data for autoTable
    const tableData = data.map((item) =>
      allKeys.map((key) => {
        const value = item[key]

        if (value === null || value === undefined) {
          return ""
        } else if (typeof value === "object") {
          return JSON.stringify(value)
        } else {
          return String(value)
        }
      }),
    )

    // Generate the table
    autoTable(doc, {
      head: [headers],
      body: tableData,
      startY: dateRange?.from ? 30 : 25,
    })

    // Save the PDF
    doc.save(`${filename}.pdf`)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isExporting}>
          {isExporting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Exporting...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Export
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleExport("csv")}>Export as CSV</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport("excel")}>Export as Excel</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport("pdf")}>Export as PDF</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

