import type { Role } from "@prisma/client"
import "next-auth"

declare module "next-auth" {
  interface User {
    id: string
    name?: string | null
    email?: string | null
    role?: Role | null
  }

  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      role: Role
    }
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    role: Role
  }
}

