import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { redirect } from "next/navigation"
import { RetentionPolicyService } from "@/lib/retention/retention-policy"
import { RetentionPolicyTable } from "@/components/data-protection/retention-policy-table"
import { RetentionExecutionButton } from "@/components/data-protection/retention-execution-button"

export default async function DataRetentionPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user || !session.user.isAdmin) {
    redirect("/login")
  }

  // Get all retention policies
  const policies = await RetentionPolicyService.getAllPolicies()

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Data Retention Policies</h1>
        <RetentionExecutionButton />
      </div>

      <RetentionPolicyTable policies={policies} />
    </div>
  )
}

