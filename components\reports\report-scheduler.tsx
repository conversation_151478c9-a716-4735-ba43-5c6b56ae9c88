"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Loader2, Plus, Trash2, Clock, FileText, FileSpreadsheet, FileIcon as FilePdf } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"

interface ReportSchedulerProps {
  reportId?: string
  scheduleId?: string
}

export function ReportScheduler({ reportId, scheduleId }: ReportSchedulerProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [reports, setReports] = useState<any[]>([])

  // Schedule state
  const [schedule, setSchedule] = useState({
    id: "",
    name: "",
    reportId: reportId || "",
    frequency: "weekly",
    dayOfWeek: 1, // Monday
    dayOfMonth: 1,
    hour: 8,
    minute: 0,
    format: "pdf",
    recipients: [""],
    subject: "",
    message: "",
    active: true,
  })

  // Load reports and schedule data if editing
  useEffect(() => {
    async function loadData() {
      setIsLoading(true)
      try {
        // Load available reports
        const reportsData = await fetchApi("/api/reports/custom")
        setReports(reportsData)

        // If editing an existing schedule, load its data
        if (scheduleId) {
          const scheduleData = await fetchApi(`/api/reports/scheduled/${scheduleId}`)
          setSchedule({
            ...scheduleData,
            reportId: scheduleData.reportId,
          })
        } else if (reportId) {
          // If creating a new schedule for a specific report, set default subject
          const reportData = reportsData.find((r: any) => r.id === reportId)
          if (reportData) {
            setSchedule((prev) => ({
              ...prev,
              name: `${reportData.name} Schedule`,
              subject: `${reportData.name} Report`,
            }))
          }
        }
      } catch (error) {
        console.error("Error loading data:", error)
        toast({
          title: "Error",
          description: "Failed to load data",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [reportId, scheduleId, toast])

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!schedule.name) {
      toast({
        title: "Validation Error",
        description: "Schedule name is required",
        variant: "destructive",
      })
      return
    }

    if (!schedule.reportId) {
      toast({
        title: "Validation Error",
        description: "Please select a report",
        variant: "destructive",
      })
      return
    }

    if (!schedule.recipients.filter(Boolean).length) {
      toast({
        title: "Validation Error",
        description: "At least one recipient email is required",
        variant: "destructive",
      })
      return
    }

    if (!schedule.subject) {
      toast({
        title: "Validation Error",
        description: "Email subject is required",
        variant: "destructive",
      })
      return
    }

    setIsSaving(true)

    try {
      // Filter out empty recipients
      const filteredSchedule = {
        ...schedule,
        recipients: schedule.recipients.filter(Boolean),
      }

      await fetchApi("/api/reports/scheduled", {
        method: "POST",
        body: JSON.stringify(filteredSchedule),
      })

      toast({
        title: "Success",
        description: "Report schedule saved successfully",
      })

      router.push("/dashboard/reports/scheduled")
    } catch (error) {
      console.error("Error saving schedule:", error)
      toast({
        title: "Error",
        description: "Failed to save report schedule",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Handle adding a new recipient
  const addRecipient = () => {
    setSchedule((prev) => ({
      ...prev,
      recipients: [...prev.recipients, ""],
    }))
  }

  // Handle removing a recipient
  const removeRecipient = (index: number) => {
    setSchedule((prev) => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index),
    }))
  }

  // Handle updating a recipient
  const updateRecipient = (index: number, value: string) => {
    setSchedule((prev) => ({
      ...prev,
      recipients: prev.recipients.map((email, i) => (i === index ? value : email)),
    }))
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <Card>
      <form onSubmit={handleSubmit}>
        <CardHeader>
          <CardTitle>{scheduleId ? "Edit Schedule" : "Create Schedule"}</CardTitle>
          <CardDescription>Schedule automated report delivery via email</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="schedule-name">Schedule Name</Label>
              <Input
                id="schedule-name"
                value={schedule.name}
                onChange={(e) => setSchedule((prev) => ({ ...prev, name: e.target.value }))}
                placeholder="Enter schedule name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="report-id">Report</Label>
              <Select
                value={schedule.reportId}
                onValueChange={(value) => setSchedule((prev) => ({ ...prev, reportId: value }))}
                disabled={!!reportId}
              >
                <SelectTrigger id="report-id">
                  <SelectValue placeholder="Select report" />
                </SelectTrigger>
                <SelectContent>
                  {reports.map((report) => (
                    <SelectItem key={report.id} value={report.id}>
                      {report.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Schedule Settings</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Select
                  value={schedule.frequency}
                  onValueChange={(value) => setSchedule((prev) => ({ ...prev, frequency: value }))}
                >
                  <SelectTrigger id="frequency">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {schedule.frequency === "weekly" && (
                <div className="space-y-2">
                  <Label htmlFor="day-of-week">Day of Week</Label>
                  <Select
                    value={schedule.dayOfWeek.toString()}
                    onValueChange={(value) => setSchedule((prev) => ({ ...prev, dayOfWeek: Number.parseInt(value) }))}
                  >
                    <SelectTrigger id="day-of-week">
                      <SelectValue placeholder="Select day" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Sunday</SelectItem>
                      <SelectItem value="1">Monday</SelectItem>
                      <SelectItem value="2">Tuesday</SelectItem>
                      <SelectItem value="3">Wednesday</SelectItem>
                      <SelectItem value="4">Thursday</SelectItem>
                      <SelectItem value="5">Friday</SelectItem>
                      <SelectItem value="6">Saturday</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {(schedule.frequency === "monthly" || schedule.frequency === "quarterly") && (
                <div className="space-y-2">
                  <Label htmlFor="day-of-month">Day of Month</Label>
                  <Select
                    value={schedule.dayOfMonth.toString()}
                    onValueChange={(value) => setSchedule((prev) => ({ ...prev, dayOfMonth: Number.parseInt(value) }))}
                  >
                    <SelectTrigger id="day-of-month">
                      <SelectValue placeholder="Select day" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 31 }, (_, i) => (
                        <SelectItem key={i + 1} value={(i + 1).toString()}>
                          {i + 1}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="hour">Hour</Label>
                <Select
                  value={schedule.hour.toString()}
                  onValueChange={(value) => setSchedule((prev) => ({ ...prev, hour: Number.parseInt(value) }))}
                >
                  <SelectTrigger id="hour">
                    <SelectValue placeholder="Select hour" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 24 }, (_, i) => (
                      <SelectItem key={i} value={i.toString()}>
                        {i.toString().padStart(2, "0")}:00
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="minute">Minute</Label>
                <Select
                  value={schedule.minute.toString()}
                  onValueChange={(value) => setSchedule((prev) => ({ ...prev, minute: Number.parseInt(value) }))}
                >
                  <SelectTrigger id="minute">
                    <SelectValue placeholder="Select minute" />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 15, 30, 45].map((minute) => (
                      <SelectItem key={minute} value={minute.toString()}>
                        {minute.toString().padStart(2, "0")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Delivery Settings</h3>

            <div className="space-y-2">
              <Label htmlFor="format">Report Format</Label>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant={schedule.format === "csv" ? "default" : "outline"}
                  className="flex-1"
                  onClick={() => setSchedule((prev) => ({ ...prev, format: "csv" }))}
                >
                  <FileText className="mr-2 h-4 w-4" />
                  CSV
                </Button>
                <Button
                  type="button"
                  variant={schedule.format === "excel" ? "default" : "outline"}
                  className="flex-1"
                  onClick={() => setSchedule((prev) => ({ ...prev, format: "excel" }))}
                >
                  <FileSpreadsheet className="mr-2 h-4 w-4" />
                  Excel
                </Button>
                <Button
                  type="button"
                  variant={schedule.format === "pdf" ? "default" : "outline"}
                  className="flex-1"
                  onClick={() => setSchedule((prev) => ({ ...prev, format: "pdf" }))}
                >
                  <FilePdf className="mr-2 h-4 w-4" />
                  PDF
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label>Recipients</Label>
                <Button type="button" variant="outline" size="sm" onClick={addRecipient}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Recipient
                </Button>
              </div>

              {schedule.recipients.map((email, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Input
                    type="email"
                    value={email}
                    onChange={(e) => updateRecipient(index, e.target.value)}
                    placeholder="Enter email address"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeRecipient(index)}
                    disabled={schedule.recipients.length === 1 && index === 0}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            <div className="space-y-2">
              <Label htmlFor="subject">Email Subject</Label>
              <Input
                id="subject"
                value={schedule.subject}
                onChange={(e) => setSchedule((prev) => ({ ...prev, subject: e.target.value }))}
                placeholder="Enter email subject"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Email Message (Optional)</Label>
              <Textarea
                id="message"
                value={schedule.message}
                onChange={(e) => setSchedule((prev) => ({ ...prev, message: e.target.value }))}
                placeholder="Enter optional message to include in the email"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={schedule.active}
                onCheckedChange={(checked) => setSchedule((prev) => ({ ...prev, active: checked }))}
              />
              <Label htmlFor="active">Schedule is active</Label>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Clock className="mr-2 h-4 w-4" />
                {scheduleId ? "Update Schedule" : "Create Schedule"}
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}

