"use client"

import { WifiOff, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function OfflinePage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
      <WifiOff className="h-16 w-16 text-muted-foreground mb-6" />
      <h1 className="text-2xl font-bold mb-2">You're offline</h1>
      <p className="text-muted-foreground mb-8 max-w-md">
        It looks like you're not connected to the internet. Some features may be unavailable until you reconnect.
      </p>
      <Button size="lg" onClick={() => window.location.reload()} className="flex items-center gap-2">
        <RefreshCw className="h-4 w-4" />
        Try Again
      </Button>
    </div>
  )
}

