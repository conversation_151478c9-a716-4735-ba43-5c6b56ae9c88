export async function exportToCSV<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: Record<keyof T, string>,
) {
  if (!data.length) {
    throw new Error("No data to export")
  }

  // Get all unique keys from the data
  const allKeys = Array.from(new Set(data.flatMap((item) => Object.keys(item)))) as (keyof T)[]

  // Use provided headers or generate from keys
  const headerRow = headers ? allKeys.map((key) => headers[key] || String(key)) : allKeys.map((key) => String(key))

  // Create CSV content
  const csvContent = [
    // Header row
    headerRow.join(","),
    // Data rows
    ...data.map((item) =>
      allKeys
        .map((key) => {
          const value = item[key]

          // Handle different value types
          if (value === null || value === undefined) {
            return ""
          } else if (typeof value === "string") {
            // Escape quotes and wrap in quotes
            return `"${value.replace(/"/g, '""')}"`
          } else if (typeof value === "object") {
            // Convert objects to JSON strings
            return `"${JSON.stringify(value).replace(/"/g, '""')}"`
          } else {
            return String(value)
          }
        })
        .join(","),
    ),
  ].join("\n")

  // Create a blob and download link
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")

  link.setAttribute("href", url)
  link.setAttribute("download", `${filename}.csv`)
  link.style.visibility = "hidden"

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export async function exportToExcel<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: Record<keyof T, string>,
) {
  // For Excel export, we'll use the CSV function for simplicity
  // In a real app, you might want to use a library like xlsx
  return exportToCSV(data, filename, headers)
}

export async function exportToPDF<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: Record<keyof T, string>,
  title?: string,
) {
  // For PDF export, we'd typically use a library like jsPDF
  // This is a simplified implementation
  const { jsPDF } = await import("jspdf")
  const { autoTable } = await import("jspdf-autotable")

  const doc = new jsPDF()

  if (title) {
    doc.text(title, 14, 15)
  }

  // Get all unique keys from the data
  const allKeys = Array.from(new Set(data.flatMap((item) => Object.keys(item)))) as (keyof T)[]

  // Use provided headers or generate from keys
  const headerRow = headers ? allKeys.map((key) => headers[key] || String(key)) : allKeys.map((key) => String(key))

  // Prepare data for autoTable
  const tableData = data.map((item) =>
    allKeys.map((key) => {
      const value = item[key]

      if (value === null || value === undefined) {
        return ""
      } else if (typeof value === "object") {
        return JSON.stringify(value)
      } else {
        return String(value)
      }
    }),
  )

  // Generate the table
  autoTable(doc, {
    head: [headerRow],
    body: tableData,
    startY: title ? 20 : 10,
  })

  // Save the PDF
  doc.save(`${filename}.pdf`)
}

