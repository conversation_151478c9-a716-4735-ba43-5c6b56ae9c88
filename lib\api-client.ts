import { useToast } from "@/hooks/use-toast"
import { useOfflineStore } from "@/lib/store"
import { ApiError } from "@/lib/api-error"
import { useTranslation } from "next-i18next"

interface FetchOptions extends RequestInit {
  handleError?: boolean
  offlineSupport?: boolean
  offlineAction?: {
    type: "order" | "inventory" | "customer"
    data: any
  }
}

export async function fetchApi<T>(url: string, options: FetchOptions = {}): Promise<T> {
  const { toast } = useToast()
  const { t } = useTranslation("common")
  const { isOffline, addOfflineTransaction } = useOfflineStore()

  const { handleError = true, offlineSupport = false, offlineAction, ...fetchOptions } = options

  // Default headers
  const headers = new Headers(fetchOptions.headers)
  if (!headers.has("Content-Type") && !(fetchOptions.body instanceof FormData)) {
    headers.set("Content-Type", "application/json")
  }

  // Handle offline mode
  if (isOffline && offlineSupport && offlineAction) {
    // Store the action for later sync
    addOfflineTransaction(offlineAction)

    // Return a mock successful response
    return { success: true, offlineQueued: true } as unknown as T
  }

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))

      // Create a specific API error based on status code
      let error: ApiError

      switch (response.status) {
        case 400:
          error = ApiError.badRequest(errorData.error?.message || t("error.badRequest"), errorData.error?.code)
          break
        case 401:
          error = ApiError.unauthorized(errorData.error?.message || t("error.unauthorized"), errorData.error?.code)
          break
        case 403:
          error = ApiError.forbidden(errorData.error?.message || t("error.forbidden"), errorData.error?.code)
          break
        case 404:
          error = ApiError.notFound(errorData.error?.message || t("error.notFound"), errorData.error?.code)
          break
        case 409:
          error = ApiError.conflict(errorData.error?.message || t("error.conflict"), errorData.error?.code)
          break
        case 422:
          error = ApiError.unprocessableEntity(errorData.error?.message || t("error.validation"), errorData.error?.code)
          break
        case 429:
          error = ApiError.tooManyRequests(errorData.error?.message || t("error.rateLimit"), errorData.error?.code)
          break
        default:
          error = ApiError.internal(errorData.error?.message || t("error.server"), errorData.error?.code)
      }

      // Add additional context to the error
      Object.assign(error, {
        data: errorData,
        statusText: response.statusText,
      })

      throw error
    }

    // Handle empty responses
    if (response.status === 204) {
      return {} as T
    }

    return await response.json()
  } catch (error) {
    if (handleError) {
      console.error("API request failed:", error)

      toast({
        title: t("error.request"),
        description: error instanceof Error ? error.message : t("error.unexpected"),
        variant: "destructive",
      })
    }

    throw error
  }
}

