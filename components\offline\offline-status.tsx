"use client"

import { useState, useEffect } from "react"
import { Wifi, WifiOff, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { offlineDataManager } from "@/lib/offline/offline-data-manager"
import { useToast } from "@/hooks/use-toast"

export default function OfflineStatus() {
  const [isOnline, setIsOnline] = useState(true)
  const [syncStatus, setSyncStatus] = useState({ total: 0, pending: 0 })
  const [isSyncing, setIsSyncing] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    // Initialize with current status
    setIsOnline(offlineDataManager.getNetworkStatus() === "online")
    setSyncStatus(offlineDataManager.getSyncQueueStatus())

    // Set up event listeners
    const handleNetworkChange = ({ status }: { status: "online" | "offline" }) => {
      setIsOnline(status === "online")

      if (status === "online") {
        toast({
          title: "You are back online",
          description: "Your changes will be synchronized automatically.",
          variant: "default",
        })
      } else {
        toast({
          title: "You are offline",
          description: "Changes will be saved locally and synchronized when you reconnect.",
          variant: "default",
        })
      }
    }

    const handleSyncStart = () => {
      setIsSyncing(true)
    }

    const handleSyncComplete = (data: { successful: number; failed: number; remaining: number }) => {
      setIsSyncing(false)
      setSyncStatus({ total: data.remaining, pending: data.remaining })

      if (data.successful > 0) {
        toast({
          title: "Synchronization complete",
          description: `${data.successful} items synchronized successfully. ${data.failed > 0 ? `${data.failed} items failed.` : ""}`,
          variant: data.failed > 0 ? "destructive" : "default",
        })
      }
    }

    const handleSyncError = () => {
      setIsSyncing(false)
      toast({
        title: "Synchronization error",
        description: "There was an error synchronizing your data. Please try again later.",
        variant: "destructive",
      })
    }

    // Add event listeners
    offlineDataManager.addEventListener("networkStatusChange", handleNetworkChange)
    offlineDataManager.addEventListener("syncStart", handleSyncStart)
    offlineDataManager.addEventListener("syncComplete", handleSyncComplete)
    offlineDataManager.addEventListener("syncError", handleSyncError)

    // Set up interval to update sync status
    const interval = setInterval(() => {
      setSyncStatus(offlineDataManager.getSyncQueueStatus())
    }, 5000)

    // Clean up
    return () => {
      offlineDataManager.removeEventListener("networkStatusChange", handleNetworkChange)
      offlineDataManager.removeEventListener("syncStart", handleSyncStart)
      offlineDataManager.removeEventListener("syncComplete", handleSyncComplete)
      offlineDataManager.removeEventListener("syncError", handleSyncError)
      clearInterval(interval)
    }
  }, [toast])

  const handleForceSync = async () => {
    if (!isOnline || isSyncing) return

    try {
      await offlineDataManager.forceSync()
    } catch (error) {
      console.error("Error forcing sync:", error)
      toast({
        title: "Synchronization error",
        description: "There was an error synchronizing your data. Please try again later.",
        variant: "destructive",
      })
    }
  }

  if (isOnline && syncStatus.total === 0) {
    return (
      <div className="flex items-center text-sm text-green-600 dark:text-green-400">
        <Wifi className="h-4 w-4 mr-1" />
        <span>Online</span>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2">
      {isOnline ? (
        <div className="flex items-center text-sm text-green-600 dark:text-green-400">
          <Wifi className="h-4 w-4 mr-1" />
          <span>Online</span>
        </div>
      ) : (
        <div className="flex items-center text-sm text-amber-600 dark:text-amber-400">
          <WifiOff className="h-4 w-4 mr-1" />
          <span>Offline</span>
        </div>
      )}

      {syncStatus.total > 0 && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {syncStatus.total} {syncStatus.total === 1 ? "change" : "changes"} pending
          </span>
          {isOnline && (
            <Button variant="outline" size="sm" className="h-7 px-2" onClick={handleForceSync} disabled={isSyncing}>
              <RefreshCw className={`h-3.5 w-3.5 mr-1 ${isSyncing ? "animate-spin" : ""}`} />
              <span>{isSyncing ? "Syncing..." : "Sync Now"}</span>
            </Button>
          )}
        </div>
      )}
    </div>
  )
}

