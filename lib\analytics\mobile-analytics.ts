import { NativeStorage, NativeDevice, isNative } from "@/lib/native/native-features"
import { v4 as uuidv4 } from "uuid"

// Constants
const DEVICE_ID_KEY = "analytics_device_id"
const SESSION_ID_KEY = "analytics_session_id"
const SESSION_START_KEY = "analytics_session_start"
const SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes

// Types
export interface AnalyticsEvent {
  eventName: string
  properties?: Record<string, any>
  timestamp?: number
}

export interface AnalyticsSession {
  sessionId: string
  startTime: number
  duration?: number
  events: AnalyticsEvent[]
  deviceInfo?: any
  networkType?: string
  isNative: boolean
}

export interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
}

export class MobileAnalytics {
  private deviceId: string | null = null
  private sessionId: string | null = null
  private sessionStartTime = 0
  private events: AnalyticsEvent[] = []
  private performanceMetrics: PerformanceMetric[] = []
  private isInitialized = false
  private flushInterval: NodeJS.Timeout | null = null
  private apiEndpoint: string

  constructor(apiEndpoint = "/api/analytics") {
    this.apiEndpoint = apiEndpoint
  }

  /**
   * Initialize the analytics system
   */
  async init() {
    if (this.isInitialized) return

    try {
      // Get or create device ID
      this.deviceId = await this.getDeviceId()

      // Start a new session or resume existing one
      await this.startSession()

      // Set up automatic flush interval (every 5 minutes)
      this.flushInterval = setInterval(
        () => {
          this.flush().catch(console.error)
        },
        5 * 60 * 1000,
      )

      // Set up event listeners
      if (typeof window !== "undefined") {
        // Track page visibility changes
        document.addEventListener("visibilitychange", this.handleVisibilityChange)

        // Track app state changes in native apps
        if (isNative()) {
          // This is handled by the native features service
        }

        // Track performance metrics
        this.trackPerformanceMetrics()
      }

      this.isInitialized = true

      // Track initialization event
      this.trackEvent("analytics_initialized")
    } catch (error) {
      console.error("Failed to initialize analytics:", error)
    }
  }

  /**
   * Clean up analytics resources
   */
  destroy() {
    if (this.flushInterval) {
      clearInterval(this.flushInterval)
    }

    if (typeof document !== "undefined") {
      document.removeEventListener("visibilitychange", this.handleVisibilityChange)
    }

    // Flush any remaining events
    this.flush().catch(console.error)
  }

  /**
   * Track an analytics event
   */
  trackEvent(eventName: string, properties?: Record<string, any>) {
    if (!this.isInitialized) {
      console.warn("Analytics not initialized. Event not tracked:", eventName)
      return
    }

    const event: AnalyticsEvent = {
      eventName,
      properties,
      timestamp: Date.now(),
    }

    this.events.push(event)

    // If we have too many events, flush them
    if (this.events.length >= 20) {
      this.flush().catch(console.error)
    }
  }

  /**
   * Track a screen view
   */
  trackScreen(screenName: string, properties?: Record<string, any>) {
    this.trackEvent("screen_view", {
      screen_name: screenName,
      ...properties,
    })
  }

  /**
   * Track a user action
   */
  trackAction(actionName: string, category: string, properties?: Record<string, any>) {
    this.trackEvent("user_action", {
      action_name: actionName,
      category,
      ...properties,
    })
  }

  /**
   * Track an error
   */
  trackError(errorMessage: string, errorType: string, properties?: Record<string, any>) {
    this.trackEvent("error", {
      error_message: errorMessage,
      error_type: errorType,
      ...properties,
    })
  }

  /**
   * Track a performance metric
   */
  trackPerformanceMetric(name: string, value: number) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
    }

    this.performanceMetrics.push(metric)

    // Also track as an event for immediate visibility
    this.trackEvent("performance_metric", {
      metric_name: name,
      metric_value: value,
    })
  }

  /**
   * Flush events to the server
   */
  async flush() {
    if (this.events.length === 0 && this.performanceMetrics.length === 0) {
      return
    }

    if (!this.deviceId || !this.sessionId) {
      console.warn("Analytics not properly initialized. Cannot flush events.")
      return
    }

    try {
      // Get device info
      const deviceInfo = isNative() ? await NativeDevice.getInfo() : this.getWebDeviceInfo()

      // Prepare session data
      const sessionData: AnalyticsSession = {
        sessionId: this.sessionId,
        startTime: this.sessionStartTime,
        duration: Date.now() - this.sessionStartTime,
        events: [...this.events],
        deviceInfo,
        networkType: navigator.connection ? (navigator.connection as any).effectiveType : "unknown",
        isNative: isNative(),
      }

      // Clear events array
      this.events = []

      // Send data to server
      const response = await fetch(this.apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          deviceId: this.deviceId,
          session: sessionData,
          performanceMetrics: [...this.performanceMetrics],
        }),
        // Allow the request to fail silently if offline
        keepalive: true,
      })

      // Clear performance metrics
      this.performanceMetrics = []

      if (!response.ok) {
        throw new Error(`Analytics API returned ${response.status}`)
      }
    } catch (error) {
      console.error("Failed to flush analytics data:", error)

      // Put events back in the queue to try again later
      // But limit the queue size to prevent memory issues
      if (this.events.length < 100) {
        this.events = [...this.events]
      }
    }
  }

  /**
   * Get or create a device ID
   */
  private async getDeviceId(): Promise<string> {
    // Try to get existing device ID
    const existingId = await NativeStorage.get(DEVICE_ID_KEY)

    if (existingId) {
      return existingId
    }

    // Create a new device ID
    const newId = uuidv4()
    await NativeStorage.set(DEVICE_ID_KEY, newId)

    return newId
  }

  /**
   * Start a new analytics session or resume an existing one
   */
  private async startSession() {
    // Check if we have an existing session
    const existingSessionId = await NativeStorage.get(SESSION_ID_KEY)
    const existingSessionStart = await NativeStorage.get(SESSION_START_KEY)

    const now = Date.now()

    // If we have an existing session that hasn't timed out, use it
    if (existingSessionId && existingSessionStart && now - existingSessionStart < SESSION_TIMEOUT) {
      this.sessionId = existingSessionId
      this.sessionStartTime = existingSessionStart

      // Track session resumed event
      this.trackEvent("session_resumed")
    } else {
      // Create a new session
      this.sessionId = uuidv4()
      this.sessionStartTime = now

      // Save session info
      await NativeStorage.set(SESSION_ID_KEY, this.sessionId)
      await NativeStorage.set(SESSION_START_KEY, this.sessionStartTime)

      // Track session started event
      this.trackEvent("session_started")
    }
  }

  /**
   * Handle visibility change events
   */
  private handleVisibilityChange = () => {
    if (document.visibilityState === "hidden") {
      // App going to background
      this.trackEvent("app_background")
      this.flush().catch(console.error)
    } else {
      // App coming to foreground
      this.trackEvent("app_foreground")

      // Check if session has timed out
      const now = Date.now()
      if (now - this.sessionStartTime >= SESSION_TIMEOUT) {
        this.startSession().catch(console.error)
      }
    }
  }

  /**
   * Track performance metrics
   */
  private trackPerformanceMetrics() {
    if (typeof window === "undefined" || !window.performance) return

    // Track navigation timing
    if (performance.timing) {
      window.addEventListener("load", () => {
        // Wait for the page to fully load
        setTimeout(() => {
          const timing = performance.timing

          // Calculate key metrics
          const loadTime = timing.loadEventEnd - timing.navigationStart
          const domReadyTime = timing.domComplete - timing.domLoading
          const networkLatency = timing.responseEnd - timing.fetchStart

          // Track metrics
          this.trackPerformanceMetric("page_load_time", loadTime)
          this.trackPerformanceMetric("dom_ready_time", domReadyTime)
          this.trackPerformanceMetric("network_latency", networkLatency)
        }, 0)
      })
    }

    // Track memory usage if available
    if ((performance as any).memory) {
      setInterval(() => {
        const memory = (performance as any).memory
        this.trackPerformanceMetric("used_js_heap_size", memory.usedJSHeapSize)
      }, 60000) // Check every minute
    }
  }

  /**
   * Get device info for web browsers
   */
  private getWebDeviceInfo() {
    return {
      platform: "web",
      userAgent: navigator.userAgent,
      language: navigator.language,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio,
    }
  }
}

// Create a singleton instance
export const mobileAnalytics = new MobileAnalytics()

