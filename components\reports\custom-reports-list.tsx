"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { MoreHorizontal, Search, Edit, Copy, Trash2, Play, FileText, Clock, Loader2, Plus } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"
import { format } from "date-fns"

export function CustomReportsList() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [reports, setReports] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [entityFilter, setEntityFilter] = useState<string>("")

  useEffect(() => {
    fetchReports()
  }, [entityFilter])

  const fetchReports = async () => {
    setIsLoading(true)
    try {
      const url = entityFilter ? `/api/reports/custom?entity=${entityFilter}` : "/api/reports/custom"

      const data = await fetchApi(url)
      setReports(data)
    } catch (error) {
      console.error("Error fetching reports:", error)
      toast({
        title: "Error",
        description: "Failed to load custom reports",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteReport = async (reportId: string) => {
    if (!confirm("Are you sure you want to delete this report?")) {
      return
    }

    try {
      await fetchApi(`/api/reports/custom/${reportId}`, {
        method: "DELETE",
      })

      toast({
        title: "Success",
        description: "Report deleted successfully",
      })

      fetchReports()
    } catch (error) {
      console.error("Error deleting report:", error)
      toast({
        title: "Error",
        description: "Failed to delete report",
        variant: "destructive",
      })
    }
  }

  const handleDuplicateReport = async (reportId: string) => {
    try {
      const report = await fetchApi(`/api/reports/custom/${reportId}`)

      // Create a duplicate with a new name
      const duplicatedReport = {
        ...report,
        id: undefined,
        name: `${report.name} (Copy)`,
        isPublic: false,
      }

      const savedReport = await fetchApi("/api/reports/custom", {
        method: "POST",
        body: JSON.stringify(duplicatedReport),
      })

      toast({
        title: "Success",
        description: "Report duplicated successfully",
      })

      fetchReports()
    } catch (error) {
      console.error("Error duplicating report:", error)
      toast({
        title: "Error",
        description: "Failed to duplicate report",
        variant: "destructive",
      })
    }
  }

  const handleRunReport = (reportId: string) => {
    router.push(`/dashboard/reports/custom/${reportId}/run`)
  }

  const handleScheduleReport = (reportId: string) => {
    router.push(`/dashboard/reports/scheduled/new?reportId=${reportId}`)
  }

  // Filter reports based on search query
  const filteredReports = reports.filter(
    (report) =>
      report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.description?.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Get entity label
  const getEntityLabel = (entity: string) => {
    const labels: Record<string, string> = {
      products: "Products",
      orders: "Orders",
      customers: "Customers",
      inventory: "Inventory",
      sales: "Sales",
    }
    return labels[entity] || entity
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search reports..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={entityFilter} onValueChange={setEntityFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="All data sources" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All data sources</SelectItem>
            <SelectItem value="products">Products</SelectItem>
            <SelectItem value="orders">Orders</SelectItem>
            <SelectItem value="customers">Customers</SelectItem>
            <SelectItem value="inventory">Inventory</SelectItem>
            <SelectItem value="sales">Sales</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : filteredReports.length > 0 ? (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Report Name</TableHead>
                <TableHead>Data Source</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.map((report) => (
                <TableRow key={report.id}>
                  <TableCell>
                    <div className="font-medium">{report.name}</div>
                    {report.description && <div className="text-sm text-muted-foreground">{report.description}</div>}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{getEntityLabel(report.entity)}</Badge>
                  </TableCell>
                  <TableCell>{format(new Date(report.updatedAt), "MMM d, yyyy")}</TableCell>
                  <TableCell>
                    <Badge variant={report.isPublic ? "default" : "secondary"}>
                      {report.isPublic ? "Public" : "Private"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleRunReport(report.id)}>
                          <Play className="mr-2 h-4 w-4" />
                          Run Report
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/reports/custom/${report.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDuplicateReport(report.id)}>
                          <Copy className="mr-2 h-4 w-4" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleScheduleReport(report.id)}>
                          <Clock className="mr-2 h-4 w-4" />
                          Schedule
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteReport(report.id)}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center py-12 border rounded-md">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No reports found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery || entityFilter
              ? "No reports match your search criteria"
              : "Create your first custom report to get started"}
          </p>
          <Button asChild>
            <Link href="/dashboard/reports/custom/new">
              <Plus className="mr-2 h-4 w-4" />
              Create New Report
            </Link>
          </Button>
        </div>
      )}
    </div>
  )
}

