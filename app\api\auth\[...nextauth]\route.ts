import NextAuth, { type NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials"
import { db } from "@/lib/db"
import { compare } from "bcrypt"
import { SessionManager } from "@/lib/auth/session-manager"
import { MfaService } from "@/lib/auth/mfa-service"
import { PasswordPolicyService } from "@/lib/auth/password-policy"
import { RoleService } from "@/lib/auth/role-service"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        mfaCode: { label: "MFA Code", type: "text" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await db.users.findUnique({
          where: { email: credentials.email },
        })

        if (!user) {
          return null
        }

        // Check if the user is locked out
        if (user.lockedUntil && user.lockedUntil > new Date()) {
          throw new Error(`Account is locked. Try again after ${user.lockedUntil.toLocaleString()}`)
        }

        // Check if the password is correct
        const isPasswordValid = await compare(credentials.password, user.passwordHash)

        if (!isPasswordValid) {
          // Record failed attempt
          const { isLocked, attemptsRemaining, lockoutEnd } = await PasswordPolicyService.recordFailedAttempt(user.id)

          if (isLocked) {
            throw new Error(`Account is locked. Try again after ${lockoutEnd?.toLocaleString()}`)
          } else {
            throw new Error(`Invalid credentials. ${attemptsRemaining} attempts remaining.`)
          }
        }

        // Check if the password has expired
        const isExpired = await PasswordPolicyService.isPasswordExpired(user.id)
        if (isExpired) {
          throw new Error("Your password has expired. Please reset your password.")
        }

        // Check if MFA is required
        const hasMfa = await MfaService.hasMfaEnabled(user.id)
        if (hasMfa) {
          // If MFA is enabled but no code provided, return partial sign-in
          if (!credentials.mfaCode) {
            return {
              id: user.id,
              email: user.email,
              name: user.name,
              requiresMfa: true,
            }
          }

          // Verify MFA code
          const methods = await MfaService.getUserMfaMethods(user.id)
          let isValidMfa = false

          // Try each method
          for (const method of methods) {
            if (method.method === "authenticator") {
              isValidMfa = await MfaService.verifyMfaSetup(user.id, "authenticator", credentials.mfaCode)
            } else if (method.method === "email" || method.method === "sms") {
              isValidMfa = await MfaService.verifyCode(user.id, method.method, credentials.mfaCode)
            }

            if (isValidMfa) break
          }

          if (!isValidMfa) {
            throw new Error("Invalid MFA code")
          }
        }

        // Reset failed attempts on successful login
        await PasswordPolicyService.resetFailedAttempts(user.id)

        // Get user permissions
        const permissions = await RoleService.getUserPermissions(user.id)

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          permissions,
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.id = user.id
        token.permissions = user.permissions || []
        token.requiresMfa = user.requiresMfa || false
      }

      // Handle session updates
      if (trigger === "update" && session) {
        if (session.permissions) {
          token.permissions = session.permissions
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.permissions = token.permissions as string[]
        session.user.requiresMfa = token.requiresMfa as boolean
      }
      return session
    },
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  events: {
    async signIn({ user, account, isNewUser, profile, session, req }) {
      // Record the session device
      if (user) {
        const userAgent = req?.headers["user-agent"] || ""
        const ipAddress = req?.headers["x-forwarded-for"] || req?.socket?.remoteAddress || ""

        await SessionManager.recordSessionDevice(user.id, session.id, userAgent, ipAddress)
      }
    },
  },
}

const handler = NextAuth(authOptions)
export { handler as GET, handler as POST }

