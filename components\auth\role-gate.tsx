"use client"

import { useSession } from "next-auth/react"
import type { Role } from "@prisma/client"
import type { ReactNode } from "react"

interface RoleGateProps {
  children: ReactNode
  allowedRoles: Role[]
  fallback?: ReactNode
}

export function RoleGate({ children, allowedRoles, fallback = null }: RoleGateProps) {
  const { data: session } = useSession()

  if (!session?.user) {
    return fallback
  }

  const userRole = session.user.role as Role

  if (!allowedRoles.includes(userRole)) {
    return fallback
  }

  return <>{children}</>
}

