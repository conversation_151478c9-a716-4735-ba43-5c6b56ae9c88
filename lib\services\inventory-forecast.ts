import { prisma } from "@/lib/prisma"
import { addDays, subDays, format, parseISO, isValid } from "date-fns"

export interface ForecastParams {
  productId?: string
  categoryId?: string
  startDate?: string
  endDate?: string
  forecastDays?: number
}

export interface ForecastResult {
  productId: string
  productName: string
  sku: string
  currentStock: number
  reorderPoint: number
  averageDailySales: number
  projectedStockDate: string | null
  daysUntilStockout: number | null
  recommendedOrder: number
  confidence: "high" | "medium" | "low"
  historicalData: {
    date: string
    sales: number
    stock: number
  }[]
  forecastData: {
    date: string
    projectedSales: number
    projectedStock: number
  }[]
}

export async function generateInventoryForecast(params: ForecastParams): Promise<ForecastResult[]> {
  const { productId, categoryId, startDate, endDate, forecastDays = 30 } = params

  // Parse dates or use defaults
  const parsedEndDate = endDate && isValid(parseISO(endDate)) ? parseISO(endDate) : new Date()

  const parsedStartDate = startDate && isValid(parseISO(startDate)) ? parseISO(startDate) : subDays(parsedEndDate, 90) // Default to 90 days of historical data

  // Build query filters
  const where: any = {}

  if (productId) {
    where.productId = productId
  }

  if (categoryId) {
    where.product = {
      categoryId,
    }
  }

  // Get historical order data
  const orderItems = await prisma.orderItem.findMany({
    where: {
      order: {
        createdAt: {
          gte: parsedStartDate,
          lte: parsedEndDate,
        },
      },
      ...where,
    },
    include: {
      product: true,
      order: {
        select: {
          createdAt: true,
        },
      },
    },
    orderBy: {
      order: {
        createdAt: "asc",
      },
    },
  })

  // Get stock movements
  const stockMovements = await prisma.stockMovement.findMany({
    where: {
      createdAt: {
        gte: parsedStartDate,
        lte: parsedEndDate,
      },
      ...where,
    },
    include: {
      product: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  })

  // Get products
  const productIds = new Set([
    ...orderItems.map((item) => item.productId),
    ...stockMovements.map((movement) => movement.productId),
  ])

  if (productId) {
    productIds.add(productId)
  }

  const products = await prisma.product.findMany({
    where: {
      id: {
        in: Array.from(productIds),
      },
      ...(categoryId ? { categoryId } : {}),
    },
  })

  // If no products found, return empty array
  if (products.length === 0) {
    return []
  }

  // Group data by product
  const productData = products.map((product) => {
    // Get order items for this product
    const productOrderItems = orderItems.filter((item) => item.productId === product.id)

    // Get stock movements for this product
    const productStockMovements = stockMovements.filter((movement) => movement.productId === product.id)

    // Calculate daily sales
    const dailySales: Record<string, number> = {}

    productOrderItems.forEach((item) => {
      const date = format(item.order.createdAt, "yyyy-MM-dd")
      dailySales[date] = (dailySales[date] || 0) + item.quantity
    })

    // Calculate daily stock levels
    const dailyStock: Record<string, number> = {}
    let currentStock = product.stockQuantity

    // Work backwards to estimate historical stock levels
    for (let d = 0; d <= forecastDays; d++) {
      const date = format(subDays(parsedEndDate, d), "yyyy-MM-dd")
      dailyStock[date] = currentStock

      // Add sales (since we're going backwards)
      if (dailySales[date]) {
        currentStock += dailySales[date]
      }

      // Apply stock movements
      const dateMovements = productStockMovements.filter((m) => format(m.createdAt, "yyyy-MM-dd") === date)

      dateMovements.forEach((movement) => {
        if (movement.type === "incoming") {
          currentStock -= movement.quantity // Subtract since we're going backwards
        } else if (movement.type === "outgoing") {
          currentStock += movement.quantity // Add since we're going backwards
        }
      })
    }

    // Calculate average daily sales
    const salesDays = Object.keys(dailySales).length
    const totalSales = Object.values(dailySales).reduce((sum, qty) => sum + qty, 0)
    const averageDailySales = salesDays > 0 ? totalSales / salesDays : 0

    // Generate historical data array
    const historicalData = []
    for (let d = 90; d >= 0; d--) {
      const date = format(subDays(parsedEndDate, d), "yyyy-MM-dd")
      historicalData.push({
        date,
        sales: dailySales[date] || 0,
        stock: dailyStock[date] || 0,
      })
    }

    // Generate forecast data
    const forecastData = []
    let projectedStock = product.stockQuantity
    let daysUntilStockout = null
    let projectedStockoutDate = null

    for (let d = 1; d <= forecastDays; d++) {
      const date = format(addDays(parsedEndDate, d), "yyyy-MM-dd")
      const projectedSales = averageDailySales

      projectedStock = Math.max(0, projectedStock - projectedSales)

      if (projectedStock <= product.reorderPoint && daysUntilStockout === null) {
        daysUntilStockout = d
        projectedStockoutDate = date
      }

      forecastData.push({
        date,
        projectedSales,
        projectedStock,
      })
    }

    // Calculate recommended order quantity
    const recommendedOrder = Math.ceil(averageDailySales * 30) - product.stockQuantity

    // Determine confidence level based on data quality
    let confidence: "high" | "medium" | "low" = "low"

    if (salesDays >= 60 && totalSales > 0) {
      confidence = "high"
    } else if (salesDays >= 30 && totalSales > 0) {
      confidence = "medium"
    }

    return {
      productId: product.id,
      productName: product.name,
      sku: product.sku,
      currentStock: product.stockQuantity,
      reorderPoint: product.reorderPoint || 0,
      averageDailySales,
      projectedStockDate: projectedStockoutDate,
      daysUntilStockout,
      recommendedOrder: Math.max(0, recommendedOrder),
      confidence,
      historicalData,
      forecastData,
    }
  })

  return productData
}

