"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Loader2, Save } from "lucide-react"
import { updatePrivacySettings } from "@/app/actions/data-protection"
import { toast } from "@/components/ui/use-toast"

interface PrivacySettingsFormProps {
  settings: {
    dataMinimization: boolean
    anonymizeAnalytics: boolean
    limitDataSharing: boolean
    enhancedSecurity: boolean
  }
}

export function PrivacySettingsForm({ settings }: PrivacySettingsFormProps) {
  const [formData, setFormData] = useState({
    dataMinimization: settings.dataMinimization,
    anonymizeAnalytics: settings.anonymizeAnalytics,
    limitDataSharing: settings.limitDataSharing,
    enhancedSecurity: settings.enhancedSecurity,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleToggle = (field: keyof typeof formData) => {
    setFormData((prev) => ({
      ...prev,
      [field]: !prev[field],
    }))
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      await updatePrivacySettings(formData)
      toast({
        title: "Settings Updated",
        description: "Your privacy settings have been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating privacy settings:", error)
      toast({
        title: "Error",
        description: "Failed to update privacy settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Privacy Preferences</CardTitle>
        <CardDescription>Control how your data is used within the application</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="dataMinimization">Data Minimization</Label>
            <p className="text-sm text-muted-foreground">Limit the collection and storage of your personal data</p>
          </div>
          <Switch
            id="dataMinimization"
            checked={formData.dataMinimization}
            onCheckedChange={() => handleToggle("dataMinimization")}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="anonymizeAnalytics">Anonymize Analytics</Label>
            <p className="text-sm text-muted-foreground">
              Remove personally identifiable information from analytics data
            </p>
          </div>
          <Switch
            id="anonymizeAnalytics"
            checked={formData.anonymizeAnalytics}
            onCheckedChange={() => handleToggle("anonymizeAnalytics")}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="limitDataSharing">Limit Data Sharing</Label>
            <p className="text-sm text-muted-foreground">Restrict sharing your data with third parties</p>
          </div>
          <Switch
            id="limitDataSharing"
            checked={formData.limitDataSharing}
            onCheckedChange={() => handleToggle("limitDataSharing")}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="enhancedSecurity">Enhanced Security</Label>
            <p className="text-sm text-muted-foreground">Apply additional security measures to protect your data</p>
          </div>
          <Switch
            id="enhancedSecurity"
            checked={formData.enhancedSecurity}
            onCheckedChange={() => handleToggle("enhancedSecurity")}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSubmit} disabled={isSubmitting} className="w-full">
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Preferences
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

