"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ead<PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { voiceCommandService } from "@/lib/voice/voice-command-service"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

interface VoiceCommandListenerProps {
  commands?: {
    [key: string]: () => void
  }
  patterns?: {
    [key: string]: (match: RegExpMatchArray) => void
  }
  continuousMode?: boolean
}

export default function VoiceCommandListener({
  commands = {},
  patterns = {},
  continuousMode = false,
}: VoiceCommandListenerProps) {
  const [isSupported, setIsSupported] = useState<boolean | null>(null)
  const [isListening, setIsListening] = useState(false)
  const [lastCommand, setLastCommand] = useState<string | null>(null)
  const [isContinuous, setIsContinuous] = useState(continuousMode)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  const router = useRouter()

  // Check if speech recognition is supported
  useEffect(() => {
    setIsSupported(voiceCommandService.isSupported())
  }, [])

  // Register commands and patterns
  useEffect(() => {
    if (!isSupported) return

    // Clear existing commands
    voiceCommandService.clearCommands()

    // Register navigation commands
    voiceCommandService.registerCommand("go to dashboard", () => router.push("/dashboard"))
    voiceCommandService.registerCommand("go to products", () => router.push("/products"))
    voiceCommandService.registerCommand("go to inventory", () => router.push("/inventory"))
    voiceCommandService.registerCommand("go to orders", () => router.push("/orders"))
    voiceCommandService.registerCommand("go to customers", () => router.push("/customers"))
    voiceCommandService.registerCommand("go to suppliers", () => router.push("/suppliers"))
    voiceCommandService.registerCommand("go to reports", () => router.push("/reports"))
    voiceCommandService.registerCommand("go to settings", () => router.push("/settings"))

    // Register action commands
    voiceCommandService.registerCommand("create new product", () => router.push("/products/new"))
    voiceCommandService.registerCommand("create new order", () => router.push("/orders/new"))
    voiceCommandService.registerCommand("search products", () => router.push("/products?focus=search"))
    voiceCommandService.registerCommand("search orders", () => router.push("/orders?focus=search"))

    // Register custom commands
    Object.entries(commands).forEach(([command, callback]) => {
      voiceCommandService.registerCommand(command, callback)
    })

    // Register custom patterns
    Object.entries(patterns).forEach(([patternStr, callback]) => {
      const pattern = new RegExp(patternStr, "i")
      voiceCommandService.registerCommandPattern(pattern, callback)
    })

    // Register search pattern
    voiceCommandService.registerCommandPattern(/search for (.+)/i, (match) => {
      const searchTerm = match[1]
      router.push(`/search?q=${encodeURIComponent(searchTerm)}`)
    })

    // Register product search pattern
    voiceCommandService.registerCommandPattern(/find product (.+)/i, (match) => {
      const searchTerm = match[1]
      router.push(`/products?q=${encodeURIComponent(searchTerm)}`)
    })

    // Register order search pattern
    voiceCommandService.registerCommandPattern(/find order (.+)/i, (match) => {
      const searchTerm = match[1]
      router.push(`/orders?q=${encodeURIComponent(searchTerm)}`)
    })

    // Set up callbacks
    voiceCommandService.onListeningChangeCallback(setIsListening)
    voiceCommandService.onResultCallback(setLastCommand)
    voiceCommandService.onErrorCallback(setError)

    // Set continuous mode
    voiceCommandService.setContinuousMode(isContinuous)
  }, [isSupported, commands, patterns, router, isContinuous])

  // Toggle listening
  const toggleListening = () => {
    if (isListening) {
      voiceCommandService.stopListening()
    } else {
      const success = voiceCommandService.startListening()

      if (success) {
        setError(null)
        toast({
          title: "Voice Commands Activated",
          description: "Listening for voice commands...",
          variant: "default",
        })
      }
    }
  }

  // Toggle continuous mode
  const toggleContinuousMode = (enabled: boolean) => {
    setIsContinuous(enabled)
    voiceCommandService.setContinuousMode(enabled)

    toast({
      title: enabled ? "Continuous Mode Enabled" : "Continuous Mode Disabled",
      description: enabled
        ? "Voice commands will be listened for continuously"
        : "Voice commands will be listened for only when activated",
      variant: "default",
    })
  }

  if (isSupported === null) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isSupported === false) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center gap-4 py-8">
            <AlertCircle className="h-12 w-12 text-muted-foreground" />
            <div className="text-center">
              <h3 className="text-lg font-medium">Voice Commands Not Supported</h3>
              <p className="text-sm text-muted-foreground">Your browser does not support the Speech Recognition API.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isListening ? <Mic className="h-5 w-5 text-primary animate-pulse" /> : <MicOff className="h-5 w-5" />}
          <span>Voice Commands</span>
          {isListening && (
            <Badge variant="outline" className="ml-2 bg-primary/10">
              Listening
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="rounded-md bg-destructive/10 p-3 text-sm text-destructive">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <h3 className="text-sm font-medium">Available Commands</h3>
          <ul className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
            <li>"Go to dashboard"</li>
            <li>"Go to products"</li>
            <li>"Go to orders"</li>
            <li>"Create new product"</li>
            <li>"Search for [term]"</li>
            <li>"Find product [name]"</li>
          </ul>
        </div>

        {lastCommand && (
          <div className="rounded-md bg-primary/10 p-3 text-sm">
            <p>
              Last command: <strong>"{lastCommand}"</strong>
            </p>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch id="continuous-mode" checked={isContinuous} onCheckedChange={toggleContinuousMode} />
            <Label htmlFor="continuous-mode">Continuous Mode</Label>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={toggleListening} className="w-full" variant={isListening ? "destructive" : "default"}>
          {isListening ? (
            <>
              <MicOff className="mr-2 h-4 w-4" />
              Stop Listening
            </>
          ) : (
            <>
              <Mic className="mr-2 h-4 w-4" />
              Start Listening
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

