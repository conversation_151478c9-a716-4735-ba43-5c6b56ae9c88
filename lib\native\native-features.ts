import { Capacitor } from "@capacitor/core"
import { LocalNotifications, type ScheduleOptions } from "@capacitor/local-notifications"
import { Share } from "@capacitor/share"
import { Haptics, ImpactStyle } from "@capacitor/haptics"
import { StatusBar, Style } from "@capacitor/status-bar"
import { App } from "@capacitor/app"
import { Device } from "@capacitor/device"
import { Network } from "@capacitor/network"
import { Toast } from "@capacitor/toast"

// Check if running in a native app context
export const isNative = () => Capacitor.isNativePlatform()
export const isIOS = () => Capacitor.getPlatform() === "ios"
export const isAndroid = () => Capacitor.getPlatform() === "android"

// Interface for native storage
export interface NativeStorageInterface {
  get(key: string): Promise<any>
  set(key: string, value: any): Promise<void>
  remove(key: string): Promise<void>
  clear(): Promise<void>
}

// Interface for native camera
export interface NativeCameraInterface {
  takePicture(): Promise<string>
  scanBarcode(): Promise<string | null>
}

// Fallback implementation using localStorage
class WebStorage implements NativeStorageInterface {
  async get(key: string): Promise<any> {
    if (typeof window === "undefined") return null
    const value = localStorage.getItem(key)
    return value ? JSON.parse(value) : null
  }

  async set(key: string, value: any): Promise<void> {
    if (typeof window === "undefined") return
    localStorage.setItem(key, JSON.stringify(value))
  }

  async remove(key: string): Promise<void> {
    if (typeof window === "undefined") return
    localStorage.removeItem(key)
  }

  async clear(): Promise<void> {
    if (typeof window === "undefined") return
    localStorage.clear()
  }
}

// Create and export the native storage instance
export const NativeStorage: NativeStorageInterface = Capacitor.isNativePlatform()
  ? (Capacitor as any).Plugins.Storage
  : new WebStorage()

// Create and export the native camera instance (mock for web)
export const NativeCamera: NativeCameraInterface = {
  async takePicture(): Promise<string> {
    if (Capacitor.isNativePlatform() && (Capacitor as any).Plugins.Camera) {
      return await (Capacitor as any).Plugins.Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: "uri",
      })
    }
    throw new Error("Camera not available")
  },

  async scanBarcode(): Promise<string | null> {
    if (Capacitor.isNativePlatform() && (Capacitor as any).Plugins.BarcodeScanner) {
      const result = await (Capacitor as any).Plugins.BarcodeScanner.scan()
      return result.text || null
    }
    // For web, we'll simulate a barcode scan
    return `PROD${Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0")}`
  },
}

// Notifications
export const NativeNotifications = {
  async schedule(title: string, body: string, id: number = Date.now(), schedule?: { at: Date }) {
    if (!isNative()) return false

    try {
      const notificationOptions: ScheduleOptions = {
        notifications: [
          {
            title,
            body,
            id,
            schedule: schedule,
            sound: "beep.wav",
            attachments: [],
            actionTypeId: "",
            extra: {},
          },
        ],
      }

      await LocalNotifications.schedule(notificationOptions)
      return true
    } catch (error) {
      console.error("Error scheduling notification:", error)
      return false
    }
  },

  async requestPermission() {
    if (!isNative()) return false

    try {
      const result = await LocalNotifications.requestPermissions()
      return result.display === "granted"
    } catch (error) {
      console.error("Error requesting notification permission:", error)
      return false
    }
  },
}

// Sharing
export const NativeShare = {
  async share(title: string, text: string, url?: string) {
    if (!isNative()) {
      // Web fallback
      if (navigator.share) {
        try {
          await navigator.share({ title, text, url })
          return true
        } catch (error) {
          console.error("Error sharing via Web Share API:", error)
          return false
        }
      } else {
        // Copy to clipboard fallback
        if (url) {
          navigator.clipboard.writeText(url)
          return true
        }
        return false
      }
    }

    try {
      await Share.share({
        title,
        text,
        url,
        dialogTitle: title,
      })
      return true
    } catch (error) {
      console.error("Error sharing:", error)
      return false
    }
  },
}

// Haptic feedback
export const NativeHaptics = {
  async impact(style: "light" | "medium" | "heavy" = "medium") {
    if (!isNative()) return

    try {
      let impactStyle: ImpactStyle

      switch (style) {
        case "light":
          impactStyle = ImpactStyle.Light
          break
        case "heavy":
          impactStyle = ImpactStyle.Heavy
          break
        default:
          impactStyle = ImpactStyle.Medium
      }

      await Haptics.impact({ style: impactStyle })
    } catch (error) {
      console.error("Error with haptic feedback:", error)
    }
  },

  async vibrate() {
    if (!isNative()) return

    try {
      await Haptics.vibrate()
    } catch (error) {
      console.error("Error with vibration:", error)
    }
  },

  async notification(type: "success" | "warning" | "error") {
    if (!isNative()) return

    try {
      await Haptics.notification({
        type: type === "success" ? "SUCCESS" : type === "warning" ? "WARNING" : "ERROR",
      })
    } catch (error) {
      console.error("Error with notification haptic:", error)
    }
  },
}

// Status bar
export const NativeStatusBar = {
  async setColor(color: string, darkContent = true) {
    if (!isNative() || !isAndroid()) return

    try {
      await StatusBar.setBackgroundColor({ color })
      await StatusBar.setStyle({ style: darkContent ? Style.Dark : Style.Light })
    } catch (error) {
      console.error("Error setting status bar color:", error)
    }
  },

  async hide() {
    if (!isNative()) return

    try {
      await StatusBar.hide()
    } catch (error) {
      console.error("Error hiding status bar:", error)
    }
  },

  async show() {
    if (!isNative()) return

    try {
      await StatusBar.show()
    } catch (error) {
      console.error("Error showing status bar:", error)
    }
  },
}

// App lifecycle
export const NativeApp = {
  onResume(callback: () => void) {
    if (!isNative()) return () => {}

    const listener = App.addListener("appStateChange", ({ isActive }) => {
      if (isActive) {
        callback()
      }
    })

    return () => {
      listener.remove()
    }
  },

  onPause(callback: () => void) {
    if (!isNative()) return () => {}

    const listener = App.addListener("appStateChange", ({ isActive }) => {
      if (!isActive) {
        callback()
      }
    })

    return () => {
      listener.remove()
    }
  },

  async getInfo() {
    if (!isNative()) return null

    try {
      return await App.getInfo()
    } catch (error) {
      console.error("Error getting app info:", error)
      return null
    }
  },

  async exitApp() {
    if (!isNative()) return

    try {
      await App.exitApp()
    } catch (error) {
      console.error("Error exiting app:", error)
    }
  },
}

// Device info
export const NativeDevice = {
  async getInfo() {
    if (!isNative()) return null

    try {
      return await Device.getInfo()
    } catch (error) {
      console.error("Error getting device info:", error)
      return null
    }
  },

  async getBatteryInfo() {
    if (!isNative()) return null

    try {
      return await Device.getBatteryInfo()
    } catch (error) {
      console.error("Error getting battery info:", error)
      return null
    }
  },
}

// Network
export const NativeNetwork = {
  async getStatus() {
    if (!isNative()) {
      return { connected: navigator.onLine, connectionType: navigator.onLine ? "wifi" : "none" }
    }

    try {
      return await Network.getStatus()
    } catch (error) {
      console.error("Error getting network status:", error)
      return { connected: false, connectionType: "none" }
    }
  },

  onNetworkStatusChange(callback: (status: { connected: boolean; connectionType: string }) => void) {
    if (!isNative()) {
      const handleOnline = () => callback({ connected: true, connectionType: "wifi" })
      const handleOffline = () => callback({ connected: false, connectionType: "none" })

      window.addEventListener("online", handleOnline)
      window.addEventListener("offline", handleOffline)

      return () => {
        window.removeEventListener("online", handleOnline)
        window.removeEventListener("offline", handleOffline)
      }
    }

    const listener = Network.addListener("networkStatusChange", callback)

    return () => {
      listener.remove()
    }
  },
}

// Toast
export const NativeToast = {
  async show(text: string, duration: "short" | "long" = "short") {
    if (!isNative()) {
      // Web fallback
      alert(text)
      return
    }

    try {
      await Toast.show({
        text,
        duration: duration === "short" ? "short" : "long",
        position: "bottom",
      })
    } catch (error) {
      console.error("Error showing toast:", error)
    }
  },
}

