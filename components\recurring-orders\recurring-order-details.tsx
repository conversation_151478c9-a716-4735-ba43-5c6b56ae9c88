"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { toast } from "@/hooks/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Loader2, ArrowLeft, CheckCircle, XCircle, Play, Pause } from "lucide-react"
import { formatDate, formatCurrency } from "@/lib/utils"

interface RecurringOrderDetailsProps {
  recurringOrder: any
}

export function RecurringOrderDetails({ recurringOrder }: RecurringOrderDetailsProps) {
  const router = useRouter()
  const [isUpdating, setIsUpdating] = useState(false)
  const [status, setStatus] = useState(recurringOrder.status)
  const [notes, setNotes] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            <Play className="mr-1 h-3 w-3" />
            Active
          </Badge>
        )
      case "PAUSED":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Pause className="mr-1 h-3 w-3" />
            Paused
          </Badge>
        )
      case "COMPLETED":
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        )
      case "CANCELLED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Cancelled
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case "DAILY":
        return "Daily"
      case "WEEKLY":
        return "Weekly"
      case "BIWEEKLY":
        return "Bi-weekly"
      case "MONTHLY":
        return "Monthly"
      case "QUARTERLY":
        return "Quarterly"
      case "BIANNUALLY":
        return "Bi-annually"
      case "ANNUALLY":
        return "Annually"
      default:
        return frequency
    }
  }

  const updateRecurringOrderStatus = async () => {
    try {
      setIsUpdating(true)

      const response = await fetch(`/api/recurring-orders/${recurringOrder.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
          notes,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to update recurring order")
      }

      toast({
        title: "Recurring Order Updated",
        description: `Recurring order has been updated successfully.`,
      })

      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update recurring order",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const processRecurringOrder = async () => {
    try {
      setIsProcessing(true)
      setIsConfirmDialogOpen(false)

      const response = await fetch(`/api/recurring-orders/${recurringOrder.id}/process`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to process recurring order")
      }

      const result = await response.json()

      toast({
        title: "Order Created",
        description: `Order #${result.order.orderNumber} has been created successfully.`,
      })

      router.refresh()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to process recurring order",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => router.back()} className="flex items-center" asChild>
          <Link href="/dashboard/recurring-orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Recurring Orders
          </Link>
        </Button>
        {getStatusBadge(recurringOrder.status)}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Recurring Order</CardTitle>
              <CardDescription>Created on {formatDate(recurringOrder.createdAt)}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Customer</h3>
                  <p>
                    {recurringOrder.customer.firstName} {recurringOrder.customer.lastName}
                  </p>
                  {recurringOrder.customer.email && <p>{recurringOrder.customer.email}</p>}
                  {recurringOrder.customer.phone && <p>{recurringOrder.customer.phone}</p>}
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Order Details</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <span>{getStatusBadge(recurringOrder.status)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Frequency:</span>
                      <span>{getFrequencyText(recurringOrder.frequency)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Next Order Date:</span>
                      <span>{formatDate(recurringOrder.nextOrderDate)}</span>
                    </div>
                    {recurringOrder.endDate && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">End Date:</span>
                        <span>{formatDate(recurringOrder.endDate)}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Payment Method:</span>
                      <span>{recurringOrder.paymentMethod.replace(/_/g, " ")}</span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-medium mb-4">Order Items</h3>
                <div className="space-y-4">
                  {recurringOrder.items.map((item: any) => (
                    <div key={item.id} className="flex justify-between items-center border-b pb-4">
                      <div>
                        <p className="font-medium">{item.product.name}</p>
                        {item.variant && <p className="text-sm text-muted-foreground">Variant: {item.variant.sku}</p>}
                        <p className="text-sm text-muted-foreground">
                          {formatCurrency(item.unitPrice)} x {item.quantity}
                        </p>
                      </div>
                      <p className="font-medium">{formatCurrency(item.total)}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-muted p-4 rounded-md">
                <div className="space-y-2">
                  <div className="flex justify-between font-medium">
                    <span>Total Amount Per Order:</span>
                    <span>
                      {formatCurrency(recurringOrder.items.reduce((sum: number, item: any) => sum + item.total, 0))}
                    </span>
                  </div>
                </div>
              </div>

              {recurringOrder.shippingAddress && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Shipping Address</h3>
                  <p className="whitespace-pre-line">{recurringOrder.shippingAddress}</p>
                </div>
              )}

              {recurringOrder.billingAddress && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Billing Address</h3>
                  <p className="whitespace-pre-line">{recurringOrder.billingAddress}</p>
                </div>
              )}

              {recurringOrder.notes && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Notes</h3>
                  <p className="text-muted-foreground">{recurringOrder.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {recurringOrder.status === "ACTIVE" || recurringOrder.status === "PAUSED" ? (
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {recurringOrder.status === "ACTIVE" && (
                  <>
                    <Button className="w-full" onClick={() => setIsConfirmDialogOpen(true)}>
                      <Play className="mr-2 h-4 w-4" /> Process Now
                    </Button>
                    <Button
                      className="w-full"
                      variant="outline"
                      onClick={() => {
                        setStatus("PAUSED")
                        setNotes("Recurring order paused by staff")
                        updateRecurringOrderStatus()
                      }}
                    >
                      <Pause className="mr-2 h-4 w-4" /> Pause
                    </Button>
                  </>
                )}

                {recurringOrder.status === "PAUSED" && (
                  <Button
                    className="w-full"
                    onClick={() => {
                      setStatus("ACTIVE")
                      setNotes("Recurring order activated by staff")
                      updateRecurringOrderStatus()
                    }}
                  >
                    <Play className="mr-2 h-4 w-4" /> Resume
                  </Button>
                )}

                <Button
                  className="w-full"
                  variant="destructive"
                  onClick={() => {
                    setStatus("CANCELLED")
                    setNotes("Recurring order cancelled by staff")
                    updateRecurringOrderStatus()
                  }}
                >
                  <XCircle className="mr-2 h-4 w-4" /> Cancel
                </Button>
              </CardContent>
            </Card>
          ) : null}

          <Card>
            <CardHeader>
              <CardTitle>Order History</CardTitle>
              <CardDescription>Previous orders generated from this recurring order</CardDescription>
            </CardHeader>
            <CardContent>
              {recurringOrder.orderHistory.length === 0 ? (
                <p className="text-center text-muted-foreground py-4">No orders generated yet</p>
              ) : (
                <div className="space-y-4">
                  {recurringOrder.orderHistory.map((history: any) => (
                    <div key={history.id} className="flex justify-between items-center border-b pb-4">
                      <div>
                        <p className="font-medium">Order #{history.orderId}</p>
                        <p className="text-sm text-muted-foreground">Scheduled: {formatDate(history.scheduledDate)}</p>
                        <p className="text-sm text-muted-foreground">Processed: {formatDate(history.processedDate)}</p>
                      </div>
                      <Badge
                        variant={history.status === "COMPLETED" ? "default" : "outline"}
                        className={
                          history.status === "COMPLETED"
                            ? "bg-green-100 text-green-800 border-green-200"
                            : "bg-yellow-100 text-yellow-800 border-yellow-200"
                        }
                      >
                        {history.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Recurring Order</DialogTitle>
            <DialogDescription>
              Are you sure you want to process this recurring order now? This will create a new order and update the
              next scheduled date.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={processRecurringOrder} disabled={isProcessing}>
              {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Process Now
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

