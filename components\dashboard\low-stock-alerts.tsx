"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useSocket } from "@/components/socket-provider"
import { AlertTriangle } from "lucide-react"

interface LowStockItem {
  id: string
  name: string
  sku: string
  stockQuantity: number
  reorderPoint: number
  category: {
    name: string
  }
}

export function LowStockAlerts() {
  const [items, setItems] = useState<LowStockItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { emit, socket, status } = useSocket()

  useEffect(() => {
    if (status === "connected") {
      setIsLoading(true)

      const syncId = `low_stock_sync_${Date.now()}`

      const handleSyncResponse = (response: any) => {
        if (response.id === syncId) {
          setItems(response.data.items)
          setIsLoading(false)

          // Remove the event listener
          socket?.off("sync_response", handleSyncResponse)
        }
      }

      // Listen for the response
      socket?.on("sync_response", handleSyncResponse)

      // Request low stock items
      emit("sync_request", {
        id: syncId,
        timestamp: Date.now(),
        type: "low_stock",
      })

      // Fallback in case we don't get a response
      const timeout = setTimeout(() => {
        if (isLoading) {
          // Use mock data if we don't get a response
          setItems([
            {
              id: "prod1",
              name: "Wireless Headphones",
              sku: "WH-BT-001",
              stockQuantity: 3,
              reorderPoint: 10,
              category: { name: "Electronics" },
            },
            {
              id: "prod2",
              name: "Fitness Tracker",
              sku: "FT-HR-001",
              stockQuantity: 2,
              reorderPoint: 5,
              category: { name: "Electronics" },
            },
            {
              id: "prod3",
              name: "Bluetooth Speaker",
              sku: "BS-BT-001",
              stockQuantity: 4,
              reorderPoint: 5,
              category: { name: "Electronics" },
            },
          ])
          setIsLoading(false)
          socket?.off("sync_response", handleSyncResponse)
        }
      }, 3000)

      return () => {
        clearTimeout(timeout)
        socket?.off("sync_response", handleSyncResponse)
      }
    }
  }, [status, emit, socket, isLoading])

  // Listen for low stock alerts
  useEffect(() => {
    if (!socket) return

    const handleLowStockAlert = (data: any) => {
      const alertData = data.data

      // Check if the item is already in the list
      setItems((prev) => {
        const exists = prev.some((item) => item.id === alertData.productId)

        if (exists) {
          // Update the existing item
          return prev.map((item) =>
            item.id === alertData.productId ? { ...item, stockQuantity: alertData.currentStock } : item,
          )
        } else {
          // Add the new item if we have all the required data
          if (alertData.productName) {
            return [
              ...prev,
              {
                id: alertData.productId,
                name: alertData.productName,
                sku: alertData.sku || "Unknown",
                stockQuantity: alertData.currentStock,
                reorderPoint: alertData.reorderPoint,
                category: { name: alertData.category || "Unknown" },
              },
            ]
          }
          return prev
        }
      })
    }

    socket.on("low_stock_alert", handleLowStockAlert)

    return () => {
      socket.off("low_stock_alert", handleLowStockAlert)
    }
  }, [socket])

  return (
    <Card className="col-span-1">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <AlertTriangle className="mr-2 h-4 w-4 text-amber-500" />
          Low Stock Alerts
        </CardTitle>
        <CardDescription>Items that need to be reordered</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        ) : items.length === 0 ? (
          <p className="text-center py-4 text-muted-foreground">No low stock items</p>
        ) : (
          <div className="space-y-3">
            {items.map((item) => (
              <div key={item.id} className="flex flex-col space-y-1 border-b pb-2 last:border-0">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{item.name}</span>
                  <Badge variant={item.stockQuantity === 0 ? "destructive" : "outline"} className="ml-2">
                    {item.stockQuantity} left
                  </Badge>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{item.sku}</span>
                  <span>{item.category.name}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

