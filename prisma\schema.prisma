// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id                String             @id @default(cuid())
  name              String?
  email             String?            @unique
  emailVerified     DateTime?
  image             String?
  password          String?
  role              String             @default("USER")
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  accounts          Account[]
  sessions          Session[]
  deviceTokens      DeviceToken[]
  products          Product[]
  categories        Category[]
  suppliers         Supplier[]
  customers         Customer[]
  orders            Order[]
  notifications     Notification[]
  notificationPrefs NotificationPrefs?
  backups           Backup[]
  batchOperations   BatchOperation[]
  returns           Return[]           @relation("ReturnCreatedBy")
  returnApprovals   Return[]           @relation("ReturnApprovedBy")
  loyaltySettings   LoyaltySettings?
  archives          Archive[]
  auditLogs         AuditLog[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Product {
  id                  String               @id @default(cuid())
  name                String
  description         String?
  sku                 String               @unique
  barcode             String?
  price               Float
  cost                Float?
  quantity            Int                  @default(0)
  lowStockThreshold   Int?
  categoryId          String?
  supplierId          String?
  imageUrl            String?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  category            Category?            @relation(fields: [categoryId], references: [id])
  supplier            Supplier?            @relation(fields: [supplierId], references: [id])
  orderItems          OrderItem[]
  images              ProductImage[]
  optionGroups        OptionGroup[]
  variants            ProductVariant[]
  serialNumbers       SerialNumber[]
  inventoryHistory    InventoryHistory[]
  returnItems         ReturnItem[]
  layawayItems        LayawayItem[]
  recurringOrderItems RecurringOrderItem[]
  userId              String
  user                User                 @relation(fields: [userId], references: [id])
}

model ProductImage {
  id        String   @id @default(cuid())
  url       String
  alt       String?
  position  Int      @default(0)
  productId String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model OptionGroup {
  id        String   @id @default(cuid())
  name      String
  position  Int      @default(0)
  productId String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  options   Option[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Option {
  id            String         @id @default(cuid())
  name          String
  position      Int            @default(0)
  optionGroupId String
  optionGroup   OptionGroup    @relation(fields: [optionGroupId], references: [id], onDelete: Cascade)
  variantValues VariantValue[]
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
}

model ProductVariant {
  id                  String               @id @default(cuid())
  sku                 String               @unique
  barcode             String?
  price               Float
  compareAtPrice      Float?
  costPrice           Float?
  inventoryLevel      Int                  @default(0)
  weight              Float?
  dimensions          String?
  isDefault           Boolean              @default(false)
  isActive            Boolean              @default(true)
  productId           String
  product             Product              @relation(fields: [productId], references: [id], onDelete: Cascade)
  variantValues       VariantValue[]
  orderItems          OrderItem[]
  serialNumbers       SerialNumber[]
  returnItems         ReturnItem[]
  layawayItems        LayawayItem[]
  recurringOrderItems RecurringOrderItem[]
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
}

model VariantValue {
  id               String         @id @default(cuid())
  productVariantId String
  optionId         String
  productVariant   ProductVariant @relation(fields: [productVariantId], references: [id], onDelete: Cascade)
  option           Option         @relation(fields: [optionId], references: [id])
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@unique([productVariantId, optionId])
}

model Category {
  id          String     @id @default(cuid())
  name        String     @unique
  description String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  products    Product[]
  slug        String     @unique
  parentId    String?
  parent      Category?  @relation("CategoryToCategory", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryToCategory")
  userId      String
  user        User       @relation(fields: [userId], references: [id])
}

model Supplier {
  id          String    @id @default(cuid())
  name        String
  email       String?
  phone       String?
  address     String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
  contactName String?
  notes       String?
  isActive    Boolean   @default(true)
  userId      String
  user        User      @relation(fields: [userId], references: [id])
}

model Customer {
  id                  String               @id @default(cuid())
  name                String
  email               String?
  phone               String?
  address             String?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  orders              Order[]
  firstName           String
  lastName            String
  city                String?
  state               String?
  postalCode          String?
  country             String?
  notes               String?
  isActive            Boolean              @default(true)
  // Customer Loyalty Program
  loyaltyPoints       Int                  @default(0)
  loyaltyTier         String?
  // Customer Segmentation
  tags                CustomerTag[]
  segments            CustomerSegment[]
  // Credit Management
  storeCredit         Float                @default(0)
  creditLimit         Float?
  creditHold          Boolean              @default(false)
  // Other relations
  userId              String
  user                User                 @relation(fields: [userId], references: [id])
  returns             Return[]
  layaways            Layaway[]
  recurringOrders     RecurringOrder[]
  loyaltyTransactions LoyaltyTransaction[]
  creditTransactions  CreditTransaction[]
}

model Order {
  id                    String               @id @default(cuid())
  orderNumber           String               @unique
  customerId            String?
  status                String               @default("PENDING")
  total                 Float
  tax                   Float?
  discount              Float?
  notes                 String?
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  customer              Customer?            @relation(fields: [customerId], references: [id])
  items                 OrderItem[]
  paymentStatus         PaymentStatus        @default(UNPAID)
  paymentMethod         PaymentMethod?
  subtotal              Float
  shipping              Float                @default(0)
  shippingAddress       String?
  billingAddress        String?
  fulfillmentDate       DateTime?
  // Loyalty points
  loyaltyPointsEarned   Int?
  loyaltyPointsRedeemed Int?
  // Store credit
  storeCreditUsed       Float?               @default(0)
  userId                String
  user                  User                 @relation(fields: [userId], references: [id])
  statusHistory         OrderStatusHistory[]
  returns               Return[]
  serialNumbers         SerialNumber[]
  loyaltyTransactions   LoyaltyTransaction[]
  creditTransactions    CreditTransaction[]
}

model OrderItem {
  id          String          @id @default(cuid())
  orderId     String
  productId   String
  quantity    Int
  price       Float
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  order       Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product     Product         @relation(fields: [productId], references: [id])
  unitPrice   Float
  subtotal    Float
  tax         Float
  total       Float
  variantId   String?
  variant     ProductVariant? @relation(fields: [variantId], references: [id])
  returnItems ReturnItem[]
}

// Device token for push notifications
model DeviceToken {
  token       String   @id
  userId      String
  createdAt   DateTime @default(now())
  lastUpdated DateTime
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token, userId])
}

// Notification log
model NotificationLog {
  id             String   @id @default(cuid())
  title          String
  body           String
  channel        String
  sentBy         String
  recipientCount Int
  successCount   Int
  failureCount   Int
  createdAt      DateTime @default(now())
}

model OrderStatusHistory {
  id        String      @id @default(cuid())
  status    OrderStatus
  notes     String?
  createdAt DateTime    @default(now())
  orderId   String
  order     Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

enum ReturnStatus {
  REQUESTED
  APPROVED
  RECEIVED
  INSPECTED
  COMPLETED
  REJECTED
  PARTIALLY_REFUNDED
  REFUNDED
}

enum RefundMethod {
  ORIGINAL_PAYMENT
  STORE_CREDIT
  EXCHANGE
  GIFT_CARD
  BANK_TRANSFER
  CHECK
  OTHER
}

model Return {
  id            String                @id @default(cuid())
  returnNumber  String                @unique
  status        ReturnStatus          @default(REQUESTED)
  reason        String
  notes         String?
  refundAmount  Float?
  refundMethod  RefundMethod?
  refundDate    DateTime?
  createdAt     DateTime              @default(now())
  updatedAt     DateTime              @updatedAt
  orderId       String
  customerId    String
  createdById   String
  approvedById  String?
  order         Order                 @relation(fields: [orderId], references: [id])
  customer      Customer              @relation(fields: [customerId], references: [id])
  createdBy     User                  @relation("ReturnCreatedBy", fields: [createdById], references: [id])
  approvedBy    User?                 @relation("ReturnApprovedBy", fields: [approvedById], references: [id])
  items         ReturnItem[]
  statusHistory ReturnStatusHistory[]
}

model ReturnItem {
  id           String          @id @default(cuid())
  quantity     Int
  reason       String
  condition    String
  refundAmount Float?
  returnId     String
  orderItemId  String
  productId    String
  variantId    String?
  return       Return          @relation(fields: [returnId], references: [id], onDelete: Cascade)
  orderItem    OrderItem       @relation(fields: [orderItemId], references: [id])
  product      Product         @relation(fields: [productId], references: [id])
  variant      ProductVariant? @relation(fields: [variantId], references: [id])
}

model ReturnStatusHistory {
  id        String       @id @default(cuid())
  status    ReturnStatus
  notes     String?
  createdAt DateTime     @default(now())
  returnId  String
  return    Return       @relation(fields: [returnId], references: [id], onDelete: Cascade)
}

enum SerialNumberStatus {
  IN_STOCK
  SOLD
  RESERVED
  DEFECTIVE
  RETURNED
}

model SerialNumber {
  id           String             @id @default(cuid())
  serialNumber String
  status       SerialNumberStatus @default(IN_STOCK)
  notes        String?
  purchaseDate DateTime?
  soldDate     DateTime?
  productId    String
  variantId    String?
  orderId      String?
  product      Product            @relation(fields: [productId], references: [id])
  variant      ProductVariant?    @relation(fields: [variantId], references: [id])
  order        Order?             @relation(fields: [orderId], references: [id])
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt

  @@unique([productId, serialNumber])
}

model InventoryHistory {
  id        String   @id @default(cuid())
  productId String
  quantity  Int
  type      String // "PURCHASE", "SALE", "ADJUSTMENT", "RETURN"
  reference String? // Order ID, Return ID, etc.
  notes     String?
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id])
}

model Notification {
  id        String   @id @default(cuid())
  title     String
  message   String
  type      String // "INFO", "WARNING", "ERROR", "SUCCESS"
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model NotificationPrefs {
  id                  String  @id @default(cuid())
  lowStockAlerts      Boolean @default(true)
  orderNotifications  Boolean @default(true)
  returnNotifications Boolean @default(true)
  systemAlerts        Boolean @default(true)
  emailNotifications  Boolean @default(true)
  pushNotifications   Boolean @default(false)
  userId              String  @unique
  user                User    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

enum BackupStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

model Backup {
  id        String       @id @default(cuid())
  filename  String
  size      Int
  status    BackupStatus @default(COMPLETED)
  notes     String?
  createdAt DateTime     @default(now())
  userId    String
  user      User         @relation(fields: [userId], references: [id])
}

enum BatchOperationType {
  IMPORT
  EXPORT
  UPDATE
  DELETE
}

enum BatchOperationStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

model BatchOperation {
  id             String               @id @default(cuid())
  name           String
  type           BatchOperationType
  status         BatchOperationStatus @default(PENDING)
  totalItems     Int                  @default(0)
  processedItems Int                  @default(0)
  successItems   Int                  @default(0)
  failedItems    Int                  @default(0)
  fileUrl        String?
  resultFileUrl  String?
  errorLog       String?
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt
  completedAt    DateTime?
  userId         String
  user           User                 @relation(fields: [userId], references: [id])
}

enum LayawayStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  EXPIRED
}

model Layaway {
  id              String           @id @default(cuid())
  layawayNumber   String           @unique
  status          LayawayStatus    @default(ACTIVE)
  totalAmount     Float
  depositAmount   Float
  balanceDue      Float
  paymentSchedule String?
  expiryDate      DateTime
  notes           String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  customerId      String
  customer        Customer         @relation(fields: [customerId], references: [id])
  items           LayawayItem[]
  payments        LayawayPayment[]
}

model LayawayItem {
  id        String          @id @default(cuid())
  quantity  Int
  unitPrice Float
  total     Float
  layawayId String
  productId String
  variantId String?
  layaway   Layaway         @relation(fields: [layawayId], references: [id], onDelete: Cascade)
  product   Product         @relation(fields: [productId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])
}

model LayawayPayment {
  id            String        @id @default(cuid())
  amount        Float
  paymentMethod PaymentMethod
  notes         String?
  createdAt     DateTime      @default(now())
  layawayId     String
  layaway       Layaway       @relation(fields: [layawayId], references: [id], onDelete: Cascade)
}

enum RecurringOrderStatus {
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

enum RecurringOrderFrequency {
  DAILY
  WEEKLY
  BIWEEKLY
  MONTHLY
  QUARTERLY
  BIANNUALLY
  ANNUALLY
}

model RecurringOrder {
  id              String                  @id @default(cuid())
  status          RecurringOrderStatus    @default(ACTIVE)
  frequency       RecurringOrderFrequency
  nextOrderDate   DateTime
  endDate         DateTime?
  paymentMethod   PaymentMethod
  shippingAddress String?
  billingAddress  String?
  notes           String?
  createdAt       DateTime                @default(now())
  updatedAt       DateTime                @updatedAt
  customerId      String
  customer        Customer                @relation(fields: [customerId], references: [id])
  items           RecurringOrderItem[]
  orderHistory    RecurringOrderHistory[]
}

model RecurringOrderItem {
  id               String          @id @default(cuid())
  quantity         Int
  unitPrice        Float
  total            Float
  recurringOrderId String
  productId        String
  variantId        String?
  recurringOrder   RecurringOrder  @relation(fields: [recurringOrderId], references: [id], onDelete: Cascade)
  product          Product         @relation(fields: [productId], references: [id])
  variant          ProductVariant? @relation(fields: [variantId], references: [id])
}

model RecurringOrderHistory {
  id               String         @id @default(cuid())
  orderId          String
  scheduledDate    DateTime
  processedDate    DateTime
  status           String
  notes            String?
  recurringOrderId String
  recurringOrder   RecurringOrder @relation(fields: [recurringOrderId], references: [id], onDelete: Cascade)
}

enum ArchiveStatus {
  PENDING
  PROCESSING
  COMPLETED
  RESTORING
  RESTORED
  FAILED
}

model Archive {
  id          String        @id @default(cuid())
  name        String
  entityType  String
  status      ArchiveStatus @default(PENDING)
  recordCount Int?
  fileSize    Int?
  filePath    String?
  error       String?
  createdAt   DateTime      @default(now())
  completedAt DateTime?
  restoredAt  DateTime?
  userId      String
  user        User          @relation(fields: [userId], references: [id])
}

model AuditLog {
  id         String   @id @default(cuid())
  action     String
  entityType String
  entityId   String?
  details    String?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())
  userId     String
  user       User     @relation(fields: [userId], references: [id])
}

enum PaymentStatus {
  PAID
  UNPAID
  PARTIAL
  REFUNDED
  CANCELLED
}

enum PaymentMethod {
  CASH
  CREDIT_CARD
  DEBIT_CARD
  BANK_TRANSFER
  MOBILE_PAYMENT
  STORE_CREDIT
  CHECK
  OTHER
}

enum OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  RETURNED
  ON_HOLD
}

model LoyaltySettings {
  id                String   @id @default(cuid())
  pointsPerPurchase Float    @default(1)
  minimumPurchase   Float    @default(0)
  pointsValue       Float    @default(0.01)
  expiryDays        Int?
  tierThresholds    Json? // JSON array of tier thresholds
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model CustomerTag {
  id        String     @id @default(cuid())
  name      String
  color     String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  customers Customer[]
}

model CustomerSegment {
  id          String     @id @default(cuid())
  name        String
  description String?
  criteria    Json? // JSON representation of segment criteria
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  customers   Customer[]
}

model LoyaltyTransaction {
  id          String   @id @default(cuid())
  customerId  String
  orderId     String?
  points      Int
  type        String // EARN, REDEEM, EXPIRE, ADJUST
  description String?
  createdAt   DateTime @default(now())
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  order       Order?   @relation(fields: [orderId], references: [id])
}

model CreditTransaction {
  id          String   @id @default(cuid())
  customerId  String
  orderId     String?
  amount      Float
  type        String // ADD, USE, EXPIRE, ADJUST
  description String?
  createdAt   DateTime @default(now())
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  order       Order?   @relation(fields: [orderId], references: [id])
}



