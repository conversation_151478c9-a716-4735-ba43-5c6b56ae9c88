import { z } from "zod"

export const supplierSchema = z.object({
  name: z.string().min(1, "Supplier name is required").max(100, "Supplier name cannot exceed 100 characters"),
  contactName: z.string().optional(),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  notes: z.string().optional(),
  website: z.string().url("Invalid website URL").optional().or(z.literal("")),
})

export type SupplierFormValues = z.infer<typeof supplierSchema>

