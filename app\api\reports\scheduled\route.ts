import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

// Schema for scheduled report creation/update
const scheduledReportSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Schedule name is required"),
  reportId: z.string().min(1, "Report ID is required"),
  frequency: z.enum(["daily", "weekly", "monthly", "quarterly"]),
  dayOfWeek: z.number().min(0).max(6).optional(),
  dayOfMonth: z.number().min(1).max(31).optional(),
  hour: z.number().min(0).max(23),
  minute: z.number().min(0).max(59),
  format: z.enum(["csv", "excel", "pdf"]),
  recipients: z.array(z.string().email()).min(1, "At least one recipient is required"),
  subject: z.string().min(1, "Email subject is required"),
  message: z.string().optional(),
  active: z.boolean().default(true),
})

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const validatedData = scheduledReportSchema.parse(body)

    // Verify the report exists and user has access
    const report = await prisma.customReport.findUnique({
      where: { id: validatedData.reportId },
    })

    if (!report) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 })
    }

    if (report.userId !== session.user.id && !report.isPublic) {
      return NextResponse.json({ error: "Access denied to this report" }, { status: 403 })
    }

    // Create or update scheduled report
    const scheduledReport = await prisma.scheduledReport.upsert({
      where: {
        id: validatedData.id || "",
      },
      update: {
        name: validatedData.name,
        frequency: validatedData.frequency,
        dayOfWeek: validatedData.dayOfWeek,
        dayOfMonth: validatedData.dayOfMonth,
        hour: validatedData.hour,
        minute: validatedData.minute,
        format: validatedData.format,
        recipients: validatedData.recipients,
        subject: validatedData.subject,
        message: validatedData.message || "",
        active: validatedData.active,
        updatedAt: new Date(),
      },
      create: {
        name: validatedData.name,
        reportId: validatedData.reportId,
        frequency: validatedData.frequency,
        dayOfWeek: validatedData.dayOfWeek,
        dayOfMonth: validatedData.dayOfMonth,
        hour: validatedData.hour,
        minute: validatedData.minute,
        format: validatedData.format,
        recipients: validatedData.recipients,
        subject: validatedData.subject,
        message: validatedData.message || "",
        active: validatedData.active,
        userId: session.user.id,
      },
    })

    return NextResponse.json(scheduledReport)
  } catch (error) {
    console.error("Error creating/updating scheduled report:", error)
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Fetch scheduled reports
    const scheduledReports = await prisma.scheduledReport.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        report: {
          select: {
            name: true,
            entity: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    })

    return NextResponse.json(scheduledReports)
  } catch (error) {
    console.error("Error fetching scheduled reports:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

