"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { useLanguage } from "@/components/language-provider"
import Link from "next/link"
import { Globe, Database, Shield } from "lucide-react"

export default function SettingsPage() {
  const { toast } = useToast()
  const { t } = useLanguage()
  const [isLoading, setIsLoading] = useState(false)

  // Mock settings data
  const settings = {
    company: {
      name: "StockSync Inc.",
      address: "123 Business St, Suite 100, Business City, BC 12345",
      phone: "555-STOCKSYNC",
      email: "<EMAIL>",
      website: "https://stocksync.com",
      logo: null,
    },
    tax: {
      rate: 8,
      includeInPrice: false,
      taxNumber: "TAX-12345-678",
    },
    receipt: {
      showLogo: true,
      footer: "Thank you for your business!",
      includeItemSku: true,
      includeTaxDetails: true,
    },
    system: {
      currency: "USD",
      dateFormat: "MM/DD/YYYY",
      timeFormat: "12h",
      lowStockThreshold: 5,
      enableNotifications: true,
      autoBackup: true,
    },
  }

  const handleSaveSettings = async (section: string, e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setIsLoading(false)
      toast({
        title: t("settings.saveChanges"),
        description: `${section} ${t("settings.saveChanges")}`,
      })
    } catch (error) {
      toast({
        title: t("errors.somethingWentWrong"),
        description: t("errors.tryAgain"),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">{t("settings.title")}</h3>
        <p className="text-sm text-muted-foreground">{t("settings.description")}</p>
      </div>

      <Tabs defaultValue="company" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="company">{t("settings.company")}</TabsTrigger>
          <TabsTrigger value="tax">{t("settings.tax")}</TabsTrigger>
          <TabsTrigger value="receipt">{t("settings.receipt")}</TabsTrigger>
          <TabsTrigger value="system">{t("settings.system")}</TabsTrigger>
          <TabsTrigger value="language">{t("settings.language")}</TabsTrigger>
          <TabsTrigger value="backup">Backup</TabsTrigger>
        </TabsList>

        {/* Company Tab Content */}
        <TabsContent value="company">
          <Card>
            <form onSubmit={(e) => handleSaveSettings(t("settings.company"), e)}>
              <CardHeader>
                <CardTitle>{t("settings.company")}</CardTitle>
                <CardDescription>{t("settings.companyInfo.description")}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="company-name">{t("settings.companyInfo.name")}</Label>
                  <Input id="company-name" defaultValue={settings.company.name} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company-address">{t("settings.companyInfo.address")}</Label>
                  <Textarea id="company-address" defaultValue={settings.company.address} />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="company-phone">{t("settings.companyInfo.phone")}</Label>
                    <Input id="company-phone" defaultValue={settings.company.phone} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="company-email">{t("settings.companyInfo.email")}</Label>
                    <Input id="company-email" type="email" defaultValue={settings.company.email} />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company-website">{t("settings.companyInfo.website")}</Label>
                  <Input id="company-website" defaultValue={settings.company.website} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company-logo">{t("settings.companyInfo.logo")}</Label>
                  <Input id="company-logo" type="file" />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? t("settings.saving") : t("settings.saveChanges")}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Tax Tab Content */}
        <TabsContent value="tax">
          <Card>
            <form onSubmit={(e) => handleSaveSettings(t("settings.tax"), e)}>
              <CardHeader>
                <CardTitle>{t("settings.tax")}</CardTitle>
                <CardDescription>{t("settings.taxSettings.description")}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="tax-rate">{t("settings.taxSettings.rate")}</Label>
                  <Input id="tax-rate" type="number" defaultValue={settings.tax.rate} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tax-number">{t("settings.taxSettings.taxNumber")}</Label>
                  <Input id="tax-number" defaultValue={settings.tax.taxNumber} />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="tax-include" defaultChecked={settings.tax.includeInPrice} />
                  <Label htmlFor="tax-include">{t("settings.taxSettings.includeInPrice")}</Label>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? t("settings.saving") : t("settings.saveChanges")}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Receipt Tab Content */}
        <TabsContent value="receipt">
          <Card>
            <form onSubmit={(e) => handleSaveSettings(t("settings.receipt"), e)}>
              <CardHeader>
                <CardTitle>{t("settings.receipt")}</CardTitle>
                <CardDescription>{t("settings.receiptSettings.description")}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch id="receipt-logo" defaultChecked={settings.receipt.showLogo} />
                  <Label htmlFor="receipt-logo">{t("settings.receiptSettings.showLogo")}</Label>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="receipt-footer">{t("settings.receiptSettings.footer")}</Label>
                  <Textarea id="receipt-footer" defaultValue={settings.receipt.footer} />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="receipt-sku" defaultChecked={settings.receipt.includeItemSku} />
                  <Label htmlFor="receipt-sku">{t("settings.receiptSettings.includeItemSku")}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="receipt-tax" defaultChecked={settings.receipt.includeTaxDetails} />
                  <Label htmlFor="receipt-tax">{t("settings.receiptSettings.includeTaxDetails")}</Label>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? t("settings.saving") : t("settings.saveChanges")}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* System Tab Content */}
        <TabsContent value="system">
          <Card>
            <form onSubmit={(e) => handleSaveSettings(t("settings.system"), e)}>
              <CardHeader>
                <CardTitle>{t("settings.system")}</CardTitle>
                <CardDescription>{t("settings.systemSettings.description")}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="system-currency">{t("settings.systemSettings.currency")}</Label>
                  <Input id="system-currency" defaultValue={settings.system.currency} />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="system-date">{t("settings.systemSettings.dateFormat")}</Label>
                    <Input id="system-date" defaultValue={settings.system.dateFormat} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="system-time">{t("settings.systemSettings.timeFormat")}</Label>
                    <Input id="system-time" defaultValue={settings.system.timeFormat} />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="system-threshold">{t("settings.systemSettings.lowStockThreshold")}</Label>
                  <Input id="system-threshold" type="number" defaultValue={settings.system.lowStockThreshold} />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="system-notifications" defaultChecked={settings.system.enableNotifications} />
                  <Label htmlFor="system-notifications">{t("settings.systemSettings.enableNotifications")}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="system-backup" defaultChecked={settings.system.autoBackup} />
                  <Label htmlFor="system-backup">{t("settings.systemSettings.autoBackup")}</Label>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? t("settings.saving") : t("settings.saveChanges")}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Language Tab Content */}
        <TabsContent value="language">
          <Card>
            <CardHeader>
              <CardTitle>{t("settings.languageSettings.title")}</CardTitle>
              <CardDescription>{t("settings.languageSettings.description")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-muted-foreground" />
                <p>{t("settings.languageSettings.description")}</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild>
                <Link href="/dashboard/settings/language">{t("settings.languageSettings.title")}</Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Backup Tab Content */}
        <TabsContent value="backup">
          <Card>
            <CardHeader>
              <CardTitle>Backup & Restore</CardTitle>
              <CardDescription>Manage database backups and restoration</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-muted-foreground" />
                <p>Protect your data with regular backups and restore when needed</p>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-muted-foreground" />
                <p>Configure automatic backups and manage backup retention</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild>
                <Link href="/dashboard/settings/backup">Backup & Restore Settings</Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

