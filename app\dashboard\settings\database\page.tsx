import type { Metada<PERSON> } from "next"
import { ImportExportPanel } from "@/components/data-management/import-export-panel"
import { DatabaseOptimizationPanel } from "@/components/data-management/database-optimization-panel"
import { DataArchivingPanel } from "@/components/data-management/data-archiving-panel"
import { AuditLogsPanel } from "@/components/data-management/audit-logs-panel"

export const metadata: Metadata = {
  title: "Database Management",
  description: "Manage database operations, optimization, and data archiving",
}

export default function DatabaseManagementPage() {
  return (
    <div className="container mx-auto py-6 space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Database Management</h1>
        <p className="text-muted-foreground">Manage database operations, optimization, and data archiving</p>
      </div>

      <div className="grid gap-8">
        <ImportExportPanel />
        <DatabaseOptimizationPanel />
        <DataArchivingPanel />
        <AuditLogsPanel />
      </div>
    </div>
  )
}

