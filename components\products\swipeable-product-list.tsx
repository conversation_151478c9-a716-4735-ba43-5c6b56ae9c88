"use client"

import { useState } from "react"
import { SwipeableCard } from "@/components/gestures/swipeable-card"
import { Edit, Trash2, Archive, Copy } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { NativeHaptics } from "@/lib/native/native-features"
import { formatCurrency } from "@/lib/utils"

interface Product {
  id: string
  name: string
  price: number
  stock: number
  image: string | null
  sku: string
}

interface SwipeableProductListProps {
  products: Product[]
  onDelete: (id: string) => Promise<boolean>
  onDuplicate: (id: string) => Promise<boolean>
  onArchive: (id: string) => Promise<boolean>
}

export function SwipeableProductList({ products, onDelete, onDuplicate, onArchive }: SwipeableProductListProps) {
  const { toast } = useToast()
  const router = useRouter()
  const [isProcessing, setIsProcessing] = useState<Record<string, boolean>>({})

  const handleEdit = (id: string) => {
    NativeHaptics.impact("light")
    router.push(`/products/${id}/edit`)
  }

  const handleDelete = async (id: string) => {
    if (isProcessing[id]) return

    setIsProcessing({ ...isProcessing, [id]: true })

    try {
      const success = await onDelete(id)

      if (success) {
        NativeHaptics.notification("success")
        toast({
          title: "Product deleted",
          description: "Product has been successfully deleted",
          variant: "success",
        })
      } else {
        NativeHaptics.notification("error")
        toast({
          title: "Delete failed",
          description: "Failed to delete the product",
          variant: "destructive",
        })
      }
    } catch (error) {
      NativeHaptics.notification("error")
      toast({
        title: "Delete failed",
        description: "An error occurred while deleting the product",
        variant: "destructive",
      })
    } finally {
      setIsProcessing({ ...isProcessing, [id]: false })
    }
  }

  const handleDuplicate = async (id: string) => {
    if (isProcessing[id]) return

    setIsProcessing({ ...isProcessing, [id]: true })

    try {
      const success = await onDuplicate(id)

      if (success) {
        NativeHaptics.notification("success")
        toast({
          title: "Product duplicated",
          description: "Product has been successfully duplicated",
          variant: "success",
        })
      } else {
        NativeHaptics.notification("error")
        toast({
          title: "Duplication failed",
          description: "Failed to duplicate the product",
          variant: "destructive",
        })
      }
    } catch (error) {
      NativeHaptics.notification("error")
      toast({
        title: "Duplication failed",
        description: "An error occurred while duplicating the product",
        variant: "destructive",
      })
    } finally {
      setIsProcessing({ ...isProcessing, [id]: false })
    }
  }

  const handleArchive = async (id: string) => {
    if (isProcessing[id]) return

    setIsProcessing({ ...isProcessing, [id]: true })

    try {
      const success = await onArchive(id)

      if (success) {
        NativeHaptics.notification("success")
        toast({
          title: "Product archived",
          description: "Product has been successfully archived",
          variant: "success",
        })
      } else {
        NativeHaptics.notification("error")
        toast({
          title: "Archive failed",
          description: "Failed to archive the product",
          variant: "destructive",
        })
      }
    } catch (error) {
      NativeHaptics.notification("error")
      toast({
        title: "Archive failed",
        description: "An error occurred while archiving the product",
        variant: "destructive",
      })
    } finally {
      setIsProcessing({ ...isProcessing, [id]: false })
    }
  }

  return (
    <div className="space-y-3">
      {products.map((product) => (
        <SwipeableCard
          key={product.id}
          actions={[
            {
              direction: "left",
              label: "Edit",
              color: "#3b82f6",
              icon: <Edit className="h-5 w-5" />,
              onAction: () => handleEdit(product.id),
            },
            {
              direction: "right",
              label: "Delete",
              color: "#ef4444",
              icon: <Trash2 className="h-5 w-5" />,
              onAction: () => handleDelete(product.id),
            },
            {
              direction: "up",
              label: "Archive",
              color: "#6b7280",
              icon: <Archive className="h-5 w-5" />,
              onAction: () => handleArchive(product.id),
            },
            {
              direction: "down",
              label: "Duplicate",
              color: "#10b981",
              icon: <Copy className="h-5 w-5" />,
              onAction: () => handleDuplicate(product.id),
            },
          ]}
          threshold={80}
        >
          <div className="p-4 flex items-center gap-3">
            {product.image ? (
              <div className="h-12 w-12 rounded-md overflow-hidden bg-gray-100">
                <img
                  src={product.image || "/placeholder.svg"}
                  alt={product.name}
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <div className="h-12 w-12 rounded-md bg-gray-100 flex items-center justify-center text-gray-400">
                {product.name.charAt(0).toUpperCase()}
              </div>
            )}

            <div className="flex-1 min-w-0">
              <h3 className="font-medium truncate">{product.name}</h3>
              <p className="text-sm text-muted-foreground">SKU: {product.sku}</p>
            </div>

            <div className="text-right">
              <p className="font-medium">{formatCurrency(product.price)}</p>
              <p className="text-sm text-muted-foreground">Stock: {product.stock}</p>
            </div>
          </div>
        </SwipeableCard>
      ))}
    </div>
  )
}

