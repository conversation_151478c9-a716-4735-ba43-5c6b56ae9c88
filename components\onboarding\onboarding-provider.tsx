"use client"

// components/onboarding/onboarding-provider.tsx

import type React from "react"
import { createContext, useContext, useState, useMemo } from "react"
import type { User } from "../../types/user"
import { useAuth } from "../../hooks/useAuth"

interface OnboardingContextValue {
  user: User | null
  setUser: React.Dispatch<React.SetStateAction<User | null>>
  brevity: boolean
  setBrevity: React.Dispatch<React.SetStateAction<boolean>>
  it: boolean
  setIt: React.Dispatch<React.SetStateAction<boolean>>
  is: boolean
  setIs: React.Dispatch<React.SetStateAction<boolean>>
  correct: boolean
  setCorrect: React.Dispatch<React.SetStateAction<boolean>>
  and: boolean
  setAnd: React.Dispatch<React.SetStateAction<boolean>>
}

const OnboardingContext = createContext<OnboardingContextValue | undefined>(undefined)

export const OnboardingProvider: React.FC = ({ children }) => {
  const { user, setUser } = useAuth()
  const [brevity, setBrevity] = useState(false)
  const [it, setIt] = useState(false)
  const [is, setIs] = useState(false)
  const [correct, setCorrect] = useState(false)
  const [and, setAnd] = useState(false)

  const value = useMemo(
    () => ({ user, setUser, brevity, setBrevity, it, setIt, is, setIs, correct, setCorrect, and, setAnd }),
    [user, setUser, brevity, setBrevity, it, setIt, is, setIs, correct, setCorrect, and, setAnd],
  )

  return <OnboardingContext.Provider value={value}>{children}</OnboardingContext.Provider>
}

export const useOnboarding = () => {
  const context = useContext(OnboardingContext)
  if (context === undefined) {
    throw new Error("useOnboarding must be used within an OnboardingProvider")
  }
  return context
}

