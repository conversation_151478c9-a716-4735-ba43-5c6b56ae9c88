import { db } from "@/lib/db"

export async function addPerformanceIndexes() {
  console.log("Adding performance indexes to database...")

  try {
    // Add indexes to products table for common query patterns
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "Product_name_idx" ON "Product" ("name");
      CREATE INDEX IF NOT EXISTS "Product_sku_idx" ON "Product" ("sku");
      CREATE INDEX IF NOT EXISTS "Product_userId_idx" ON "Product" ("userId");
      CREATE INDEX IF NOT EXISTS "Product_categoryId_idx" ON "Product" ("categoryId");
      CREATE INDEX IF NOT EXISTS "Product_quantity_idx" ON "Product" ("quantity");
      CREATE INDEX IF NOT EXISTS "Product_createdAt_idx" ON "Product" ("createdAt");
    `

    // Add indexes to orders table
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "Order_userId_idx" ON "Order" ("userId");
      CREATE INDEX IF NOT EXISTS "Order_customerId_idx" ON "Order" ("customerId");
      CREATE INDEX IF NOT EXISTS "Order_status_idx" ON "Order" ("status");
      CREATE INDEX IF NOT EXISTS "Order_createdAt_idx" ON "Order" ("createdAt");
    `

    // Add indexes to order items table
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "OrderItem_orderId_idx" ON "OrderItem" ("orderId");
      CREATE INDEX IF NOT EXISTS "OrderItem_productId_idx" ON "OrderItem" ("productId");
    `

    // Add composite indexes for common query patterns
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "Product_userId_categoryId_idx" ON "Product" ("userId", "categoryId");
      CREATE INDEX IF NOT EXISTS "Order_userId_status_idx" ON "Order" ("userId", "status");
      CREATE INDEX IF NOT EXISTS "Order_userId_createdAt_idx" ON "Order" ("userId", "createdAt");
    `

    console.log("Database indexes added successfully")
    return { success: true }
  } catch (error) {
    console.error("Error adding database indexes:", error)
    return { error: "Failed to add database indexes" }
  }
}

