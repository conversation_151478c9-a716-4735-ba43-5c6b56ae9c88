import { CustomerLoyalty } from "@/components/loyalty/customer-loyalty"
import { CustomerCredit } from "@/components/customers/customer-credit"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"

interface CustomerDetailPageProps {
  params: {
    id: string
  }
}

export default function CustomerDetailPage({ params }: CustomerDetailPageProps) {
  const customerId = params.id

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-bold tracking-tight">Customer Details</h2>

      <Tabs defaultValue="loyalty" className="space-y-4">
        <TabsList>
          <TabsTrigger value="loyalty">Loyalty Program</TabsTrigger>
          <TabsTrigger value="credit">Store Credit</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="info">Information</TabsTrigger>
        </TabsList>

        <TabsContent value="loyalty">
          <CustomerLoyalty customerId={customerId} />
        </TabsContent>

        <TabsContent value="credit">
          <CustomerCredit customerId={customerId} />
        </TabsContent>

        <TabsContent value="orders">
          <div className="rounded-md border p-8 text-center">Order history will be displayed here</div>
        </TabsContent>

        <TabsContent value="info">
          <div className="rounded-md border p-8 text-center">Customer information will be displayed here</div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

