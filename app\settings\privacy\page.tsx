import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { redirect } from "next/navigation"
import { ConsentManager } from "@/lib/privacy/consent-manager"
import { PrivacySettingsService } from "@/lib/privacy/privacy-settings"
import { DataSubjectRequestService } from "@/lib/privacy/data-subject-request"
import { PrivacySettingsForm } from "@/components/data-protection/privacy-settings-form"
import { ConsentManager as ConsentManagerUI } from "@/components/data-protection/consent-manager"
import { DataExport } from "@/components/data-protection/data-export"
import { DataDeletionRequest } from "@/components/data-protection/data-deletion-request"

export default async function PrivacySettingsPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/login")
  }

  // Get user's privacy settings
  const privacySettings = await PrivacySettingsService.getSettings(session.user.id)

  // Get user's consents
  const consents = await ConsentManager.getUserConsents(session.user.id)

  // Get user's data subject requests
  const requests = await DataSubjectRequestService.getUserRequests(session.user.id)

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Privacy Settings</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="space-y-8">
          <PrivacySettingsForm settings={privacySettings} />

          <ConsentManagerUI consents={consents} />
        </div>

        <div className="space-y-8">
          <DataExport userId={session.user.id} />

          <DataDeletionRequest userId={session.user.id} existingRequests={requests} />
        </div>
      </div>
    </div>
  )
}

