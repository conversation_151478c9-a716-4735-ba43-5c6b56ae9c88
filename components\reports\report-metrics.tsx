"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { ArrowDown, ArrowUp, DollarSign, Package, ShoppingCart, Users } from "lucide-react"

interface ReportMetricsProps {
  data: any
  reportType: "sales" | "inventory" | "customers"
  isLoading: boolean
}

export function ReportMetrics({ data, reportType, isLoading }: ReportMetricsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <Skeleton className="h-4 w-24" />
              </CardTitle>
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-[100px]" />
              <Skeleton className="h-4 w-[80px] mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!data?.metrics) {
    return null
  }

  const { metrics } = data

  // Render different metrics based on report type
  if (reportType === "sales") {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.totalRevenue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {metrics.revenueChange >= 0 ? (
                <ArrowUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.revenueChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(metrics.revenueChange).toFixed(1)}%
              </span>
              <span className="ml-1">from previous period</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalOrders}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {metrics.ordersChange >= 0 ? (
                <ArrowUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.ordersChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(metrics.ordersChange).toFixed(1)}%
              </span>
              <span className="ml-1">from previous period</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.averageOrderValue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {metrics.aovChange >= 0 ? (
                <ArrowUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.aovChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(metrics.aovChange).toFixed(1)}%
              </span>
              <span className="ml-1">from previous period</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Items Sold</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.itemsSold}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {metrics.itemsSoldChange >= 0 ? (
                <ArrowUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.itemsSoldChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(metrics.itemsSoldChange).toFixed(1)}%
              </span>
              <span className="ml-1">from previous period</span>
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (reportType === "inventory") {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalProducts}</div>
            <p className="text-xs text-muted-foreground">{metrics.lowStockItems} items low in stock</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inventory Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.inventoryValue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {metrics.inventoryValueChange >= 0 ? (
                <ArrowUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.inventoryValueChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(metrics.inventoryValueChange).toFixed(1)}%
              </span>
              <span className="ml-1">from previous period</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Turnover</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.stockTurnover.toFixed(2)}x</div>
            <p className="text-xs text-muted-foreground">Average turnover rate</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.outOfStockItems}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.outOfStockPercentage.toFixed(1)}% of total products
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (reportType === "customers") {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalCustomers}</div>
            <p className="text-xs text-muted-foreground flex items-center">{metrics.newCustomers} new in this period</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Spend</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.averageSpend.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {metrics.averageSpendChange >= 0 ? (
                <ArrowUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.averageSpendChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(metrics.averageSpendChange).toFixed(1)}%
              </span>
              <span className="ml-1">from previous period</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Repeat Purchase Rate</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.repeatPurchaseRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Customers who purchased more than once</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Retention</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.customerRetention.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {metrics.retentionChange >= 0 ? (
                <ArrowUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              <span className={metrics.retentionChange >= 0 ? "text-green-500" : "text-red-500"}>
                {Math.abs(metrics.retentionChange).toFixed(1)}%
              </span>
              <span className="ml-1">from previous period</span>
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return null
}

