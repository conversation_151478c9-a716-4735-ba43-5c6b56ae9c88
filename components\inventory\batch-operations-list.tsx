"use client"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Download, FileUp, FileDown, AlertCircle, CheckCircle, Clock, RefreshCw } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface BatchOperation {
  id: string
  type: string
  status: string
  fileName: string | null
  totalItems: number
  successItems: number
  errorItems: number
  createdAt: string
  completedAt: string | null
}

export function BatchOperationsList() {
  const router = useRouter()
  const [operations, setOperations] = useState<BatchOperation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchBatchOperations = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch("/api/batch")

      if (!response.ok) {
        throw new Error("Failed to fetch batch operations")
      }

      const data = await response.json()
      setOperations(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchBatchOperations()
  }, [])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Pending
          </Badge>
        )
      case "processing":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Processing
          </Badge>
        )
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Completed
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "import":
        return <FileUp className="h-4 w-4" />
      case "export":
        return <FileDown className="h-4 w-4" />
      case "update":
        return <RefreshCw className="h-4 w-4" />
      default:
        return null
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "processing":
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Batch Operations</CardTitle>
          <CardDescription>View and manage batch imports and exports</CardDescription>
        </div>
        <Button variant="outline" size="sm" onClick={fetchBatchOperations} disabled={isLoading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-md">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-40" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-8 w-24" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="p-4 border border-red-200 rounded-md bg-red-50 text-red-700">
            <AlertCircle className="h-4 w-4 inline mr-2" />
            {error}
          </div>
        ) : operations.length === 0 ? (
          <div className="text-center p-6 text-muted-foreground">
            <p>No batch operations found</p>
            <p className="text-sm mt-1">Import or export products to see your history here</p>
          </div>
        ) : (
          <div className="space-y-4">
            {operations.map((operation) => (
              <div
                key={operation.id}
                className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-md hover:bg-muted/50 cursor-pointer transition-colors"
                onClick={() => router.push(`/dashboard/products/batch/${operation.id}`)}
              >
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(operation.type)}
                    <span className="font-medium capitalize">{operation.type}</span>
                    {getStatusBadge(operation.status)}
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-sm text-muted-foreground">
                    <span>{operation.fileName || "Unnamed operation"}</span>
                    <span className="hidden sm:inline">•</span>
                    <span>
                      {operation.status === "completed"
                        ? `${operation.successItems} of ${operation.totalItems} items processed`
                        : operation.status === "processing"
                          ? "Processing..."
                          : operation.status === "pending"
                            ? "Pending..."
                            : `${operation.errorItems} errors`}
                    </span>
                    <span className="hidden sm:inline">•</span>
                    <span>
                      {operation.completedAt
                        ? `Completed ${formatDistanceToNow(new Date(operation.completedAt))} ago`
                        : `Started ${formatDistanceToNow(new Date(operation.createdAt))} ago`}
                    </span>
                  </div>
                </div>

                <div className="flex items-center gap-2 mt-3 sm:mt-0">
                  {operation.type === "export" && operation.status === "completed" && (
                    <Button variant="outline" size="sm" className="gap-1">
                      <Download className="h-4 w-4" />
                      Download
                    </Button>
                  )}
                  <div className="ml-2">{getStatusIcon(operation.status)}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

