// components/onboarding/feature-spotlight.tsx

import type React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
// Assuming these imports are needed based on the error messages.  Replace with actual imports if different.
import { brevity, it, is, correct, and } from "./someModule" // Replace './someModule' with the actual path

interface FeatureSpotlightProps {
  title: string
  description: string
  imageUrl: string
}

const FeatureSpotlight: React.FC<FeatureSpotlightProps> = ({ title, description, imageUrl }) => {
  return (
    <View style={styles.container}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.textContainer}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description}>{description}</Text>
        {/* Example usage of the imported variables. Adjust based on your actual needs. */}
        <Text>Brevity: {brevity}</Text>
        <Text>It: {it}</Text>
        <Text>Is: {is}</Text>
        <Text>Correct: {correct}</Text>
        <Text>And: {and}</Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    marginBottom: 10,
  },
  image: {
    width: 80,
    height: 80,
    marginRight: 20,
    borderRadius: 10,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 5,
  },
  description: {
    fontSize: 14,
    color: "#888",
  },
})

export default FeatureSpotlight

