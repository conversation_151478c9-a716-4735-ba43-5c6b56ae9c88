"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

interface PredictiveAnalyticsProps {
  data: any
}

export function PredictiveAnalytics({ data }: PredictiveAnalyticsProps) {
  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No predictive analytics data available</p>
      </div>
    )
  }

  const { salesForecast, inventoryPredictions, demandForecast, seasonalTrends, pricingRecommendations } = data

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(value)
  }

  return (
    <div className="space-y-6">
      {/* Sales Forecast */}
      <Card>
        <CardHeader>
          <CardTitle>Sales Forecast</CardTitle>
          <CardDescription>Predicted sales for the next 6 months</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={salesForecast} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <defs>
                  <linearGradient id="colorActual" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                  </linearGradient>
                  <linearGradient id="colorPredicted" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#82ca9d" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => formatCurrency(value).replace(".00", "")} />
                <Tooltip formatter={(value) => [formatCurrency(value as number), "Sales"]} />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="actual"
                  stroke="#8884d8"
                  fillOpacity={1}
                  fill="url(#colorActual)"
                  name="Actual Sales"
                />
                <Area
                  type="monotone"
                  dataKey="predicted"
                  stroke="#82ca9d"
                  fillOpacity={1}
                  fill="url(#colorPredicted)"
                  name="Predicted Sales"
                  strokeDasharray="5 5"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-muted rounded-md">
              <p className="text-sm font-medium">Prediction Method</p>
              <p className="text-sm text-muted-foreground">Time Series Analysis with Seasonal Adjustment</p>
            </div>
            <div className="p-4 bg-muted rounded-md">
              <p className="text-sm font-medium">Confidence Level</p>
              <p className="text-sm text-muted-foreground">{salesForecast[0].confidenceLevel}%</p>
            </div>
            <div className="p-4 bg-muted rounded-md">
              <p className="text-sm font-medium">Growth Trend</p>
              <p className="text-sm text-muted-foreground">
                {salesForecast[0].growthTrend > 0 ? "+" : ""}
                {salesForecast[0].growthTrend}% per month
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Predictions */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Predictions</CardTitle>
          <CardDescription>Predicted stockouts and recommended reorder quantities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Current Stock</TableHead>
                  <TableHead>Predicted Demand</TableHead>
                  <TableHead>Days Until Stockout</TableHead>
                  <TableHead>Recommended Order</TableHead>
                  <TableHead>Risk Level</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {inventoryPredictions.slice(0, 5).map((item: any) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>{item.currentStock}</TableCell>
                    <TableCell>{item.predictedDemand} units/month</TableCell>
                    <TableCell>{item.daysUntilStockout}</TableCell>
                    <TableCell>{item.recommendedOrder}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          item.riskLevel === "High"
                            ? "destructive"
                            : item.riskLevel === "Medium"
                              ? "default"
                              : "secondary"
                        }
                      >
                        {item.riskLevel}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Seasonal Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Seasonal Trends</CardTitle>
          <CardDescription>Identified seasonal patterns in sales data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={seasonalTrends} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" tickFormatter={(value) => `${value}%`} />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Legend />
                <Line yAxisId="left" type="monotone" dataKey="seasonalIndex" stroke="#8884d8" name="Seasonal Index" />
                <Line yAxisId="right" type="monotone" dataKey="averageSales" stroke="#82ca9d" name="Average Sales" />
              </LineChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 text-sm text-muted-foreground">
            <p>
              Seasonal index values above 100% indicate months with above-average sales, while values below 100%
              indicate months with below-average sales.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Recommendations</CardTitle>
          <CardDescription>AI-generated pricing suggestions based on demand elasticity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Current Price</TableHead>
                  <TableHead>Recommended Price</TableHead>
                  <TableHead>Price Change</TableHead>
                  <TableHead>Predicted Impact</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pricingRecommendations.slice(0, 5).map((item: any) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>{formatCurrency(item.currentPrice)}</TableCell>
                    <TableCell>{formatCurrency(item.recommendedPrice)}</TableCell>
                    <TableCell>
                      <Badge
                        variant={item.priceChange > 0 ? "default" : item.priceChange < 0 ? "destructive" : "secondary"}
                      >
                        {item.priceChange > 0 ? "+" : ""}
                        {item.priceChange}%
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {item.predictedImpact.revenue > 0 ? "+" : ""}
                      {item.predictedImpact.revenue}% Revenue, {item.predictedImpact.volume > 0 ? "+" : ""}
                      {item.predictedImpact.volume}% Volume
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className="mt-4 text-sm text-muted-foreground">
            <p>
              Recommendations are based on price elasticity analysis and competitive pricing data. Consider testing
              these changes incrementally.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

