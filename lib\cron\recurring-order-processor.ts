import prisma from "@/lib/prisma"
import { generateOrderNumber } from "@/lib/utils"

export async function processRecurringOrders() {
  try {
    console.log("Starting recurring order processing...")

    // Find all active recurring orders that are due for processing
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    \
    0,0,0)

    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const dueRecurringOrders = await prisma.recurringOrder.findMany({
      where: {
        status: "ACTIVE",
        nextOrderDate: {
          gte: today,
          lt: tomorrow,
        },
      },
      include: {
        customer: true,
        items: {
          include: {
            product: true,
            variant: true,
          },
        },
      },
    })

    console.log(`Found ${dueRecurringOrders.length} recurring orders to process`)

    // Process each recurring order
    for (const recurringOrder of dueRecurringOrders) {
      try {
        await prisma.$transaction(async (tx) => {
          // Generate order number
          const orderNumber = await generateOrderNumber()

          // Calculate totals
          const subtotal = recurringOrder.items.reduce((sum, item) => sum + item.total, 0)
          const tax = 0 // Assuming tax is calculated elsewhere or included in item prices
          const total = subtotal + tax

          // Create order
          const order = await tx.order.create({
            data: {
              orderNumber,
              status: "PROCESSING",
              paymentStatus: "UNPAID",
              paymentMethod: recurringOrder.paymentMethod,
              subtotal,
              tax,
              shipping: 0, // Assuming shipping is calculated elsewhere
              discount: 0, // Assuming no discounts
              total,
              shippingAddress: recurringOrder.shippingAddress,
              billingAddress: recurringOrder.billingAddress,
              notes: `Generated from recurring order #${recurringOrder.id}`,
              customerId: recurringOrder.customerId,
              userId: "system", // Using a system user ID for automated processes
            },
          })

          // Create order items
          for (const item of recurringOrder.items) {
            await tx.orderItem.create({
              data: {
                orderId: order.id,
                productId: item.productId,
                variantId: item.variantId,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                subtotal: item.total,
                tax: 0, // Assuming tax is included in the item total
                total: item.total,
              },
            })

            // Update inventory
            if (item.variantId) {
              await tx.productVariant.update({
                where: { id: item.variantId },
                data: {
                  inventoryLevel: {
                    decrement: item.quantity,
                  },
                },
              })
            } else {
              await tx.product.update({
                where: { id: item.productId },
                data: {
                  inventoryLevel: {
                    decrement: item.quantity,
                  },
                },
              })
            }

            // Add inventory history record
            await tx.inventoryHistory.create({
              data: {
                productId: item.productId,
                quantity: -item.quantity,
                type: "SALE",
                reference: order.id,
                notes: `Order #${orderNumber} from recurring order`,
              },
            })
          }

          // Create order status history
          await tx.orderStatusHistory.create({
            data: {
              orderId: order.id,
              status: "PROCESSING",
              notes: `Order created from recurring order #${recurringOrder.id}`,
            },
          })

          // Add to recurring order history
          await tx.recurringOrderHistory.create({
            data: {
              recurringOrderId: recurringOrder.id,
              orderId: order.id,
              scheduledDate: recurringOrder.nextOrderDate,
              processedDate: new Date(),
              status: "COMPLETED",
              notes: `Order #${orderNumber} created`,
            },
          })

          // Calculate next order date based on frequency
          let nextOrderDate = new Date(recurringOrder.nextOrderDate)

          switch (recurringOrder.frequency) {
            case "DAILY":
              nextOrderDate.setDate(nextOrderDate.getDate() + 1)
              break
            case "WEEKLY":
              nextOrderDate.setDate(nextOrderDate.getDate() + 7)
              break
            case "BIWEEKLY":
              nextOrderDate.setDate(nextOrderDate.getDate() + 14)
              break
            case "MONTHLY":
              nextOrderDate.setMonth(nextOrderDate.getMonth() + 1)
              break
            case "QUARTERLY":
              nextOrderDate.setMonth(nextOrderDate.getMonth() + 3)
              break
            case "BIANNUALLY":
              nextOrderDate.setMonth(nextOrderDate.getMonth() + 6)
              break
            case "ANNUALLY":
              nextOrderDate.setFullYear(nextOrderDate.getFullYear() + 1)
              break
          }

          // Check if next order date is after end date
          let newStatus = recurringOrder.status
          if (recurringOrder.endDate && nextOrderDate > recurringOrder.endDate) {
            nextOrderDate = recurringOrder.endDate
            newStatus = "COMPLETED"
          }

          // Update recurring order with new next order date
          await tx.recurringOrder.update({
            where: { id: recurringOrder.id },
            data: {
              nextOrderDate,
              status: newStatus,
            },
          })

          // Create notification for the customer
          await tx.notification.create({
            data: {
              title: "Recurring Order Processed",
              message: `Your recurring order has been processed and Order #${orderNumber} has been created.`,
              type: "INFO",
              userId: recurringOrder.customerId, // Sending to the customer
            },
          })

          console.log(`Processed recurring order ${recurringOrder.id}, created order ${orderNumber}`)
        })
      } catch (error) {
        console.error(`Error processing recurring order ${recurringOrder.id}:`, error)
      }
    }

    console.log("Recurring order processing completed")
    return { processed: dueRecurringOrders.length }
  } catch (error) {
    console.error("Error in recurring order processor:", error)
    throw error
  }
}

// Function to check for expired layaways
export async function checkExpiredLayaways() {
  try {
    console.log("Checking for expired layaways...")

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Find all active layaways that have expired
    const expiredLayaways = await prisma.layaway.findMany({
      where: {
        status: "ACTIVE",
        expiryDate: {
          lt: today,
        },
      },
      include: {
        items: true,
      },
    })

    console.log(`Found ${expiredLayaways.length} expired layaways`)

    // Process each expired layaway
    for (const layaway of expiredLayaways) {
      try {
        await prisma.$transaction(async (tx) => {
          // Update layaway status to EXPIRED
          await tx.layaway.update({
            where: { id: layaway.id },
            data: {
              status: "EXPIRED",
            },
          })

          // Return inventory for each item
          for (const item of layaway.items) {
            if (item.variantId) {
              await tx.productVariant.update({
                where: { id: item.variantId },
                data: {
                  inventoryLevel: {
                    increment: item.quantity,
                  },
                },
              })
            } else {
              await tx.product.update({
                where: { id: item.productId },
                data: {
                  inventoryLevel: {
                    increment: item.quantity,
                  },
                },
              })
            }

            // Add inventory history record
            await tx.inventoryHistory.create({
              data: {
                productId: item.productId,
                quantity: item.quantity,
                type: "ADJUSTMENT",
                reference: layaway.id,
                notes: `Layaway #${layaway.layawayNumber} expired`,
              },
            })
          }

          // Create notification
          await tx.notification.create({
            data: {
              title: "Layaway Expired",
              message: `Layaway #${layaway.layawayNumber} has expired and inventory has been returned to stock.`,
              type: "WARNING",
              userId: layaway.customerId,
            },
          })

          console.log(`Processed expired layaway ${layaway.id}`)
        })
      } catch (error) {
        console.error(`Error processing expired layaway ${layaway.id}:`, error)
      }
    }

    console.log("Expired layaway processing completed")
    return { processed: expiredLayaways.length }
  } catch (error) {
    console.error("Error in expired layaway processor:", error)
    throw error
  }
}

// This function can be called by a cron job scheduler
export async function runDailyProcessing() {
  try {
    await processRecurringOrders()
    await checkExpiredLayaways()
    return { success: true }
  } catch (error) {
    console.error("Error in daily processing:", error)
    return { success: false, error }
  }
}

