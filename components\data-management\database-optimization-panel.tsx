"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>rigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Loader2, Database, RefreshCw, AlertCircle, CheckCircle } from "lucide-react"
import { toast } from "@/hooks/use-toast"

export function DatabaseOptimizationPanel() {
  const [activeTab, setActiveTab] = useState("analyze")
  const [analyzing, setAnalyzing] = useState(false)
  const [optimizing, setOptimizing] = useState(false)
  const [analyzeResult, setAnalyzeResult] = useState<any>(null)
  const [optimizeResult, setOptimizeResult] = useState<any>(null)

  const handleAnalyze = async () => {
    setAnalyzing(true)
    setAnalyzeResult(null)

    try {
      const response = await fetch("/api/db/optimization/analyze")
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to analyze database")
      }

      setAnalyzeResult(result)

      toast({
        title: "Analysis complete",
        description: "Database analysis completed successfully",
        variant: "default",
      })
    } catch (error) {
      console.error("Analysis error:", error)

      toast({
        title: "Analysis failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setAnalyzing(false)
    }
  }

  const handleOptimize = async () => {
    setOptimizing(true)
    setOptimizeResult(null)

    try {
      const response = await fetch("/api/db/optimization/optimize", {
        method: "POST",
      })
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to optimize database")
      }

      setOptimizeResult(result)

      toast({
        title: "Optimization complete",
        description: "Database optimization completed successfully",
        variant: "default",
      })
    } catch (error) {
      console.error("Optimization error:", error)

      toast({
        title: "Optimization failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setOptimizing(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Database Optimization</CardTitle>
        <CardDescription>Analyze and optimize database performance</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="analyze">Analyze</TabsTrigger>
            <TabsTrigger value="optimize">Optimize</TabsTrigger>
          </TabsList>

          <TabsContent value="analyze" className="space-y-4 pt-4">
            <div className="space-y-4">
              <Button onClick={handleAnalyze} disabled={analyzing}>
                {analyzing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Database className="mr-2 h-4 w-4" />
                    Analyze Database
                  </>
                )}
              </Button>

              {analyzeResult && (
                <div className="space-y-4">
                  <Alert variant="default">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    <AlertTitle>Analysis Complete</AlertTitle>
                    <AlertDescription>Database analysis completed successfully</AlertDescription>
                  </Alert>

                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Table Statistics</h3>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Table</TableHead>
                            <TableHead>Rows</TableHead>
                            <TableHead>Total Size</TableHead>
                            <TableHead>Table Size</TableHead>
                            <TableHead>Index Size</TableHead>
                            <TableHead>Sequential Scans</TableHead>
                            <TableHead>Index Scans</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {analyzeResult.tableStats.map((stat: any, index: number) => (
                            <TableRow key={index}>
                              <TableCell>{stat.table_name}</TableCell>
                              <TableCell>{stat.row_count}</TableCell>
                              <TableCell>{stat.total_size}</TableCell>
                              <TableCell>{stat.table_size}</TableCell>
                              <TableCell>{stat.index_size}</TableCell>
                              <TableCell>{stat.sequential_scans}</TableCell>
                              <TableCell>{stat.index_scans}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium">Slow Queries</h3>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Query</TableHead>
                            <TableHead>Calls</TableHead>
                            <TableHead>Total Time (ms)</TableHead>
                            <TableHead>Mean Time (ms)</TableHead>
                            <TableHead>Max Time (ms)</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {analyzeResult.slowQueries.map((query: any, index: number) => (
                            <TableRow key={index}>
                              <TableCell className="max-w-md truncate">{query.query}</TableCell>
                              <TableCell>{query.calls}</TableCell>
                              <TableCell>{query.total_time.toFixed(2)}</TableCell>
                              <TableCell>{query.mean_time.toFixed(2)}</TableCell>
                              <TableCell>{query.max_time.toFixed(2)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium">Index Usage</h3>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Table</TableHead>
                            <TableHead>Index</TableHead>
                            <TableHead>Index Scans</TableHead>
                            <TableHead>Index Size</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {analyzeResult.indexUsage.map((index: any, i: number) => (
                            <TableRow key={i}>
                              <TableCell>{index.table_name}</TableCell>
                              <TableCell>{index.index_name}</TableCell>
                              <TableCell>{index.index_scans}</TableCell>
                              <TableCell>{index.index_size}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="optimize" className="space-y-4 pt-4">
            <div className="space-y-4">
              <Alert variant="warning">
                <AlertCircle className="h-4 w-4 mr-2" />
                <AlertTitle>Warning</AlertTitle>
                <AlertDescription>
                  Database optimization can be resource-intensive and may temporarily affect system performance. It is
                  recommended to run this during off-peak hours.
                </AlertDescription>
              </Alert>

              <Button onClick={handleOptimize} disabled={optimizing}>
                {optimizing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Optimizing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Optimize Database
                  </>
                )}
              </Button>

              {optimizeResult && (
                <Alert variant="default">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <AlertTitle>Optimization Complete</AlertTitle>
                  <AlertDescription>{optimizeResult.message}</AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

