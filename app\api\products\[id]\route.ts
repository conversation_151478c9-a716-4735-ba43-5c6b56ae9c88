import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { ApiError } from "@/lib/api-error"
import { productWithVariantsSchema } from "@/lib/validations/variant-schema"

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        category: true,
        supplier: true,
        variants: {
          include: {
            options: {
              include: {
                optionGroup: true,
              },
            },
          },
        },
        optionGroups: true,
      },
    })

    if (!product) {
      throw ApiError.notFound("Product not found")
    }

    return NextResponse.json(product)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Product fetch error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const body = await req.json()

    // Validate request body
    const validatedData = productWithVariantsSchema.parse(body)

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        variants: true,
        optionGroups: {
          include: {
            options: true,
          },
        },
      },
    })

    if (!existingProduct) {
      throw ApiError.notFound("Product not found")
    }

    // Check if SKU already exists (if changed)
    if (validatedData.sku !== existingProduct.sku) {
      const existingSku = await prisma.product.findUnique({
        where: { sku: validatedData.sku },
      })

      if (existingSku) {
        throw ApiError.conflict("A product with this SKU already exists")
      }
    }

    // Update product
    const product = await prisma.product.update({
      where: { id: params.id },
      data: {
        name: validatedData.name,
        sku: validatedData.sku,
        barcode: validatedData.barcode || null,
        description: validatedData.description || null,
        price: validatedData.price,
        cost: validatedData.cost || null,
        taxRate: validatedData.taxRate || 0,
        stockQuantity: validatedData.stockQuantity,
        reorderPoint: validatedData.reorderPoint || 10,
        categoryId: validatedData.categoryId,
        supplierId: validatedData.supplierId || null,
      },
    })

    // Handle variants
    if (validatedData.hasVariants && validatedData.optionGroups && validatedData.variants) {
      // Delete existing option groups and variants
      await prisma.optionGroup.deleteMany({
        where: { productId: product.id },
      })

      // Create option groups
      const optionGroupMap = new Map()

      for (const group of validatedData.optionGroups) {
        const createdGroup = await prisma.optionGroup.create({
          data: {
            name: group.name,
            displayName: group.displayName,
            productId: product.id,
          },
        })

        optionGroupMap.set(group.name, createdGroup.id)
      }

      // Create variants
      for (const variant of validatedData.variants) {
        const createdVariant = await prisma.productVariant.create({
          data: {
            sku: `${product.sku}-${variant.sku}`,
            barcode: variant.barcode || null,
            price: variant.price,
            cost: variant.cost,
            stockQuantity: variant.stockQuantity,
            isDefault: variant.isDefault,
            productId: product.id,
          },
        })

        // Create variant options
        for (const [groupName, optionValue] of Object.entries(variant.options)) {
          const optionGroupId = optionGroupMap.get(groupName)

          if (optionGroupId) {
            await prisma.variantOption.create({
              data: {
                optionGroupId,
                optionValue,
                productVariantId: createdVariant.id,
              },
            })
          }
        }
      }
    } else {
      // If product no longer has variants, delete existing ones
      await prisma.optionGroup.deleteMany({
        where: { productId: product.id },
      })
    }

    return NextResponse.json({
      success: true,
      product: {
        id: product.id,
        name: product.name,
        sku: product.sku,
      },
    })
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Product update error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
    })

    if (!existingProduct) {
      throw ApiError.notFound("Product not found")
    }

    // Delete product (will cascade delete variants and option groups)
    await prisma.product.delete({
      where: { id: params.id },
    })

    return NextResponse.json({
      success: true,
    })
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Product deletion error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

