"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import {
  ArrowLeft,
  Download,
  FileUp,
  FileDown,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw,
  AlertTriangle,
} from "lucide-react"
import { format } from "date-fns"

interface BatchOperation {
  id: string
  type: string
  status: string
  fileName: string | null
  fileUrl: string | null
  totalItems: number
  processedItems: number
  successItems: number
  errorItems: number
  errors: any[] | null
  metadata: any | null
  createdAt: string
  updatedAt: string
  completedAt: string | null
}

export default function BatchOperationDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [operation, setOperation] = useState<BatchOperation | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("overview")

  const fetchBatchOperation = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/batch/${params.id}`)

      if (!response.ok) {
        throw new Error("Failed to fetch batch operation details")
      }

      const data = await response.json()
      setOperation(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchBatchOperation()

    // Poll for updates if the operation is pending or processing
    let interval: NodeJS.Timeout | null = null

    if (operation?.status === "pending" || operation?.status === "processing") {
      interval = setInterval(fetchBatchOperation, 5000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [params.id, operation?.status])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Pending
          </Badge>
        )
      case "processing":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Processing
          </Badge>
        )
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Completed
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "import":
        return <FileUp className="h-5 w-5" />
      case "export":
        return <FileDown className="h-5 w-5" />
      case "update":
        return <RefreshCw className="h-5 w-5" />
      default:
        return null
    }
  }

  const getProgressColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500"
      case "processing":
        return "bg-blue-500"
      case "completed":
        return "bg-green-500"
      case "failed":
        return "bg-red-500"
      default:
        return ""
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10" />
          <div>
            <Skeleton className="h-6 w-40 mb-1" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-4 w-60" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-40 w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !operation) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" onClick={() => router.back()} className="gap-1">
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>

        <Card>
          <CardContent className="pt-6">
            <div className="p-4 border border-red-200 rounded-md bg-red-50 text-red-700">
              <AlertCircle className="h-4 w-4 inline mr-2" />
              {error || "Batch operation not found"}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const progress = operation.totalItems > 0 ? Math.round((operation.processedItems / operation.totalItems) * 100) : 0

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => router.back()} className="gap-1">
          <ArrowLeft className="h-4 w-4" />
          Back to Batch Operations
        </Button>

        <Button variant="outline" size="sm" onClick={fetchBatchOperation} className="gap-1">
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      <div className="flex items-center gap-3">
        <div className="p-3 rounded-full bg-muted">{getTypeIcon(operation.type)}</div>
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            {operation.type === "import" ? "Product Import" : "Product Export"}
            {getStatusBadge(operation.status)}
          </h1>
          <p className="text-muted-foreground">
            {operation.fileName || "Unnamed operation"} •
            {operation.createdAt && ` Started ${format(new Date(operation.createdAt), "PPp")}`}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Operation Details</CardTitle>
          <CardDescription>
            {operation.status === "completed"
              ? `Processed ${operation.successItems} of ${operation.totalItems} items successfully`
              : operation.status === "processing"
                ? "Processing items..."
                : operation.status === "pending"
                  ? "Waiting to start processing..."
                  : "Operation failed"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              {operation.errors && operation.errors.length > 0 && (
                <TabsTrigger value="errors">
                  Errors
                  <Badge variant="destructive" className="ml-2">
                    {operation.errorItems}
                  </Badge>
                </TabsTrigger>
              )}
              <TabsTrigger value="details">Details</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              {(operation.status === "pending" || operation.status === "processing") && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{progress}%</span>
                  </div>
                  <Progress value={progress} className={getProgressColor(operation.status)} />
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 rounded-lg border bg-muted/50">
                  <div className="text-sm text-muted-foreground mb-1">Total Items</div>
                  <div className="text-2xl font-bold">{operation.totalItems}</div>
                </div>

                <div className="p-4 rounded-lg border bg-muted/50">
                  <div className="text-sm text-muted-foreground mb-1">Successful</div>
                  <div className="text-2xl font-bold text-green-600">{operation.successItems}</div>
                </div>

                <div className="p-4 rounded-lg border bg-muted/50">
                  <div className="text-sm text-muted-foreground mb-1">Errors</div>
                  <div className="text-2xl font-bold text-red-600">{operation.errorItems}</div>
                </div>
              </div>

              <div className="rounded-lg border p-4">
                <h3 className="font-medium mb-2">Status</h3>
                <div className="flex items-center gap-2">
                  {operation.status === "pending" && (
                    <>
                      <Clock className="h-4 w-4 text-yellow-500" />
                      <span>Pending - Waiting to start processing</span>
                    </>
                  )}

                  {operation.status === "processing" && (
                    <>
                      <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
                      <span>
                        Processing - {operation.processedItems} of {operation.totalItems} items processed
                      </span>
                    </>
                  )}

                  {operation.status === "completed" && (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>
                        Completed - {operation.successItems} items processed successfully
                        {operation.errorItems > 0 && `, ${operation.errorItems} errors`}
                      </span>
                    </>
                  )}

                  {operation.status === "failed" && (
                    <>
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span>Failed - Operation could not be completed</span>
                    </>
                  )}
                </div>

                {operation.completedAt && (
                  <div className="text-sm text-muted-foreground mt-2">
                    Completed at {format(new Date(operation.completedAt), "PPp")}
                  </div>
                )}
              </div>

              {operation.type === "export" && operation.status === "completed" && operation.fileUrl && (
                <div className="flex justify-center mt-4">
                  <Button className="gap-2">
                    <Download className="h-4 w-4" />
                    Download Export File
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="errors">
              {operation.errors && operation.errors.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-amber-600 bg-amber-50 p-3 rounded-md border border-amber-200">
                    <AlertTriangle className="h-4 w-4" />
                    <span>The following errors occurred during the operation</span>
                  </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="bg-muted p-3 font-medium text-sm grid grid-cols-12">
                      <div className="col-span-2">Row</div>
                      <div className="col-span-4">Field</div>
                      <div className="col-span-6">Error</div>
                    </div>

                    <div className="divide-y">
                      {operation.errors.map((error, index) => (
                        <div key={index} className="p-3 text-sm grid grid-cols-12">
                          <div className="col-span-2">{error.row || "N/A"}</div>
                          <div className="col-span-4">{error.field || "Multiple"}</div>
                          <div className="col-span-6 text-red-600">{error.message || JSON.stringify(error)}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center p-6 text-muted-foreground">
                  <CheckCircle className="h-6 w-6 mx-auto mb-2 text-green-500" />
                  <p>No errors found</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="details">
              <div className="border rounded-md overflow-hidden">
                <div className="divide-y">
                  <div className="grid grid-cols-3 p-3">
                    <div className="font-medium">Operation ID</div>
                    <div className="col-span-2 font-mono text-sm">{operation.id}</div>
                  </div>

                  <div className="grid grid-cols-3 p-3">
                    <div className="font-medium">Type</div>
                    <div className="col-span-2 capitalize">{operation.type}</div>
                  </div>

                  <div className="grid grid-cols-3 p-3">
                    <div className="font-medium">File Name</div>
                    <div className="col-span-2">{operation.fileName || "N/A"}</div>
                  </div>

                  <div className="grid grid-cols-3 p-3">
                    <div className="font-medium">Created At</div>
                    <div className="col-span-2">
                      {operation.createdAt && format(new Date(operation.createdAt), "PPp")}
                    </div>
                  </div>

                  <div className="grid grid-cols-3 p-3">
                    <div className="font-medium">Updated At</div>
                    <div className="col-span-2">
                      {operation.updatedAt && format(new Date(operation.updatedAt), "PPp")}
                    </div>
                  </div>

                  <div className="grid grid-cols-3 p-3">
                    <div className="font-medium">Completed At</div>
                    <div className="col-span-2">
                      {operation.completedAt ? format(new Date(operation.completedAt), "PPp") : "Not completed"}
                    </div>
                  </div>

                  {operation.metadata && (
                    <div className="grid grid-cols-3 p-3">
                      <div className="font-medium">Metadata</div>
                      <div className="col-span-2 font-mono text-xs whitespace-pre-wrap">
                        {JSON.stringify(operation.metadata, null, 2)}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

