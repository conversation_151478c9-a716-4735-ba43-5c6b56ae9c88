"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  serialNumberSchema,
  serialNumberBulkImportSchema,
  type SerialNumberFormValues,
  type SerialNumberBulkImportValues,
} from "@/lib/validations/serial-number-schema"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { fetchApi } from "@/lib/api-client"
import { useToast } from "@/hooks/use-toast"
import { AlertCircle, Plus, Trash2, Edit, FileText, Calendar, Tag } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { format } from "date-fns"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface SerialNumberManagerProps {
  productId: string
  variantId?: string
  productName: string
  variantName?: string
}

interface SerialNumber {
  id: string
  serialNumber: string
  status: string
  notes?: string
  purchaseDate?: string
  soldDate?: string
  productId: string
  variantId?: string
  createdAt: string
  updatedAt: string
  variant?: {
    id: string
    sku: string
  }
}

export function SerialNumberManager({ productId, variantId, productName, variantName }: SerialNumberManagerProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("list")
  const [serialNumbers, setSerialNumbers] = useState<SerialNumber[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<string | null>(null)
  const [editingSerialNumber, setEditingSerialNumber] = useState<SerialNumber | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [serverError, setServerError] = useState<string | null>(null)

  const singleForm = useForm<SerialNumberFormValues>({
    resolver: zodResolver(serialNumberSchema),
    defaultValues: {
      serialNumber: "",
      status: "in_stock",
      notes: "",
      purchaseDate: new Date().toISOString().split("T")[0],
      productId,
      variantId: variantId || null,
    },
  })

  const bulkForm = useForm<SerialNumberBulkImportValues>({
    resolver: zodResolver(serialNumberBulkImportSchema),
    defaultValues: {
      productId,
      variantId: variantId || null,
      serialNumbers: [],
      status: "in_stock",
      purchaseDate: new Date().toISOString().split("T")[0],
    },
  })

  const editForm = useForm<SerialNumberFormValues>({
    resolver: zodResolver(serialNumberSchema),
    defaultValues: {
      serialNumber: "",
      status: "in_stock",
      notes: "",
      productId,
      variantId: variantId || null,
    },
  })

  useEffect(() => {
    loadSerialNumbers()
  }, [productId, variantId, statusFilter])

  useEffect(() => {
    if (editingSerialNumber) {
      editForm.reset({
        serialNumber: editingSerialNumber.serialNumber,
        status: editingSerialNumber.status as any,
        notes: editingSerialNumber.notes || "",
        purchaseDate: editingSerialNumber.purchaseDate
          ? new Date(editingSerialNumber.purchaseDate).toISOString().split("T")[0]
          : undefined,
        productId,
        variantId: editingSerialNumber.variantId || null,
      })
      setIsDialogOpen(true)
    }
  }, [editingSerialNumber, editForm, productId])

  const loadSerialNumbers = async () => {
    setIsLoading(true)
    try {
      let url = `/api/products/${productId}/serial-numbers`
      const params = new URLSearchParams()

      if (variantId) {
        params.append("variantId", variantId)
      }

      if (statusFilter) {
        params.append("status", statusFilter)
      }

      if (params.toString()) {
        url += `?${params.toString()}`
      }

      const data = await fetchApi(url)
      setSerialNumbers(data)
    } catch (error) {
      toast({
        title: "Failed to load serial numbers",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const onSubmitSingle = async (values: SerialNumberFormValues) => {
    setServerError(null)
    try {
      await fetchApi(`/api/products/${productId}/serial-numbers`, {
        method: "POST",
        body: JSON.stringify(values),
      })

      toast({
        title: "Serial number added",
        description: "The serial number has been successfully added",
      })

      singleForm.reset({
        serialNumber: "",
        status: "in_stock",
        notes: "",
        purchaseDate: new Date().toISOString().split("T")[0],
        productId,
        variantId: variantId || null,
      })

      loadSerialNumbers()
      setActiveTab("list")
    } catch (error) {
      setServerError(error instanceof Error ? error.message : "An unexpected error occurred")
      toast({
        title: "Failed to add serial number",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      })
    }
  }

  const onSubmitBulk = async (values: SerialNumberBulkImportValues) => {
    setServerError(null)
    try {
      // Parse the textarea input into an array of serial numbers
      const serialNumbersText = bulkForm.getValues("serialNumbersText") as string
      const serialNumbers = serialNumbersText
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line.length > 0)

      if (serialNumbers.length === 0) {
        setServerError("Please enter at least one serial number")
        return
      }

      const payload = {
        ...values,
        serialNumbers,
      }

      const response = await fetchApi(`/api/products/${productId}/serial-numbers`, {
        method: "POST",
        body: JSON.stringify(payload),
      })

      toast({
        title: "Serial numbers imported",
        description: `Successfully imported ${response.created} serial numbers. ${response.duplicates} duplicates were skipped.`,
      })

      bulkForm.reset({
        productId,
        variantId: variantId || null,
        serialNumbers: [],
        status: "in_stock",
        purchaseDate: new Date().toISOString().split("T")[0],
      })

      loadSerialNumbers()
      setActiveTab("list")
    } catch (error) {
      setServerError(error instanceof Error ? error.message : "An unexpected error occurred")
      toast({
        title: "Failed to import serial numbers",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      })
    }
  }

  const onSubmitEdit = async (values: SerialNumberFormValues) => {
    if (!editingSerialNumber) return

    setServerError(null)
    try {
      await fetchApi(`/api/serial-numbers/${editingSerialNumber.id}`, {
        method: "PUT",
        body: JSON.stringify(values),
      })

      toast({
        title: "Serial number updated",
        description: "The serial number has been successfully updated",
      })

      setIsDialogOpen(false)
      setEditingSerialNumber(null)
      loadSerialNumbers()
    } catch (error) {
      setServerError(error instanceof Error ? error.message : "An unexpected error occurred")
      toast({
        title: "Failed to update serial number",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this serial number?")) {
      return
    }

    try {
      await fetchApi(`/api/serial-numbers/${id}`, {
        method: "DELETE",
      })

      toast({
        title: "Serial number deleted",
        description: "The serial number has been successfully deleted",
      })

      loadSerialNumbers()
    } catch (error) {
      toast({
        title: "Failed to delete serial number",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      })
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "in_stock":
        return "default"
      case "sold":
        return "success"
      case "reserved":
        return "warning"
      case "defective":
        return "destructive"
      case "returned":
        return "secondary"
      default:
        return "outline"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Serial Numbers</span>
          <div className="flex items-center gap-2">
            <Select value={statusFilter || ""} onValueChange={(value) => setStatusFilter(value || null)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_statuses">All Statuses</SelectItem>
                <SelectItem value="in_stock">In Stock</SelectItem>
                <SelectItem value="sold">Sold</SelectItem>
                <SelectItem value="reserved">Reserved</SelectItem>
                <SelectItem value="defective">Defective</SelectItem>
                <SelectItem value="returned">Returned</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardTitle>
        <CardDescription>
          Manage serial numbers for {productName}
          {variantName && ` - ${variantName}`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="list">Serial Numbers</TabsTrigger>
            <TabsTrigger value="add">Add Single</TabsTrigger>
            <TabsTrigger value="bulk">Bulk Import</TabsTrigger>
          </TabsList>

          <TabsContent value="list">
            {isLoading ? (
              <div className="space-y-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center justify-between p-2 border rounded">
                    <Skeleton className="h-6 w-1/3" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                ))}
              </div>
            ) : serialNumbers.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No serial numbers found</p>
                <Button variant="outline" className="mt-4" onClick={() => setActiveTab("add")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Serial Number
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                {serialNumbers.map((sn) => (
                  <div key={sn.id} className="flex items-center justify-between p-3 border rounded hover:bg-muted/50">
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2">
                        <Tag className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{sn.serialNumber}</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                        {sn.purchaseDate && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>Purchased: {format(new Date(sn.purchaseDate), "MMM d, yyyy")}</span>
                          </div>
                        )}
                        {sn.notes && (
                          <div className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            <span>
                              {sn.notes.substring(0, 30)}
                              {sn.notes.length > 30 ? "..." : ""}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusBadgeVariant(sn.status)}>{sn.status.replace("_", " ")}</Badge>
                      <Button variant="ghost" size="icon" onClick={() => setEditingSerialNumber(sn)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDelete(sn.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="add">
            <Form {...singleForm}>
              <form onSubmit={singleForm.handleSubmit(onSubmitSingle)} className="space-y-4">
                {serverError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{serverError}</AlertDescription>
                  </Alert>
                )}

                <FormField
                  control={singleForm.control}
                  name="serialNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Serial Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter serial number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={singleForm.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <FormControl>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="in_stock">In Stock</SelectItem>
                            <SelectItem value="sold">Sold</SelectItem>
                            <SelectItem value="reserved">Reserved</SelectItem>
                            <SelectItem value="defective">Defective</SelectItem>
                            <SelectItem value="returned">Returned</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={singleForm.control}
                  name="purchaseDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purchase Date (Optional)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={singleForm.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter notes about this serial number"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setActiveTab("list")}>
                    Cancel
                  </Button>
                  <Button type="submit">Add Serial Number</Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value="bulk">
            <Form {...bulkForm}>
              <form onSubmit={bulkForm.handleSubmit(onSubmitBulk)} className="space-y-4">
                {serverError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{serverError}</AlertDescription>
                  </Alert>
                )}

                <FormField
                  control={bulkForm.control}
                  name="serialNumbersText"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Serial Numbers (one per line)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter serial numbers, one per line"
                          className="min-h-32 font-mono"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>Enter each serial number on a new line</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={bulkForm.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <FormControl>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="in_stock">In Stock</SelectItem>
                            <SelectItem value="sold">Sold</SelectItem>
                            <SelectItem value="reserved">Reserved</SelectItem>
                            <SelectItem value="defective">Defective</SelectItem>
                            <SelectItem value="returned">Returned</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={bulkForm.control}
                  name="purchaseDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purchase Date (Optional)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setActiveTab("list")}>
                    Cancel
                  </Button>
                  <Button type="submit">Import Serial Numbers</Button>
                </div>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </CardContent>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Serial Number</DialogTitle>
            <DialogDescription>Update the details for this serial number</DialogDescription>
          </DialogHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onSubmitEdit)} className="space-y-4">
              {serverError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{serverError}</AlertDescription>
                </Alert>
              )}

              <FormField
                control={editForm.control}
                name="serialNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Serial Number</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter serial number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="in_stock">In Stock</SelectItem>
                          <SelectItem value="sold">Sold</SelectItem>
                          <SelectItem value="reserved">Reserved</SelectItem>
                          <SelectItem value="defective">Defective</SelectItem>
                          <SelectItem value="returned">Returned</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="purchaseDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Purchase Date (Optional)</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter notes about this serial number"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Update Serial Number</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

