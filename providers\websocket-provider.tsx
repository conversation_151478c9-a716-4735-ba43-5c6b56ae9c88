"use client"

import type React from "react"

import { createContext, useContext, useEffect, useRef, useState, useCallback } from "react"
import { useToast } from "@/components/ui/use-toast"

interface WebSocketContextType {
  socket: WebSocket | null
  isConnected: boolean
  sendMessage: (message: any) => void
  lastMessage: any
}

const WebSocketContext = createContext<WebSocketContextType>({
  socket: null,
  isConnected: false,
  sendMessage: () => {},
  lastMessage: null,
})

export const useWebSocket = () => useContext(WebSocketContext)

interface WebSocketProviderProps {
  children: React.ReactNode
}

export function WebSocketProvider({ children }: WebSocketProviderProps) {
  const [socket, setSocket] = useState<WebSocket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [lastMessage, setLastMessage] = useState<any>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttemptsRef = useRef(0)
  const maxReconnectAttempts = 10
  const { toast } = useToast()

  // Calculate exponential backoff delay
  const getReconnectDelay = useCallback(() => {
    const baseDelay = 1000 // 1 second
    const attempt = Math.min(reconnectAttemptsRef.current, 10) // Cap at 10 for max delay
    return Math.min(baseDelay * Math.pow(1.5, attempt), 30000) // Max 30 seconds
  }, [])

  const connectWebSocket = useCallback(() => {
    if (typeof window === "undefined") return

    try {
      const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || "wss://api.stocksync.example.com"
      const ws = new WebSocket(socketUrl)

      ws.onopen = () => {
        setIsConnected(true)
        setSocket(ws)
        reconnectAttemptsRef.current = 0

        // Send authentication message
        const authMessage = {
          type: "authenticate",
          token: localStorage.getItem("token") || "",
        }
        ws.send(JSON.stringify(authMessage))
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          setLastMessage(message)

          // Handle different message types if needed
          if (message.type === "error") {
            console.error("WebSocket error:", message.error)
          }
        } catch (error) {
          console.error("Error parsing WebSocket message:", error)
        }
      }

      ws.onclose = (event) => {
        setIsConnected(false)
        setSocket(null)

        // Only attempt reconnect if not a clean close
        if (!event.wasClean && reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current += 1
          const delay = getReconnectDelay()

          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current)
          }

          reconnectTimeoutRef.current = setTimeout(() => {
            connectWebSocket()
          }, delay)

          // Show toast for reconnection attempts
          if (reconnectAttemptsRef.current > 3) {
            toast({
              title: "Connection issue",
              description: `Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})...`,
              variant: "default",
            })
          }
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          toast({
            title: "Connection failed",
            description: "Unable to connect to the server. Please refresh the page.",
            variant: "destructive",
          })
        }
      }

      ws.onerror = () => {
        // Error handling is done in onclose
        ws.close()
      }

      return ws
    } catch (error) {
      console.error("WebSocket connection error:", error)
      return null
    }
  }, [getReconnectDelay, toast])

  // Initialize WebSocket connection
  useEffect(() => {
    const ws = connectWebSocket()

    // Cleanup function
    return () => {
      if (ws) {
        ws.close()
      }

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, [connectWebSocket])

  // Handle visibility change to reconnect when tab becomes visible
  useEffect(() => {
    if (typeof window === "undefined") return

    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && !isConnected) {
        connectWebSocket()
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [connectWebSocket, isConnected])

  // Function to send messages
  const sendMessage = useCallback(
    (message: any) => {
      if (socket && isConnected) {
        socket.send(JSON.stringify(message))
      } else {
        console.warn("Cannot send message: WebSocket is not connected")
      }
    },
    [socket, isConnected],
  )

  return (
    <WebSocketContext.Provider
      value={{
        socket,
        isConnected,
        sendMessage,
        lastMessage,
      }}
    >
      {children}
    </WebSocketContext.Provider>
  )
}

