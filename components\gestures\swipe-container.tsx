"use client"

import type React from "react"
import { useRef, useEffect, useState } from "react"
import { GestureSystem, type GestureDirection } from "@/lib/gestures/gesture-system"
import { cn } from "@/lib/utils"

interface SwipeContainerProps {
  children: React.ReactNode
  onSwipe?: (direction: GestureDirection) => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  threshold?: number
  className?: string
  enableHaptics?: boolean
  showIndicators?: boolean
}

export function SwipeContainer({
  children,
  onSwipe,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50,
  className,
  enableHaptics = true,
  showIndicators = false,
}: SwipeContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const gestureRef = useRef<GestureSystem | null>(null)
  const [swipeDirection, setSwipeDirection] = useState<GestureDirection | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragDelta, setDragDelta] = useState({ x: 0, y: 0 })

  useEffect(() => {
    if (!containerRef.current) return

    gestureRef.current = new GestureSystem({
      element: containerRef.current,
      threshold,
      enableHaptics,
      onStart: () => {
        setIsDragging(true)
        setDragDelta({ x: 0, y: 0 })
      },
      onMove: (_, data) => {
        setDragDelta({ x: data.deltaX, y: data.deltaY })
      },
      onSwipe: (direction, _) => {
        setSwipeDirection(direction)

        // Call the appropriate callback
        if (onSwipe) onSwipe(direction)

        switch (direction) {
          case "left":
            if (onSwipeLeft) onSwipeLeft()
            break
          case "right":
            if (onSwipeRight) onSwipeRight()
            break
          case "up":
            if (onSwipeUp) onSwipeUp()
            break
          case "down":
            if (onSwipeDown) onSwipeDown()
            break
        }
      },
      onEnd: () => {
        setIsDragging(false)
        setDragDelta({ x: 0, y: 0 })

        // Reset swipe direction after a short delay
        setTimeout(() => {
          setSwipeDirection(null)
        }, 300)
      },
    })

    return () => {
      if (gestureRef.current) {
        gestureRef.current.destroy()
      }
    }
  }, [threshold, enableHaptics, onSwipe, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown])

  // Calculate transform style for drag effect
  const getTransformStyle = () => {
    if (!isDragging) return {}

    // Limit the drag distance
    const maxDrag = 40
    const x = Math.max(-maxDrag, Math.min(maxDrag, dragDelta.x * 0.5))
    const y = Math.max(-maxDrag, Math.min(maxDrag, dragDelta.y * 0.5))

    return {
      transform: `translate(${x}px, ${y}px)`,
      transition: isDragging ? "none" : "transform 0.3s ease-out",
    }
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-hidden touch-pan-y",
        {
          "transition-transform duration-300 ease-out": !isDragging,
          "cursor-grabbing": isDragging,
        },
        className,
      )}
    >
      <div
        style={getTransformStyle()}
        className={cn("w-full h-full", {
          "transition-transform duration-300 ease-out": !isDragging,
        })}
      >
        {children}
      </div>

      {showIndicators && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Left indicator */}
          <div
            className={cn("absolute left-0 top-0 bottom-0 w-1 bg-primary opacity-0 transition-opacity", {
              "opacity-50": swipeDirection === "left",
            })}
          />

          {/* Right indicator */}
          <div
            className={cn("absolute right-0 top-0 bottom-0 w-1 bg-primary opacity-0 transition-opacity", {
              "opacity-50": swipeDirection === "right",
            })}
          />

          {/* Top indicator */}
          <div
            className={cn("absolute top-0 left-0 right-0 h-1 bg-primary opacity-0 transition-opacity", {
              "opacity-50": swipeDirection === "up",
            })}
          />

          {/* Bottom indicator */}
          <div
            className={cn("absolute bottom-0 left-0 right-0 h-1 bg-primary opacity-0 transition-opacity", {
              "opacity-50": swipeDirection === "down",
            })}
          />
        </div>
      )}
    </div>
  )
}

