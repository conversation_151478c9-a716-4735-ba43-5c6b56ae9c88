import { Separator } from "@/components/ui/separator"
import { BiometricSettings } from "@/components/settings/biometric-settings"
import { isNative } from "@/lib/native/native-features"

export default function SecuritySettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Security Settings</h3>
        <p className="text-sm text-muted-foreground">Manage your account security settings and preferences</p>
      </div>

      <Separator />

      {/* Password Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Password</h4>
        {/* Password settings component would go here */}
      </div>

      <Separator />

      {/* Two-Factor Authentication */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Two-Factor Authentication</h4>
        {/* 2FA settings component would go here */}
      </div>

      {/* Biometric Authentication (only shown on native platforms) */}
      {isNative() && (
        <>
          <Separator />

          <div className="space-y-4">
            <h4 className="text-sm font-medium">Biometric Authentication</h4>
            <BiometricSettings />
          </div>
        </>
      )}

      <Separator />

      {/* Session Management */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Active Sessions</h4>
        {/* Session management component would go here */}
      </div>
    </div>
  )
}

