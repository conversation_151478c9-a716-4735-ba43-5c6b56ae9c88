import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { ArchivingService } from "@/lib/services/archiving-service"
import { ApiError } from "@/lib/api-error"
import { AuditService, AuditAction } from "@/lib/services/audit-service"

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to delete archives
    if (session.user.role !== "ADMIN") {
      throw ApiError.forbidden("You do not have permission to delete archives")
    }

    const archiveId = params.id

    if (!archiveId) {
      throw ApiError.badRequest("No archive ID provided")
    }

    // Delete archive
    const result = await ArchivingService.deleteArchive(archiveId)

    // Log audit event
    await AuditService.log({
      action: AuditAction.DELETE,
      entityType: "Archive",
      entityId: archiveId,
      userId: session.user.id,
      ipAddress: req.headers.get("x-forwarded-for") || req.ip,
      userAgent: req.headers.get("user-agent"),
    })

    return NextResponse.json(result)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Delete archive error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

