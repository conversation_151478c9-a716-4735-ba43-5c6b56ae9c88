import { type NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { addPerformanceIndexes } from "@/lib/db/migrations/add-performance-indexes"

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const result = await addPerformanceIndexes()

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error optimizing database:", error)
    return NextResponse.json({ error: "Failed to optimize database" }, { status: 500 })
  }
}

