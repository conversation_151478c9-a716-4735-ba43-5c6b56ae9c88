// This file will be loaded as a Web Worker
// It handles heavy computations without blocking the main thread

// Helper function to calculate sales trends
function calculateSalesTrends(salesData: any[]) {
  // Group sales by day
  const salesByDay = salesData.reduce((acc, sale) => {
    const date = new Date(sale.createdAt).toISOString().split("T")[0]
    if (!acc[date]) {
      acc[date] = 0
    }
    acc[date] += sale.total
    return acc
  }, {})

  // Convert to array format for charting
  const trends = Object.entries(salesByDay).map(([date, total]) => ({
    date,
    total,
  }))

  // Sort by date
  trends.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

  return trends
}

// Helper function to analyze inventory
function analyzeInventory(products: any[]) {
  // Calculate inventory value
  const totalValue = products.reduce((sum, product) => sum + product.costPrice * product.quantity, 0)

  // Identify low stock items
  const lowStockItems = products.filter((product) => product.quantity <= product.reorderLevel)

  // Calculate inventory turnover (if sales data is available)
  // This is a simplified calculation
  const inventoryTurnover = products.reduce((acc, product) => {
    if (product.soldQuantity && product.averageQuantity) {
      acc.push({
        id: product.id,
        name: product.name,
        turnover: product.soldQuantity / product.averageQuantity,
      })
    }
    return acc
  }, [])

  return {
    totalValue,
    lowStockItems,
    inventoryTurnover,
  }
}

// Helper function for forecasting
function forecastInventory(products: any[], salesHistory: any[]) {
  return products.map((product) => {
    // Get sales history for this product
    const productSales = salesHistory.filter((sale) => sale.productId === product.id)

    // Calculate average daily sales (simplified)
    const totalSold = productSales.reduce((sum, sale) => sum + sale.quantity, 0)
    const daysCovered = productSales.length > 0 ? 30 : 1 // Assume 30 days if we have data
    const avgDailySales = totalSold / daysCovered

    // Calculate days until stockout
    const daysUntilStockout = avgDailySales > 0 ? Math.floor(product.quantity / avgDailySales) : 999 // If no sales, assume very long time

    // Determine if reorder is needed
    const reorderNeeded = daysUntilStockout <= 14 // Reorder if less than 2 weeks of stock

    return {
      id: product.id,
      name: product.name,
      currentStock: product.quantity,
      avgDailySales,
      daysUntilStockout,
      reorderNeeded,
    }
  })
}

// Listen for messages from the main thread
self.addEventListener("message", (event) => {
  const { task, data } = event.data

  let result

  switch (task) {
    case "calculateSalesTrends":
      result = calculateSalesTrends(data.sales)
      break
    case "analyzeInventory":
      result = analyzeInventory(data.products)
      break
    case "forecastInventory":
      result = forecastInventory(data.products, data.salesHistory)
      break
    default:
      result = { error: "Unknown task" }
  }

  // Send the result back to the main thread
  self.postMessage({ task, result })
})

// Export empty type for TypeScript
export {}

