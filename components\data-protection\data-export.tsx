"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2, Download, FileJson, FileSpreadsheetIcon as FileCsv } from "lucide-react"
import { exportUserData } from "@/app/actions/data-protection"

interface DataExportProps {
  userId: string
}

export function DataExport({ userId }: DataExportProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [format, setFormat] = useState<"json" | "csv">("json")

  const handleExport = async () => {
    setIsLoading(true)
    try {
      const data = await exportUserData(userId, format)

      // Create a blob and download it
      const blob = new Blob([data], { type: format === "json" ? "application/json" : "text/csv" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `user-data-export.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error exporting data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Export Your Data</CardTitle>
        <CardDescription>Download a copy of your personal data in a machine-readable format</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <div className="flex space-x-4">
            <Button
              variant={format === "json" ? "default" : "outline"}
              onClick={() => setFormat("json")}
              className="flex-1"
            >
              <FileJson className="mr-2 h-4 w-4" />
              JSON Format
            </Button>
            <Button
              variant={format === "csv" ? "default" : "outline"}
              onClick={() => setFormat("csv")}
              className="flex-1"
            >
              <FileCsv className="mr-2 h-4 w-4" />
              CSV Format
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            {format === "json"
              ? "JSON format is best for importing into other applications."
              : "CSV format is best for opening in spreadsheet applications."}
          </p>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleExport} disabled={isLoading} className="w-full">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Preparing Export...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Export My Data
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

