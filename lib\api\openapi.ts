import { OpenAPIRegistry } from "@asteasolutions/zod-to-openapi"
import { createDocument } from "@asteasolutions/zod-to-openapi"
import { z } from "zod"
import { productSchema } from "@/lib/validations/product-schema"
import { customerSchema } from "@/lib/validations/customer-schema"
import { supplierSchema } from "@/lib/validations/supplier-schema"
import { categorySchema } from "@/lib/validations/category-schema"
import { orderSchema } from "@/lib/validations/order-schema"

// Create OpenAPI registry
const registry = new OpenAPIRegistry()

// Define server
registry.registerServer({
  url: process.env.NEXTAUTH_URL || "http://localhost:3000",
  description: "StockSync API Server",
})

// Define security schemes
registry.registerComponent("securitySchemes", "bearerAuth", {
  type: "http",
  scheme: "bearer",
  bearerFormat: "JWT",
  description: "JWT Authorization header using the Bearer scheme",
})

// Define common responses
const ErrorResponse = registry.register(
  "ErrorResponse",
  z.object({
    error: z.object({
      message: z.string(),
      code: z.string().optional(),
    }),
  }),
)

const PaginationParams = registry.register(
  "PaginationParams",
  z.object({
    page: z.number().int().min(1).default(1).describe("Page number"),
    limit: z.number().int().min(1).max(100).default(10).describe("Items per page"),
  }),
)

// Register schemas
const ProductResponse = registry.register("Product", productSchema)
const ProductListResponse = registry.register(
  "ProductList",
  z.object({
    data: z.array(ProductResponse),
    meta: z.object({
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
  }),
)

const CustomerResponse = registry.register("Customer", customerSchema)
const CustomerListResponse = registry.register(
  "CustomerList",
  z.object({
    data: z.array(CustomerResponse),
    meta: z.object({
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
  }),
)

const SupplierResponse = registry.register("Supplier", supplierSchema)
const SupplierListResponse = registry.register(
  "SupplierList",
  z.object({
    data: z.array(SupplierResponse),
    meta: z.object({
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
  }),
)

const CategoryResponse = registry.register("Category", categorySchema)
const CategoryListResponse = registry.register(
  "CategoryList",
  z.object({
    data: z.array(CategoryResponse),
    meta: z.object({
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
  }),
)

const OrderResponse = registry.register("Order", orderSchema)
const OrderListResponse = registry.register(
  "OrderList",
  z.object({
    data: z.array(OrderResponse),
    meta: z.object({
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
  }),
)

// Define API paths

// Products
registry.registerPath({
  method: "get",
  path: "/api/products",
  tags: ["Products"],
  summary: "Get all products",
  security: [{ bearerAuth: [] }],
  request: {
    query: PaginationParams,
  },
  responses: {
    200: {
      description: "List of products",
      content: {
        "application/json": {
          schema: ProductListResponse,
        },
      },
    },
    401: {
      description: "Unauthorized",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    500: {
      description: "Internal Server Error",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
  },
})

registry.registerPath({
  method: "post",
  path: "/api/products",
  tags: ["Products"],
  summary: "Create a new product",
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        "application/json": {
          schema: ProductResponse.omit({ id: true, createdAt: true, updatedAt: true }),
        },
      },
    },
  },
  responses: {
    201: {
      description: "Product created successfully",
      content: {
        "application/json": {
          schema: ProductResponse,
        },
      },
    },
    400: {
      description: "Bad Request",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    401: {
      description: "Unauthorized",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    500: {
      description: "Internal Server Error",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
  },
})

registry.registerPath({
  method: "get",
  path: "/api/products/{id}",
  tags: ["Products"],
  summary: "Get a product by ID",
  security: [{ bearerAuth: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().describe("Product ID"),
    }),
  },
  responses: {
    200: {
      description: "Product details",
      content: {
        "application/json": {
          schema: ProductResponse,
        },
      },
    },
    404: {
      description: "Product not found",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    401: {
      description: "Unauthorized",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    500: {
      description: "Internal Server Error",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
  },
})

registry.registerPath({
  method: "put",
  path: "/api/products/{id}",
  tags: ["Products"],
  summary: "Update a product",
  security: [{ bearerAuth: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().describe("Product ID"),
    }),
    body: {
      content: {
        "application/json": {
          schema: ProductResponse.omit({ id: true, createdAt: true, updatedAt: true }),
        },
      },
    },
  },
  responses: {
    200: {
      description: "Product updated successfully",
      content: {
        "application/json": {
          schema: ProductResponse,
        },
      },
    },
    400: {
      description: "Bad Request",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    404: {
      description: "Product not found",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    401: {
      description: "Unauthorized",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    500: {
      description: "Internal Server Error",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
  },
})

registry.registerPath({
  method: "delete",
  path: "/api/products/{id}",
  tags: ["Products"],
  summary: "Delete a product",
  security: [{ bearerAuth: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().describe("Product ID"),
    }),
  },
  responses: {
    200: {
      description: "Product deleted successfully",
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            message: z.string(),
          }),
        },
      },
    },
    404: {
      description: "Product not found",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    401: {
      description: "Unauthorized",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
    500: {
      description: "Internal Server Error",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
  },
})

// Similar paths for Customers, Suppliers, Categories, and Orders
// ... (omitted for brevity)

// Generate OpenAPI document
export const openApiDocument = createDocument({
  openapi: "3.0.0",
  info: {
    title: "StockSync API",
    version: "1.0.0",
    description: "API documentation for StockSync inventory management system",
    contact: {
      name: "StockSync Support",
      email: "<EMAIL>",
      url: "https://stocksync.example.com/support",
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT",
    },
  },
  components: registry.definitions.components,
  paths: registry.definitions.paths,
})

// Export registry for reuse
export { registry }

