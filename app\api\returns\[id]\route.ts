import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const returnData = await prisma.return.findUnique({
      where: { id: params.id },
      include: {
        customer: true,
        order: {
          include: {
            items: true,
          },
        },
        items: {
          include: {
            product: true,
            variant: true,
            orderItem: true,
          },
        },
        statusHistory: {
          orderBy: {
            createdAt: "desc",
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    if (!returnData) {
      return NextResponse.json({ error: "Return not found" }, { status: 404 })
    }

    return NextResponse.json(returnData)
  } catch (error) {
    console.error("Error fetching return:", error)
    return NextResponse.json({ error: "Failed to fetch return" }, { status: 500 })
  }
}

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const { status, notes, refundAmount, refundMethod, refundDate } = body

    // Validate the return exists
    const returnData = await prisma.return.findUnique({
      where: { id: params.id },
    })

    if (!returnData) {
      return NextResponse.json({ error: "Return not found" }, { status: 404 })
    }

    // Update the return in a transaction
    const updatedReturn = await prisma.$transaction(async (tx) => {
      // Update the return status
      const returnRecord = await tx.return.update({
        where: { id: params.id },
        data: {
          status,
          notes: notes || returnData.notes,
          refundAmount: refundAmount !== undefined ? refundAmount : returnData.refundAmount,
          refundMethod: refundMethod || returnData.refundMethod,
          refundDate: refundDate ? new Date(refundDate) : returnData.refundDate,
          approvedById: status === "APPROVED" ? session.user.id : returnData.approvedById,
        },
      })

      // Add status history
      await tx.returnStatusHistory.create({
        data: {
          returnId: params.id,
          status,
          notes: notes || `Status updated to ${status}`,
        },
      })

      // If status is APPROVED, create a notification
      if (status === "APPROVED") {
        await tx.notification.create({
          data: {
            title: "Return Approved",
            message: `Return #${returnData.returnNumber} has been approved.`,
            type: "SUCCESS",
            userId: session.user.id,
          },
        })
      }

      // If status is REFUNDED, update inventory
      if (status === "REFUNDED" || status === "PARTIALLY_REFUNDED") {
        // Get return items
        const returnItems = await tx.returnItem.findMany({
          where: { returnId: params.id },
          include: {
            product: true,
            variant: true,
          },
        })

        // Update inventory for each item
        for (const item of returnItems) {
          if (item.variant) {
            await tx.productVariant.update({
              where: { id: item.variantId! },
              data: {
                inventoryLevel: {
                  increment: item.quantity,
                },
              },
            })
          } else {
            await tx.product.update({
              where: { id: item.productId },
              data: {
                inventoryLevel: {
                  increment: item.quantity,
                },
              },
            })
          }

          // Add inventory history record
          await tx.inventoryHistory.create({
            data: {
              productId: item.productId,
              quantity: item.quantity,
              type: "RETURN",
              reference: params.id,
              notes: `Return #${returnData.returnNumber}`,
            },
          })
        }

        // Create notification for inventory update
        await tx.notification.create({
          data: {
            title: "Inventory Updated",
            message: `Inventory has been updated for return #${returnData.returnNumber}.`,
            type: "INFO",
            userId: session.user.id,
          },
        })
      }

      return returnRecord
    })

    return NextResponse.json(updatedReturn)
  } catch (error) {
    console.error("Error updating return:", error)
    return NextResponse.json({ error: "Failed to update return" }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if return exists
    const returnData = await prisma.return.findUnique({
      where: { id: params.id },
    })

    if (!returnData) {
      return NextResponse.json({ error: "Return not found" }, { status: 404 })
    }

    // Only allow deletion of returns in REQUESTED status
    if (returnData.status !== "REQUESTED") {
      return NextResponse.json({ error: "Only returns in REQUESTED status can be deleted" }, { status: 400 })
    }

    // Delete the return
    await prisma.return.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting return:", error)
    return NextResponse.json({ error: "Failed to delete return" }, { status: 500 })
  }
}

