import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { parseISO, format, startOfDay, endOfDay, startOfWeek } from "date-fns"

export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const fromDate = searchParams.get("from")
    const toDate = searchParams.get("to")
    const category = searchParams.get("category")
    const store = searchParams.get("store")
    const product = searchParams.get("product")
    const groupBy = searchParams.get("groupBy") || "day"

    if (!fromDate || !toDate) {
      return NextResponse.json({ error: "Missing date range parameters" }, { status: 400 })
    }

    // Parse dates
    const from = startOfDay(parseISO(fromDate))
    const to = endOfDay(parseISO(toDate))

    // Build query filters
    const filters: any = {
      createdAt: {
        gte: from,
        lte: to,
      },
    }

    if (category) {
      filters.items = {
        some: {
          product: {
            categoryId: category,
          },
        },
      }
    }

    if (store) {
      filters.storeId = store
    }

    if (product) {
      filters.items = {
        some: {
          productId: product,
        },
      }
    }

    // Fetch orders
    const orders = await prisma.order.findMany({
      where: filters,
      include: {
        items: {
          include: {
            product: {
              include: {
                category: true,
              },
            },
          },
        },
        customer: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    })

    // Calculate metrics
    const totalRevenue = orders.reduce((sum, order) => sum + Number(order.total), 0)
    const totalOrders = orders.length
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0
    const itemsSold = orders.reduce(
      (sum, order) => sum + order.items.reduce((itemSum, item) => itemSum + item.quantity, 0),
      0,
    )

    // Calculate previous period metrics for comparison
    const periodLength = to.getTime() - from.getTime()
    const previousFrom = new Date(from.getTime() - periodLength)
    const previousTo = new Date(to.getTime() - periodLength)

    const previousPeriodOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: previousFrom,
          lte: previousTo,
        },
      },
      select: {
        total: true,
        items: {
          select: {
            quantity: true,
          },
        },
      },
    })

    const previousTotalRevenue = previousPeriodOrders.reduce((sum, order) => sum + Number(order.total), 0)
    const previousTotalOrders = previousPeriodOrders.length
    const previousItemsSold = previousPeriodOrders.reduce(
      (sum, order) => sum + order.items.reduce((itemSum, item) => itemSum + item.quantity, 0),
      0,
    )

    // Calculate percentage changes
    const revenueChange =
      previousTotalRevenue > 0 ? ((totalRevenue - previousTotalRevenue) / previousTotalRevenue) * 100 : 0
    const ordersChange = previousTotalOrders > 0 ? ((totalOrders - previousTotalOrders) / previousTotalOrders) * 100 : 0
    const aovChange =
      previousTotalOrders > 0
        ? ((averageOrderValue - previousTotalRevenue / previousTotalOrders) /
            (previousTotalRevenue / previousTotalOrders)) *
          100
        : 0
    const itemsSoldChange = previousItemsSold > 0 ? ((itemsSold - previousItemsSold) / previousItemsSold) * 100 : 0

    // Generate chart data based on groupBy parameter
    let chartData: any[] = []

    if (groupBy === "day") {
      // Group by day
      const dailyData = new Map()

      orders.forEach((order) => {
        const day = format(order.createdAt, "yyyy-MM-dd")
        const existing = dailyData.get(day) || { date: day, revenue: 0, orders: 0, items: 0 }

        existing.revenue += Number(order.total)
        existing.orders += 1
        existing.items += order.items.reduce((sum, item) => sum + item.quantity, 0)

        dailyData.set(day, existing)
      })

      chartData = Array.from(dailyData.values())
    } else if (groupBy === "week") {
      // Group by week
      const weeklyData = new Map()

      orders.forEach((order) => {
        const weekStart = startOfWeek(order.createdAt)
        const week = format(weekStart, "yyyy-'W'ww")
        const existing = weeklyData.get(week) || { week, revenue: 0, orders: 0, items: 0 }

        existing.revenue += Number(order.total)
        existing.orders += 1
        existing.items += order.items.reduce((sum, item) => sum + item.quantity, 0)

        weeklyData.set(week, existing)
      })

      chartData = Array.from(weeklyData.values())
    } else if (groupBy === "month") {
      // Group by month
      const monthlyData = new Map()

      orders.forEach((order) => {
        const month = format(order.createdAt, "yyyy-MM")
        const existing = monthlyData.get(month) || { month, revenue: 0, orders: 0, items: 0 }

        existing.revenue += Number(order.total)
        existing.orders += 1
        existing.items += order.items.reduce((sum, item) => sum + item.quantity, 0)

        monthlyData.set(month, existing)
      })

      chartData = Array.from(monthlyData.values())
    }

    // Generate pie chart data
    const pieData = [
      { name: "Revenue", value: totalRevenue },
      { name: "Tax", value: orders.reduce((sum, order) => sum + Number(order.tax), 0) },
      { name: "Discount", value: orders.reduce((sum, order) => sum + (Number(order.discount) || 0), 0) },
    ]

    // Generate table data
    const tableData = orders.map((order) => ({
      id: order.id,
      orderNumber: order.orderNumber,
      date: order.createdAt.toISOString(),
      customer: order.customer?.name || "Guest",
      items: order.items.reduce((sum, item) => sum + item.quantity, 0),
      total: Number(order.total),
      status: order.status,
    }))

    return NextResponse.json({
      metrics: {
        totalRevenue,
        totalOrders,
        averageOrderValue,
        itemsSold,
        revenueChange,
        ordersChange,
        aovChange,
        itemsSoldChange,
        totalProfit: totalRevenue * 0.3, // Simplified profit calculation
        totalTax: orders.reduce((sum, order) => sum + Number(order.tax), 0),
      },
      chartData,
      pieData,
      tableData,
    })
  } catch (error) {
    console.error("Error generating sales report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

