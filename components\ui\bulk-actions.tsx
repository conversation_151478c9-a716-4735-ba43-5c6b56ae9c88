"use client"

import type React from "react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { ChevronDown, Loader2 } from "lucide-react"
import { useAccessibility } from "@/providers/accessibility-provider"

export interface BulkAction<T> {
  id: string
  label: string
  icon?: React.ReactNode
  action: (selectedItems: T[]) => Promise<void>
  confirmationRequired?: boolean
  confirmationMessage?: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
}

interface BulkActionsProps<T> {
  actions: BulkAction<T>[]
  selectedItems: T[]
  getItemId: (item: T) => string
  getItemLabel?: (item: T) => string
  onActionComplete?: () => void
  className?: string
}

export function BulkActions<T>({
  actions,
  selectedItems,
  getItemId,
  getItemLabel = (item) => getItemId(item),
  onActionComplete,
  className,
}: BulkActionsProps<T>) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentActionId, setCurrentActionId] = useState<string | null>(null)
  const { announceMessage } = useAccessibility()

  const handleAction = async (action: BulkAction<T>) => {
    if (selectedItems.length === 0) {
      announceMessage("No items selected", "assertive")
      return
    }

    // Check if confirmation is required
    if (action.confirmationRequired) {
      const message =
        action.confirmationMessage ||
        `Are you sure you want to ${action.label.toLowerCase()} ${selectedItems.length} selected item(s)?`

      if (!window.confirm(message)) {
        return
      }
    }

    try {
      setIsProcessing(true)
      setCurrentActionId(action.id)

      await action.action(selectedItems)

      announceMessage(`${action.label} completed for ${selectedItems.length} item(s)`, "polite")

      if (onActionComplete) {
        onActionComplete()
      }
    } catch (error) {
      console.error(`Error performing bulk action ${action.label}:`, error)
      announceMessage(`Error performing ${action.label}. Please try again.`, "assertive")
    } finally {
      setIsProcessing(false)
      setCurrentActionId(null)
    }
  }

  return (
    <div
      className={cn("flex items-center space-x-2 rounded-md border bg-background p-2", className)}
      role="region"
      aria-label="Bulk actions"
    >
      <span className="text-sm font-medium">
        {selectedItems.length} item{selectedItems.length !== 1 ? "s" : ""} selected
      </span>

      {actions.length <= 3 ? (
        // Show individual buttons for a few actions
        <div className="flex space-x-2">
          {actions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant || "default"}
              size="sm"
              onClick={() => handleAction(action)}
              disabled={isProcessing || selectedItems.length === 0}
              aria-busy={isProcessing && currentActionId === action.id}
            >
              {isProcessing && currentActionId === action.id ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                action.icon && <span className="mr-2">{action.icon}</span>
              )}
              {action.label}
            </Button>
          ))}
        </div>
      ) : (
        // Use dropdown for many actions
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" disabled={isProcessing || selectedItems.length === 0}>
              Actions
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {actions.map((action) => (
              <DropdownMenuItem
                key={action.id}
                onClick={() => handleAction(action)}
                disabled={isProcessing}
                className={cn({
                  "text-destructive": action.variant === "destructive",
                })}
              >
                {action.icon && <span className="mr-2">{action.icon}</span>}
                {action.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  )
}

