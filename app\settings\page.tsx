import Link from "next/link"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Settings, Bell, Mic, Database, User, Shield } from "lucide-react"

export default function SettingsPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Link href="/settings/profile" className="block">
          <Card className="h-full transition-colors hover:bg-accent/5">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-medium">Profile</CardTitle>
              <User className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <CardDescription>Manage your account settings and preferences.</CardDescription>
            </CardContent>
          </Card>
        </Link>

        <Link href="/settings/security" className="block">
          <Card className="h-full transition-colors hover:bg-accent/5">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-medium">Security</CardTitle>
              <Shield className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <CardDescription>Configure security settings and permissions.</CardDescription>
            </CardContent>
          </Card>
        </Link>

        <Link href="/settings/database" className="block">
          <Card className="h-full transition-colors hover:bg-accent/5">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-medium">Database</CardTitle>
              <Database className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <CardDescription>Manage database settings and backups.</CardDescription>
            </CardContent>
          </Card>
        </Link>

        <Link href="/settings/notifications" className="block">
          <Card className="h-full transition-colors hover:bg-accent/5">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-medium">Notifications</CardTitle>
              <Bell className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <CardDescription>Configure push notification preferences.</CardDescription>
            </CardContent>
          </Card>
        </Link>

        <Link href="/settings/voice-commands" className="block">
          <Card className="h-full transition-colors hover:bg-accent/5">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-medium">Voice Commands</CardTitle>
              <Mic className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <CardDescription>Configure voice commands for hands-free operation.</CardDescription>
            </CardContent>
          </Card>
        </Link>

        <Link href="/settings/general" className="block">
          <Card className="h-full transition-colors hover:bg-accent/5">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-medium">General</CardTitle>
              <Settings className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <CardDescription>Configure general application settings.</CardDescription>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}

