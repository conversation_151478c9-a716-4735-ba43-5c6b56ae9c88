import { cache } from "react"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"

// Cached user data fetching for server components
export const getCurrentUser = cache(async () => {
  const session = await auth()
  if (!session?.user?.id) return null

  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      id: true,
      name: true,
      email: true,
      image: true,
      role: true,
      settings: true,
    },
  })

  return user
})

// Cached product data fetching for server components
export const getProducts = cache(
  async (
    options: {
      page?: number
      limit?: number
      search?: string
      category?: string
      sort?: string
      order?: "asc" | "desc"
    } = {},
  ) => {
    const session = await auth()
    if (!session?.user?.id) return { products: [], pagination: { totalCount: 0 } }

    const { page = 1, limit = 20, search = "", category = "", sort = "name", order = "asc" } = options

    const offset = (page - 1) * limit

    const where = {
      userId: session.user.id,
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { sku: { contains: search, mode: "insensitive" } },
              { description: { contains: search, mode: "insensitive" } },
            ],
          }
        : {}),
      ...(category ? { categoryId: category } : {}),
    }

    const [products, totalCount] = await Promise.all([
      db.product.findMany({
        where,
        include: {
          category: true,
          supplier: true,
        },
        take: limit,
        skip: offset,
        orderBy: {
          [sort]: order,
        },
      }),
      db.product.count({ where }),
    ])

    return {
      products,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasMore: offset + products.length < totalCount,
      },
    }
  },
)

// Similar cached functions for other entities...

