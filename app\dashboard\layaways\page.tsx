import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LayawaysList } from "@/components/layaways/layaways-list"
import Link from "next/link"
import { Plus } from "lucide-react"
import prisma from "@/lib/prisma"

async function getLayaways() {
  const layaways = await prisma.layaway.findMany({
    include: {
      customer: true,
      items: true,
      payments: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  })

  return layaways
}

export default async function LayawaysPage() {
  const layaways = await getLayaways()

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Layaways</h2>
        <Button asChild>
          <Link href="/dashboard/layaways/new">
            <Plus className="mr-2 h-4 w-4" /> New Layaway
          </Link>
        </Button>
      </div>

      <Suspense fallback={<div>Loading layaways...</div>}>
        <LayawaysList layaways={layaways} />
      </Suspense>
    </div>
  )
}

