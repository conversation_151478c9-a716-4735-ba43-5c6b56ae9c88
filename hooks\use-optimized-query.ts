"use client"

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/components/ui/use-toast"

interface UseOptimizedQueryOptions<T> {
  queryKey: string[]
  queryFn: () => Promise<T>
  staleTime?: number
  cacheTime?: number
  retry?: number | boolean
  refetchOnWindowFocus?: boolean
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
}

export function useOptimizedQuery<T>({
  queryKey,
  queryFn,
  staleTime = 1000 * 60 * 5, // 5 minutes
  cacheTime = 1000 * 60 * 30, // 30 minutes
  retry = 1,
  refetchOnWindowFocus = false,
  onSuccess,
  onError,
}: UseOptimizedQueryOptions<T>) {
  return useQuery({
    queryKey,
    queryFn,
    staleTime,
    gcTime: cacheTime,
    retry,
    refetchOnWindowFocus,
    onSuccess,
    onError: (error: any) => {
      console.error(`Query error for ${queryKey.join("/")}:`, error)
      toast({
        title: "Error",
        description: error.message || "An error occurred while fetching data",
        variant: "destructive",
      })
      if (onError) onError(error)
    },
  })
}

interface UseOptimizedMutationOptions<TData, TVariables> {
  mutationFn: (variables: TVariables) => Promise<TData>
  onSuccess?: (data: TData, variables: TVariables) => void
  onError?: (error: Error, variables: TVariables) => void
  onSettled?: (data: TData | undefined, error: Error | null, variables: TVariables) => void
  invalidateQueries?: string[][]
}

export function useOptimizedMutation<TData, TVariables>({
  mutationFn,
  onSuccess,
  onError,
  onSettled,
  invalidateQueries = [],
}: UseOptimizedMutationOptions<TData, TVariables>) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn,
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      invalidateQueries.forEach((queryKey) => {
        queryClient.invalidateQueries({ queryKey })
      })

      if (onSuccess) onSuccess(data, variables)
    },
    onError: (error: any, variables) => {
      console.error("Mutation error:", error)
      toast({
        title: "Error",
        description: error.message || "An error occurred",
        variant: "destructive",
      })
      if (onError) onError(error, variables)
    },
    onSettled: (data, error, variables) => {
      if (onSettled) onSettled(data, error, variables as TVariables)
    },
  })
}

