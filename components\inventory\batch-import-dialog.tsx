"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, Di<PERSON>Footer } from "@/components/ui/dialog"
import { <PERSON>ton } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { Upload, AlertCircle, FileText, Check, X } from "lucide-react"
import { CsvProcessor } from "@/lib/utils/csv-processor"
import { productExportHeaders } from "@/lib/validations/product-import-schema"
import { useRouter } from "next/navigation"

interface BatchImportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function BatchImportDialog({ open, onOpenChange }: BatchImportDialogProps) {
  const { toast } = useToast()
  const router = useRouter()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null
    setFile(selectedFile)
    setError(null)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    const droppedFile = e.dataTransfer.files[0]

    if (droppedFile && droppedFile.name.endsWith(".csv")) {
      setFile(droppedFile)
      setError(null)
    } else {
      setError("Please upload a CSV file")
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }

  const downloadTemplate = () => {
    const csv = CsvProcessor.generateTemplate(productExportHeaders)
    const blob = new Blob([csv], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = "product-import-template.csv"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleSubmit = async () => {
    if (!file) {
      setError("Please select a file to upload")
      return
    }

    setIsUploading(true)
    setUploadProgress(0)
    setError(null)

    try {
      const formData = new FormData()
      formData.append("file", file)

      const xhr = new XMLHttpRequest()
      xhr.open("POST", "/api/batch/import", true)

      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100)
          setUploadProgress(progress)
        }
      }

      xhr.onload = () => {
        setIsUploading(false)

        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText)

          toast({
            title: "Import started",
            description: "Your products are being imported. You will be notified when the process is complete.",
          })

          onOpenChange(false)
          router.push(`/dashboard/products/batch/${response.batchOperationId}`)
        } else {
          let errorMessage = "An error occurred during upload"

          try {
            const response = JSON.parse(xhr.responseText)
            errorMessage = response.error?.message || errorMessage
          } catch (e) {
            // Parsing error, use default message
          }

          setError(errorMessage)
        }
      }

      xhr.onerror = () => {
        setIsUploading(false)
        setError("Network error occurred during upload")
      }

      xhr.send(formData)
    } catch (err) {
      setIsUploading(false)
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Import Products</DialogTitle>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div
          className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-muted/50 transition-colors"
          onClick={() => fileInputRef.current?.click()}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".csv" className="hidden" />

          <Upload className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />

          {file ? (
            <div className="flex items-center justify-center gap-2 text-sm">
              <FileText className="h-4 w-4" />
              <span className="font-medium">{file.name}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={(e) => {
                  e.stopPropagation()
                  setFile(null)
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <>
              <p className="text-sm font-medium mb-1">Click to upload or drag and drop</p>
              <p className="text-xs text-muted-foreground">CSV files only (max 10MB)</p>
            </>
          )}
        </div>

        {isUploading && (
          <div className="space-y-2">
            <Progress value={uploadProgress} />
            <p className="text-xs text-center text-muted-foreground">Uploading... {uploadProgress}%</p>
          </div>
        )}

        <div className="bg-muted rounded-lg p-3">
          <h4 className="text-sm font-medium mb-2">Before you import:</h4>
          <ul className="text-xs space-y-1 text-muted-foreground">
            <li className="flex items-start gap-2">
              <Check className="h-3 w-3 mt-0.5 text-green-500" />
              <span>Make sure your CSV file follows the required format</span>
            </li>
            <li className="flex items-start gap-2">
              <Check className="h-3 w-3 mt-0.5 text-green-500" />
              <span>Required fields: Name, SKU, Price, Stock Quantity, Category</span>
            </li>
            <li className="flex items-start gap-2">
              <Check className="h-3 w-3 mt-0.5 text-green-500" />
              <span>Products with existing SKUs will be updated</span>
            </li>
          </ul>

          <Button
            variant="link"
            size="sm"
            className="px-0 text-xs mt-2"
            onClick={(e) => {
              e.stopPropagation()
              downloadTemplate()
            }}
          >
            Download template
          </Button>
        </div>

        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isUploading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!file || isUploading}>
            {isUploading ? "Uploading..." : "Import Products"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

