"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, ArrowUpDown, MoveUp, MoveDown } from "lucide-react"

interface Field {
  id: string
  name: string
  label: string
  type: string
  category?: string
}

interface SortField {
  field: string
  direction: "asc" | "desc"
}

interface ReportSortBuilderProps {
  availableFields: Field[]
  sortFields: SortField[]
  onSortFieldsChange: (sortFields: SortField[]) => void
}

export function ReportSortBuilder({
  availableFields = [],
  sortFields = [],
  onSortFieldsChange,
}: ReportSortBuilderProps) {
  // Add a new sort field
  const addSortField = () => {
    if (availableFields.length === 0) return

    const newSortField = {
      field: availableFields[0].id,
      direction: "asc" as const,
    }
    onSortFieldsChange([...sortFields, newSortField])
  }

  // Remove a sort field
  const removeSortField = (index: number) => {
    const newSortFields = [...sortFields]
    newSortFields.splice(index, 1)
    onSortFieldsChange(newSortFields)
  }

  // Update a sort field
  const updateSortField = (index: number, field: string, value: any) => {
    const newSortFields = [...sortFields]
    newSortFields[index] = { ...newSortFields[index], [field]: value }
    onSortFieldsChange(newSortFields)
  }

  // Move sort field up in priority
  const moveSortFieldUp = (index: number) => {
    if (index === 0) return

    const newSortFields = [...sortFields]
    const temp = newSortFields[index]
    newSortFields[index] = newSortFields[index - 1]
    newSortFields[index - 1] = temp
    onSortFieldsChange(newSortFields)
  }

  // Move sort field down in priority
  const moveSortFieldDown = (index: number) => {
    if (index === sortFields.length - 1) return

    const newSortFields = [...sortFields]
    const temp = newSortFields[index]
    newSortFields[index] = newSortFields[index + 1]
    newSortFields[index + 1] = temp
    onSortFieldsChange(newSortFields)
  }

  // Get field label by id
  const getFieldLabel = (fieldId: string) => {
    const field = availableFields.find((f) => f.id === fieldId)
    return field?.label || fieldId
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Badge variant="outline" className="px-2 py-1">
          {sortFields.length} sort fields applied
        </Badge>
        <Button variant="outline" size="sm" onClick={addSortField}>
          <Plus className="h-4 w-4 mr-2" />
          Add Sort Field
        </Button>
      </div>

      {sortFields.length > 0 ? (
        <div className="space-y-4">
          {sortFields.map((sortField, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                  <div className="md:col-span-1 flex items-center justify-center">
                    <Badge variant="outline" className="w-6 h-6 flex items-center justify-center p-0">
                      {index + 1}
                    </Badge>
                  </div>

                  <div className="md:col-span-6">
                    <Select value={sortField.field} onValueChange={(value) => updateSortField(index, "field", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select field" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableFields.map((field) => (
                          <SelectItem key={field.id} value={field.id}>
                            {field.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="md:col-span-3">
                    <Select
                      value={sortField.direction}
                      onValueChange={(value) => updateSortField(index, "direction", value as "asc" | "desc")}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Direction" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asc">Ascending</SelectItem>
                        <SelectItem value="desc">Descending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="md:col-span-2 flex justify-end space-x-1">
                    <Button variant="ghost" size="icon" onClick={() => moveSortFieldUp(index)} disabled={index === 0}>
                      <MoveUp className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => moveSortFieldDown(index)}
                      disabled={index === sortFields.length - 1}
                    >
                      <MoveDown className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => removeSortField(index)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 border rounded-md">
          <ArrowUpDown className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-muted-foreground">No sorting applied</p>
          <p className="text-sm text-muted-foreground">Add sort fields to organize your report results</p>
        </div>
      )}
    </div>
  )
}

