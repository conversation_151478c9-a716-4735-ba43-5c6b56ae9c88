"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  Pie,
  <PERSON><PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
} from "@/components/ui/chart"

interface InventoryReportChartProps {
  data: any
  isLoading: boolean
}

export function InventoryReportChart({ data, isLoading }: InventoryReportChartProps) {
  const [chartType, setChartType] = useState<"bar" | "pie">("bar")

  if (isLoading) {
    return <Skeleton className="w-full h-full min-h-[300px]" />
  }

  if (!data?.chartData || data.chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-full min-h-[300px] text-muted-foreground">
        No data available for the selected period
      </div>
    )
  }

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8", "#82ca9d"]

  return (
    <div className="space-y-4">
      <Tabs value={chartType} onValueChange={(value) => setChartType(value as any)}>
        <TabsList className="grid grid-cols-2 w-[200px]">
          <TabsTrigger value="bar">Bar</TabsTrigger>
          <TabsTrigger value="pie">Pie</TabsTrigger>
        </TabsList>

        <TabsContent value="bar" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="stockQuantity" fill="#3b82f6" name="Stock Quantity" />
              <Bar dataKey="value" fill="#10b981" name="Value ($)" />
            </BarChart>
          </ResponsiveContainer>
        </TabsContent>

        <TabsContent value="pie" className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data.pieData || data.chartData}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {data.pieData?.map((entry: any, index: number) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => `$${value}`} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </TabsContent>
      </Tabs>
    </div>
  )
}

