import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const customerId = params.id

    const customer = await prisma.customer.findUnique({
      where: {
        id: customerId,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        loyaltyPoints: true,
        loyaltyTier: true,
      },
    })

    if (!customer) {
      return new NextResponse("Customer not found", { status: 404 })
    }

    const transactions = await prisma.loyaltyTransaction.findMany({
      where: {
        customerId,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json({
      customer,
      transactions,
    })
  } catch (error) {
    console.error("[CUSTOMER_LOYALTY_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function POST(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const customerId = params.id
    const body = await req.json()
    const { points, type, description } = body

    const customer = await prisma.customer.findUnique({
      where: {
        id: customerId,
      },
    })

    if (!customer) {
      return new NextResponse("Customer not found", { status: 404 })
    }

    // Create transaction
    const transaction = await prisma.loyaltyTransaction.create({
      data: {
        customerId,
        points,
        type,
        description,
      },
    })

    // Update customer points
    let newPoints = customer.loyaltyPoints
    if (type === "EARN") {
      newPoints += points
    } else if (type === "REDEEM" || type === "EXPIRE") {
      newPoints -= points
      if (newPoints < 0) newPoints = 0
    } else if (type === "ADJUST") {
      newPoints = points
    }

    await prisma.customer.update({
      where: {
        id: customerId,
      },
      data: {
        loyaltyPoints: newPoints,
      },
    })

    // Update loyalty tier if needed
    const loyaltySettings = await prisma.loyaltySettings.findFirst({
      where: {
        userId: session.user.id,
      },
      include: {
        tiers: {
          orderBy: {
            minimumPoints: "desc",
          },
        },
      },
    })

    if (loyaltySettings && loyaltySettings.tiers.length > 0) {
      const eligibleTier = loyaltySettings.tiers.find((tier) => newPoints >= tier.minimumPoints)

      if (eligibleTier && customer.loyaltyTier !== eligibleTier.name) {
        await prisma.customer.update({
          where: {
            id: customerId,
          },
          data: {
            loyaltyTier: eligibleTier.name,
          },
        })
      }
    }

    return NextResponse.json({
      transaction,
      currentPoints: newPoints,
    })
  } catch (error) {
    console.error("[CUSTOMER_LOYALTY_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

