import { prisma } from "@/lib/prisma"

/**
 * Types of actions that can be audited
 */
export enum AuditAction {
  CREATE = "CREATE",
  UPDATE = "UPDATE",
  DELETE = "DELETE",
  LOGIN = "LOGIN",
  LOGOUT = "LOGOUT",
  EXPORT = "EXPORT",
  IMPORT = "IMPORT",
  ARCHIVE = "ARCHIVE",
  RESTORE = "RESTORE",
  PERMISSION_CHANGE = "PERMISSION_CHANGE",
  SETTING_CHANGE = "SETTING_CHANGE",
  PASSWORD_CHANGE = "PASSWORD_CHANGE",
  PASSWORD_RESET = "PASSWORD_RESET",
  FAILED_LOGIN = "FAILED_LOGIN",
}

/**
 * Service for audit logging
 */
export class AuditService {
  /**
   * Log an audit event
   */
  static async log(options: {
    action: AuditAction | string
    entityType: string
    entityId?: string
    userId: string
    details?: any
    ipAddress?: string
    userAgent?: string
  }) {
    const { action, entityType, entityId, userId, details, ipAddress, userAgent } = options

    try {
      // Create audit log entry
      const auditLog = await prisma.auditLog.create({
        data: {
          action,
          entityType,
          entityId,
          details: details ? JSON.stringify(details) : null,
          ipAddress,
          userAgent,
          userId,
        },
      })

      return {
        success: true,
        auditLogId: auditLog.id,
      }
    } catch (error) {
      console.error("Error logging audit event:", error)
      // Don't throw error, just log it
      // We don't want audit logging to break the application
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  /**
   * Get audit logs
   */
  static async getAuditLogs(options?: {
    userId?: string
    entityType?: string
    entityId?: string
    action?: string
    startDate?: Date
    endDate?: Date
    page?: number
    pageSize?: number
  }) {
    const { userId, entityType, entityId, action, startDate, endDate, page = 1, pageSize = 20 } = options || {}

    const where: any = {}

    if (userId) {
      where.userId = userId
    }

    if (entityType) {
      where.entityType = entityType
    }

    if (entityId) {
      where.entityId = entityId
    }

    if (action) {
      where.action = action
    }

    if (startDate || endDate) {
      where.createdAt = {}

      if (startDate) {
        where.createdAt.gte = startDate
      }

      if (endDate) {
        where.createdAt.lte = endDate
      }
    }

    // Get total count
    const totalCount = await prisma.auditLog.count({ where })

    // Get audit logs
    const auditLogs = await prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    })

    return {
      success: true,
      auditLogs,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      },
    }
  }

  /**
   * Get audit log details
   */
  static async getAuditLogDetails(auditLogId: string) {
    const auditLog = await prisma.auditLog.findUnique({
      where: { id: auditLogId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    })

    if (!auditLog) {
      throw new Error(`Audit log with ID ${auditLogId} not found`)
    }

    return {
      success: true,
      auditLog,
    }
  }

  /**
   * Get entity audit history
   */
  static async getEntityAuditHistory(entityType: string, entityId: string) {
    const auditLogs = await prisma.auditLog.findMany({
      where: {
        entityType,
        entityId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return {
      success: true,
      auditLogs,
    }
  }

  /**
   * Get user audit history
   */
  static async getUserAuditHistory(userId: string) {
    const auditLogs = await prisma.auditLog.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return {
      success: true,
      auditLogs,
    }
  }
}

