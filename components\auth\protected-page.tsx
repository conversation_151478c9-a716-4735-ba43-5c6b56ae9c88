"use client"

import type React from "react"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import type { Role } from "@prisma/client"

interface ProtectedPageProps {
  children: React.ReactNode
  allowedRoles?: Role[]
}

export function ProtectedPage({ children, allowedRoles }: ProtectedPageProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/auth/login")
      return
    }

    if (allowedRoles && !allowedRoles.includes(session.user.role as Role)) {
      router.push("/dashboard")
    }
  }, [session, status, router, allowedRoles])

  if (status === "loading") {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  if (allowedRoles && !allowedRoles.includes(session.user.role as Role)) {
    return null
  }

  return <>{children}</>
}

