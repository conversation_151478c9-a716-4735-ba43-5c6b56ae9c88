"use client"

import { notFound, useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Printer, Download, RefreshCw } from "lucide-react"
import { OrderStatusWorkflow } from "@/components/orders/order-status-workflow"
import prisma from "@/lib/prisma"
import { formatDate, formatCurrency } from "@/lib/utils"

interface OrderPageProps {
  params: {
    id: string
  }
}

async function getOrder(id: string) {
  const order = await prisma.order.findUnique({
    where: { id },
    include: {
      customer: true,
      items: {
        include: {
          product: true,
          variant: true,
        },
      },
      statusHistory: {
        orderBy: {
          createdAt: "desc",
        },
      },
      returns: true,
    },
  })

  if (!order) {
    notFound()
  }

  return order
}

export default async function OrderPage({ params }: OrderPageProps) {
  const order = await getOrder(params.id)
  const router = useRouter()

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline">Pending</Badge>
      case "PROCESSING":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
            Processing
          </Badge>
        )
      case "AWAITING_PAYMENT":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            Awaiting Payment
          </Badge>
        )
      case "AWAITING_FULFILLMENT":
        return (
          <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200">
            Awaiting Fulfillment
          </Badge>
        )
      case "AWAITING_SHIPMENT":
        return (
          <Badge variant="outline" className="bg-cyan-100 text-cyan-800 border-cyan-200">
            Awaiting Shipment
          </Badge>
        )
      case "PARTIALLY_SHIPPED":
        return (
          <Badge variant="outline" className="bg-teal-100 text-teal-800 border-teal-200">
            Partially Shipped
          </Badge>
        )
      case "SHIPPED":
        return (
          <Badge variant="outline" className="bg-emerald-100 text-emerald-800 border-emerald-200">
            Shipped
          </Badge>
        )
      case "DELIVERED":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Delivered</Badge>
      case "COMPLETED":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>
      case "CANCELLED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            Cancelled
          </Badge>
        )
      case "ON_HOLD":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
            On Hold
          </Badge>
        )
      case "BACKORDERED":
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
            Backordered
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case "UNPAID":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            Unpaid
          </Badge>
        )
      case "PARTIALLY_PAID":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            Partially Paid
          </Badge>
        )
      case "PAID":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Paid</Badge>
      case "REFUNDED":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
            Refunded
          </Badge>
        )
      case "PARTIALLY_REFUNDED":
        return (
          <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200">
            Partially Refunded
          </Badge>
        )
      case "VOIDED":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            Voided
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => router.back()} className="flex items-center" asChild>
          <Link href="/dashboard/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>
        <div className="flex gap-2">
          <Button variant="outline">
            <Printer className="mr-2 h-4 w-4" /> Print
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" /> Export
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/dashboard/orders/${order.id}/return`}>
              <RefreshCw className="mr-2 h-4 w-4" /> Create Return
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex justify-between">
                <span>Order #{order.orderNumber}</span>
                {getStatusBadge(order.status)}
              </CardTitle>
              <CardDescription>Placed on {formatDate(order.createdAt)}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Customer</h3>
                  <p>
                    {order.customer.firstName} {order.customer.lastName}
                  </p>
                  {order.customer.email && <p>{order.customer.email}</p>}
                  {order.customer.phone && <p>{order.customer.phone}</p>}
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Payment</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <span>{getPaymentStatusBadge(order.paymentStatus)}</span>
                    </div>
                    {order.paymentMethod && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Method:</span>
                        <span>{order.paymentMethod.replace(/_/g, " ")}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-medium mb-4">Order Items</h3>
                <div className="space-y-4">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex justify-between items-center border-b pb-4">
                      <div>
                        <p className="font-medium">{item.product.name}</p>
                        {item.variant && <p className="text-sm text-muted-foreground">Variant: {item.variant.sku}</p>}
                        <p className="text-sm text-muted-foreground">
                          {formatCurrency(item.unitPrice)} x {item.quantity}
                        </p>
                      </div>
                      <p className="font-medium">{formatCurrency(item.total)}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-muted p-4 rounded-md">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>{formatCurrency(order.subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax</span>
                    <span>{formatCurrency(order.tax)}</span>
                  </div>
                  {order.shipping > 0 && (
                    <div className="flex justify-between">
                      <span>Shipping</span>
                      <span>{formatCurrency(order.shipping)}</span>
                    </div>
                  )}
                  {order.discount > 0 && (
                    <div className="flex justify-between">
                      <span>Discount</span>
                      <span>-{formatCurrency(order.discount)}</span>
                    </div>
                  )}
                  <Separator className="my-2" />
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span>{formatCurrency(order.total)}</span>
                  </div>
                </div>
              </div>

              {order.notes && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Notes</h3>
                  <p className="text-muted-foreground">{order.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <OrderStatusWorkflow order={order} />

          {order.returns.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Returns</CardTitle>
                <CardDescription>Returns associated with this order</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.returns.map((returnItem) => (
                    <div key={returnItem.id} className="flex justify-between items-center">
                      <div>
                        <Link href={`/dashboard/returns/${returnItem.id}`} className="font-medium hover:underline">
                          #{returnItem.returnNumber}
                        </Link>
                        <p className="text-sm text-muted-foreground">{formatDate(returnItem.createdAt)}</p>
                      </div>
                      {getStatusBadge(returnItem.status)}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

