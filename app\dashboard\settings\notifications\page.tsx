"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"

interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  lowStockAlerts: boolean
  newOrderAlerts: boolean
  orderStatusChanges: boolean
  inventoryUpdates: boolean
  dailySummary: boolean
}

export default function NotificationSettingsPage() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [settings, setSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    lowStockAlerts: true,
    newOrderAlerts: true,
    orderStatusChanges: true,
    inventoryUpdates: false,
    dailySummary: true,
  })

  const handleToggle = (key: keyof NotificationSettings) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }))
  }

  const handleSaveSettings = async () => {
    setIsLoading(true)

    try {
      await fetchApi("/api/user/notification-settings", {
        method: "PUT",
        body: JSON.stringify(settings),
      })

      toast({
        title: "Settings saved",
        description: "Your notification preferences have been updated",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save notification settings",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Notification Settings</h3>
        <p className="text-sm text-muted-foreground">Configure how and when you receive notifications</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Delivery Methods</CardTitle>
          <CardDescription>Choose how you want to receive notifications</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-notifications">Email Notifications</Label>
              <p className="text-sm text-muted-foreground">Receive notifications via email</p>
            </div>
            <Switch
              id="email-notifications"
              checked={settings.emailNotifications}
              onCheckedChange={() => handleToggle("emailNotifications")}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="push-notifications">Push Notifications</Label>
              <p className="text-sm text-muted-foreground">Receive notifications in the browser</p>
            </div>
            <Switch
              id="push-notifications"
              checked={settings.pushNotifications}
              onCheckedChange={() => handleToggle("pushNotifications")}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Notification Types</CardTitle>
          <CardDescription>Select which types of notifications you want to receive</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="low-stock-alerts">Low Stock Alerts</Label>
              <p className="text-sm text-muted-foreground">Get notified when products are running low</p>
            </div>
            <Switch
              id="low-stock-alerts"
              checked={settings.lowStockAlerts}
              onCheckedChange={() => handleToggle("lowStockAlerts")}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="new-order-alerts">New Order Alerts</Label>
              <p className="text-sm text-muted-foreground">Get notified when new orders are placed</p>
            </div>
            <Switch
              id="new-order-alerts"
              checked={settings.newOrderAlerts}
              onCheckedChange={() => handleToggle("newOrderAlerts")}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="order-status-changes">Order Status Changes</Label>
              <p className="text-sm text-muted-foreground">Get notified when order statuses change</p>
            </div>
            <Switch
              id="order-status-changes"
              checked={settings.orderStatusChanges}
              onCheckedChange={() => handleToggle("orderStatusChanges")}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="inventory-updates">Inventory Updates</Label>
              <p className="text-sm text-muted-foreground">Get notified about inventory changes</p>
            </div>
            <Switch
              id="inventory-updates"
              checked={settings.inventoryUpdates}
              onCheckedChange={() => handleToggle("inventoryUpdates")}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="daily-summary">Daily Summary</Label>
              <p className="text-sm text-muted-foreground">Receive a daily summary of activity</p>
            </div>
            <Switch
              id="daily-summary"
              checked={settings.dailySummary}
              onCheckedChange={() => handleToggle("dailySummary")}
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSaveSettings} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Settings"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

