import { Capacitor } from "@capacitor/core"
import {
  PushNotifications,
  type Token,
  type PushNotificationSchema,
  type ActionPerformed,
} from "@capacitor/push-notifications"
import { NativeStorage } from "../native/native-features"

// Types for our notification system
export type NotificationChannel = "inventory" | "orders" | "customers" | "system"
export type NotificationPriority = "high" | "default" | "low"

export interface NotificationPreferences {
  enabled: boolean
  channels: {
    [key in NotificationChannel]: boolean
  }
  quiet_hours: {
    enabled: boolean
    start: string // Format: "HH:MM"
    end: string // Format: "HH:MM"
  }
}

// Default notification preferences
const DEFAULT_PREFERENCES: NotificationPreferences = {
  enabled: true,
  channels: {
    inventory: true,
    orders: true,
    customers: true,
    system: true,
  },
  quiet_hours: {
    enabled: false,
    start: "22:00",
    end: "07:00",
  },
}

class PushNotificationService {
  private initialized = false
  private token: string | null = null
  private preferences: NotificationPreferences = DEFAULT_PREFERENCES
  private listeners: Map<string, Function[]> = new Map()

  constructor() {
    // Only load preferences on the client side
    if (typeof window !== "undefined") {
      this.loadPreferences()
    }
  }

  // Initialize push notifications
  async initialize(): Promise<boolean> {
    if (!Capacitor.isNativePlatform()) {
      console.log("Push notifications are only available on native platforms")
      return false
    }

    if (this.initialized) return true

    try {
      // Request permission
      const permissionStatus = await PushNotifications.requestPermissions()

      if (permissionStatus.receive !== "granted") {
        console.log("Push notification permission was denied")
        return false
      }

      // Register with the native layer
      await PushNotifications.register()

      // Setup listeners
      PushNotifications.addListener("registration", (token: Token) => {
        this.token = token.value
        this.saveToken(token.value)
        this.notifyListeners("tokenReceived", token.value)
      })

      PushNotifications.addListener("registrationError", (error: any) => {
        console.error("Registration error: ", error)
        this.notifyListeners("error", error)
      })

      PushNotifications.addListener("pushNotificationReceived", (notification: PushNotificationSchema) => {
        if (this.shouldProcessNotification(notification)) {
          this.notifyListeners("notificationReceived", notification)
        }
      })

      PushNotifications.addListener("pushNotificationActionPerformed", (action: ActionPerformed) => {
        this.notifyListeners("notificationAction", action)
      })

      this.initialized = true
      return true
    } catch (error) {
      console.error("Error initializing push notifications:", error)
      return false
    }
  }

  // Check if we should process a notification based on preferences
  private shouldProcessNotification(notification: PushNotificationSchema): boolean {
    if (!this.preferences.enabled) return false

    // Check channel
    const channel = (notification.data?.channel as NotificationChannel) || "system"
    if (!this.preferences.channels[channel]) return false

    // Check quiet hours
    if (this.preferences.quiet_hours.enabled) {
      const now = new Date()
      const currentHour = now.getHours()
      const currentMinute = now.getMinutes()
      const currentTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

      const startTime = this.preferences.quiet_hours.start
      const endTime = this.preferences.quiet_hours.end

      // Handle cases where quiet hours span midnight
      if (startTime > endTime) {
        if (currentTime >= startTime || currentTime < endTime) {
          return false
        }
      } else {
        if (currentTime >= startTime && currentTime < endTime) {
          return false
        }
      }
    }

    return true
  }

  // Save FCM token to server
  async saveToken(token: string): Promise<boolean> {
    try {
      const response = await fetch("/api/notifications/register-device", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token }),
      })

      return response.ok
    } catch (error) {
      console.error("Error saving token:", error)
      return false
    }
  }

  // Get current token
  getToken(): string | null {
    return this.token
  }

  // Load notification preferences from storage
  async loadPreferences(): Promise<NotificationPreferences> {
    try {
      const prefs = await NativeStorage.get("notification_preferences")
      if (prefs) {
        this.preferences = { ...DEFAULT_PREFERENCES, ...prefs }
      }
      return this.preferences
    } catch (error) {
      console.error("Error loading notification preferences:", error)
      return DEFAULT_PREFERENCES
    }
  }

  // Save notification preferences
  async savePreferences(preferences: NotificationPreferences): Promise<boolean> {
    try {
      this.preferences = preferences
      await NativeStorage.set("notification_preferences", preferences)
      return true
    } catch (error) {
      console.error("Error saving notification preferences:", error)
      return false
    }
  }

  // Get current preferences
  getPreferences(): NotificationPreferences {
    return this.preferences
  }

  // Update a specific channel preference
  async updateChannelPreference(channel: NotificationChannel, enabled: boolean): Promise<boolean> {
    try {
      this.preferences.channels[channel] = enabled
      await this.savePreferences(this.preferences)
      return true
    } catch (error) {
      console.error("Error updating channel preference:", error)
      return false
    }
  }

  // Update quiet hours settings
  async updateQuietHours(enabled: boolean, start?: string, end?: string): Promise<boolean> {
    try {
      this.preferences.quiet_hours.enabled = enabled
      if (start) this.preferences.quiet_hours.start = start
      if (end) this.preferences.quiet_hours.end = end
      await this.savePreferences(this.preferences)
      return true
    } catch (error) {
      console.error("Error updating quiet hours:", error)
      return false
    }
  }

  // Add event listener
  addEventListener(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)?.push(callback)
  }

  // Remove event listener
  removeEventListener(event: string, callback: Function): void {
    if (!this.listeners.has(event)) return

    const callbacks = this.listeners.get(event) || []
    const index = callbacks.indexOf(callback)

    if (index !== -1) {
      callbacks.splice(index, 1)
      this.listeners.set(event, callbacks)
    }
  }

  // Notify all listeners of an event
  private notifyListeners(event: string, data: any): void {
    if (!this.listeners.has(event)) return

    const callbacks = this.listeners.get(event) || []
    callbacks.forEach((callback) => {
      try {
        callback(data)
      } catch (error) {
        console.error(`Error in notification listener for event ${event}:`, error)
      }
    })
  }
}

// Create singleton instance
export const pushNotificationService = new PushNotificationService()

