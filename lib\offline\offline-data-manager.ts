import { openDB, type IDBPDatabase } from "idb"
import { v4 as uuidv4 } from "uuid"

// Define the database schema
interface StockSyncDB {
  products: {
    id: string
    name: string
    sku: string
    price: number
    quantity: number
    category: string
    lastModified: number
    isDeleted: boolean
    pendingSync: boolean
    syncStatus: "pending" | "synced" | "error"
    syncError?: string
    localId?: string
  }
  orders: {
    id: string
    orderNumber: string
    customerName: string
    total: number
    status: string
    items: Array<{
      productId: string
      quantity: number
      price: number
    }>
    createdAt: number
    lastModified: number
    isDeleted: boolean
    pendingSync: boolean
    syncStatus: "pending" | "synced" | "error"
    syncError?: string
    localId?: string
  }
  customers: {
    id: string
    name: string
    email: string
    phone: string
    address: string
    lastModified: number
    isDeleted: boolean
    pendingSync: boolean
    syncStatus: "pending" | "synced" | "error"
    syncError?: string
    localId?: string
  }
  suppliers: {
    id: string
    name: string
    email: string
    phone: string
    address: string
    lastModified: number
    isDeleted: boolean
    pendingSync: boolean
    syncStatus: "pending" | "synced" | "error"
    syncError?: string
    localId?: string
  }
  syncLog: {
    id: string
    timestamp: number
    action: string
    entity: string
    entityId: string
    status: "success" | "error"
    error?: string
  }
}

// Define the entity types
export type EntityType = "products" | "orders" | "customers" | "suppliers"

// Define the sync operation types
export type SyncOperation = "create" | "update" | "delete"

// Define the sync queue item
export interface SyncQueueItem {
  id: string
  entityType: EntityType
  entityId: string
  operation: SyncOperation
  data: any
  timestamp: number
  attempts: number
  lastAttempt?: number
  error?: string
}

class OfflineDataManager {
  private db: IDBPDatabase<StockSyncDB> | null = null
  private syncQueue: SyncQueueItem[] = []
  private isSyncing = false
  private syncInterval: NodeJS.Timeout | null = null
  private networkStatus: "online" | "offline" = "online"
  private listeners: Map<string, Function[]> = new Map()

  constructor() {
    // Only initialize on the client side
    if (typeof window !== "undefined") {
      this.initDatabase()
      this.setupNetworkListeners()
    }
  }

  // Initialize the IndexedDB database
  private async initDatabase(): Promise<void> {
    try {
      this.db = await openDB<StockSyncDB>("stocksync-db", 1, {
        upgrade(db) {
          // Create object stores if they don't exist
          if (!db.objectStoreNames.contains("products")) {
            const productStore = db.createObjectStore("products", { keyPath: "id" })
            productStore.createIndex("sku", "sku", { unique: true })
            productStore.createIndex("pendingSync", "pendingSync", { unique: false })
            productStore.createIndex("lastModified", "lastModified", { unique: false })
          }

          if (!db.objectStoreNames.contains("orders")) {
            const orderStore = db.createObjectStore("orders", { keyPath: "id" })
            orderStore.createIndex("orderNumber", "orderNumber", { unique: true })
            orderStore.createIndex("pendingSync", "pendingSync", { unique: false })
            orderStore.createIndex("lastModified", "lastModified", { unique: false })
          }

          if (!db.objectStoreNames.contains("customers")) {
            const customerStore = db.createObjectStore("customers", { keyPath: "id" })
            customerStore.createIndex("email", "email", { unique: true })
            customerStore.createIndex("pendingSync", "pendingSync", { unique: false })
            customerStore.createIndex("lastModified", "lastModified", { unique: false })
          }

          if (!db.objectStoreNames.contains("suppliers")) {
            const supplierStore = db.createObjectStore("suppliers", { keyPath: "id" })
            supplierStore.createIndex("email", "email", { unique: true })
            supplierStore.createIndex("pendingSync", "pendingSync", { unique: false })
            supplierStore.createIndex("lastModified", "lastModified", { unique: false })
          }

          if (!db.objectStoreNames.contains("syncLog")) {
            db.createObjectStore("syncLog", { keyPath: "id" })
          }
        },
      })

      console.log("IndexedDB initialized successfully")
      this.loadSyncQueue()
      this.startSyncInterval()
    } catch (error) {
      console.error("Error initializing IndexedDB:", error)
    }
  }

  // Set up network status listeners
  private setupNetworkListeners(): void {
    if (typeof window !== "undefined") {
      this.networkStatus = navigator.onLine ? "online" : "offline"

      window.addEventListener("online", () => {
        this.networkStatus = "online"
        this.notifyListeners("networkStatusChange", { status: "online" })
        this.syncData()
      })

      window.addEventListener("offline", () => {
        this.networkStatus = "offline"
        this.notifyListeners("networkStatusChange", { status: "offline" })
      })
    }
  }

  // Start the sync interval
  private startSyncInterval(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
    }

    // Attempt to sync every 30 seconds
    this.syncInterval = setInterval(() => {
      if (this.networkStatus === "online" && !this.isSyncing && this.syncQueue.length > 0) {
        this.syncData()
      }
    }, 30000)
  }

  // Load the sync queue from IndexedDB
  private async loadSyncQueue(): Promise<void> {
    if (!this.db) return

    try {
      // Get all entities with pendingSync = true
      // Get all records and filter manually to avoid IDBKeyRange issues
      const allProducts = await this.db.getAll("products")
      const allOrders = await this.db.getAll("orders")
      const allCustomers = await this.db.getAll("customers")
      const allSuppliers = await this.db.getAll("suppliers")

      // Filter for pending sync items
      const products = allProducts.filter(item => item.pendingSync === true)
      const orders = allOrders.filter(item => item.pendingSync === true)
      const customers = allCustomers.filter(item => item.pendingSync === true)
      const suppliers = allSuppliers.filter(item => item.pendingSync === true)

      // Clear the current queue
      this.syncQueue = []

      // Add products to the queue
      products.forEach((product) => {
        this.syncQueue.push({
          id: uuidv4(),
          entityType: "products",
          entityId: product.id,
          operation: product.localId ? "create" : "update",
          data: product,
          timestamp: Date.now(),
          attempts: 0,
        })
      })

      // Add orders to the queue
      orders.forEach((order) => {
        this.syncQueue.push({
          id: uuidv4(),
          entityType: "orders",
          entityId: order.id,
          operation: order.localId ? "create" : "update",
          data: order,
          timestamp: Date.now(),
          attempts: 0,
        })
      })

      // Add customers to the queue
      customers.forEach((customer) => {
        this.syncQueue.push({
          id: uuidv4(),
          entityType: "customers",
          entityId: customer.id,
          operation: customer.localId ? "create" : "update",
          data: customer,
          timestamp: Date.now(),
          attempts: 0,
        })
      })

      // Add suppliers to the queue
      suppliers.forEach((supplier) => {
        this.syncQueue.push({
          id: uuidv4(),
          entityType: "suppliers",
          entityId: supplier.id,
          operation: supplier.localId ? "create" : "update",
          data: supplier,
          timestamp: Date.now(),
          attempts: 0,
        })
      })

      console.log(`Loaded ${this.syncQueue.length} items into sync queue`)
    } catch (error) {
      console.error("Error loading sync queue:", error)
    }
  }

  // Sync data with the server
  private async syncData(): Promise<void> {
    if (this.isSyncing || this.syncQueue.length === 0 || this.networkStatus === "offline") {
      return
    }

    this.isSyncing = true
    this.notifyListeners("syncStart", null)

    try {
      // Sort the queue by timestamp (oldest first)
      this.syncQueue.sort((a, b) => a.timestamp - b.timestamp)

      // Process up to 10 items at a time
      const itemsToProcess = this.syncQueue.slice(0, 10)
      const successfulItems: SyncQueueItem[] = []
      const failedItems: SyncQueueItem[] = []

      for (const item of itemsToProcess) {
        try {
          // Update the attempt count
          item.attempts += 1
          item.lastAttempt = Date.now()

          // Process the item based on operation type
          let success = false

          switch (item.operation) {
            case "create":
              success = await this.syncCreateOperation(item)
              break
            case "update":
              success = await this.syncUpdateOperation(item)
              break
            case "delete":
              success = await this.syncDeleteOperation(item)
              break
          }

          if (success) {
            successfulItems.push(item)
          } else {
            failedItems.push(item)
          }
        } catch (error) {
          console.error(`Error syncing item ${item.id}:`, error)
          item.error = error instanceof Error ? error.message : "Unknown error"
          failedItems.push(item)
        }
      }

      // Remove successful items from the queue
      this.syncQueue = this.syncQueue.filter((item) => !successfulItems.some((si) => si.id === item.id))

      // Update failed items in the queue
      failedItems.forEach((item) => {
        const index = this.syncQueue.findIndex((qi) => qi.id === item.id)
        if (index !== -1) {
          this.syncQueue[index] = item
        }
      })

      // Log the sync results
      console.log(`Sync completed: ${successfulItems.length} successful, ${failedItems.length} failed`)
      this.notifyListeners("syncComplete", {
        successful: successfulItems.length,
        failed: failedItems.length,
        remaining: this.syncQueue.length,
      })
    } catch (error) {
      console.error("Error during sync process:", error)
      this.notifyListeners("syncError", { error })
    } finally {
      this.isSyncing = false
    }
  }

  // Sync a create operation
  private async syncCreateOperation(item: SyncQueueItem): Promise<boolean> {
    if (!this.db) return false

    try {
      // Prepare the data for the API
      const { localId, pendingSync, syncStatus, syncError, ...apiData } = item.data

      // Send the data to the API
      const response = await fetch(`/api/${item.entityType}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(apiData),
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`)
      }

      // Get the server-generated ID
      const result = await response.json()
      const serverId = result.id

      // Update the local record with the server ID
      const updatedData = {
        ...item.data,
        id: serverId,
        localId: undefined,
        pendingSync: false,
        syncStatus: "synced" as const,
        lastModified: Date.now(),
      }

      // If the entity had a temporary local ID, we need to update it
      if (localId) {
        // Get all records that might reference this entity
        // For example, if this is a product, update any orders that reference it
        if (item.entityType === "products") {
          const orders = await this.db.getAll("orders")
          for (const order of orders) {
            let updated = false
            for (const orderItem of order.items) {
              if (orderItem.productId === localId) {
                orderItem.productId = serverId
                updated = true
              }
            }

            if (updated) {
              order.lastModified = Date.now()
              await this.db.put("orders", order)
            }
          }
        }
      }

      // Update the record in IndexedDB
      await this.db.put(item.entityType, updatedData)

      // Log the successful sync
      await this.logSync("create", item.entityType, serverId, "success")

      return true
    } catch (error) {
      console.error(`Error syncing create operation for ${item.entityType} ${item.entityId}:`, error)

      // Log the failed sync
      await this.logSync(
        "create",
        item.entityType,
        item.entityId,
        "error",
        error instanceof Error ? error.message : "Unknown error",
      )

      // Update the entity with the error
      const entity = await this.db?.get(item.entityType, item.entityId)
      if (entity) {
        entity.syncStatus = "error"
        entity.syncError = error instanceof Error ? error.message : "Unknown error"
        await this.db?.put(item.entityType, entity)
      }

      return false
    }
  }

  // Sync an update operation
  private async syncUpdateOperation(item: SyncQueueItem): Promise<boolean> {
    if (!this.db) return false

    try {
      // Prepare the data for the API
      const { pendingSync, syncStatus, syncError, ...apiData } = item.data

      // Send the data to the API
      const response = await fetch(`/api/${item.entityType}/${item.entityId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(apiData),
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`)
      }

      // Update the local record
      const updatedData = {
        ...item.data,
        pendingSync: false,
        syncStatus: "synced" as const,
        syncError: undefined,
        lastModified: Date.now(),
      }

      // Update the record in IndexedDB
      await this.db.put(item.entityType, updatedData)

      // Log the successful sync
      await this.logSync("update", item.entityType, item.entityId, "success")

      return true
    } catch (error) {
      console.error(`Error syncing update operation for ${item.entityType} ${item.entityId}:`, error)

      // Log the failed sync
      await this.logSync(
        "update",
        item.entityType,
        item.entityId,
        "error",
        error instanceof Error ? error.message : "Unknown error",
      )

      // Update the entity with the error
      const entity = await this.db?.get(item.entityType, item.entityId)
      if (entity) {
        entity.syncStatus = "error"
        entity.syncError = error instanceof Error ? error.message : "Unknown error"
        await this.db?.put(item.entityType, entity)
      }

      return false
    }
  }

  // Sync a delete operation
  private async syncDeleteOperation(item: SyncQueueItem): Promise<boolean> {
    if (!this.db) return false

    try {
      // Send the delete request to the API
      const response = await fetch(`/api/${item.entityType}/${item.entityId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`)
      }

      // Delete the record from IndexedDB
      await this.db.delete(item.entityType, item.entityId)

      // Log the successful sync
      await this.logSync("delete", item.entityType, item.entityId, "success")

      return true
    } catch (error) {
      console.error(`Error syncing delete operation for ${item.entityType} ${item.entityId}:`, error)

      // Log the failed sync
      await this.logSync(
        "delete",
        item.entityType,
        item.entityId,
        "error",
        error instanceof Error ? error.message : "Unknown error",
      )

      return false
    }
  }

  // Log a sync operation
  private async logSync(
    action: string,
    entity: string,
    entityId: string,
    status: "success" | "error",
    error?: string,
  ): Promise<void> {
    if (!this.db) return

    try {
      await this.db.add("syncLog", {
        id: uuidv4(),
        timestamp: Date.now(),
        action,
        entity,
        entityId,
        status,
        error,
      })
    } catch (logError) {
      console.error("Error logging sync operation:", logError)
    }
  }

  // Add a listener for events
  addEventListener(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)?.push(callback)
  }

  // Remove a listener
  removeEventListener(event: string, callback: Function): void {
    if (!this.listeners.has(event)) return

    const callbacks = this.listeners.get(event) || []
    const index = callbacks.indexOf(callback)

    if (index !== -1) {
      callbacks.splice(index, 1)
      this.listeners.set(event, callbacks)
    }
  }

  // Notify all listeners of an event
  private notifyListeners(event: string, data: any): void {
    if (!this.listeners.has(event)) return

    const callbacks = this.listeners.get(event) || []
    callbacks.forEach((callback) => {
      try {
        callback(data)
      } catch (error) {
        console.error(`Error in listener for event ${event}:`, error)
      }
    })
  }

  // Public methods for data operations

  // Get all items of a specific entity type
  async getAll<T extends EntityType>(entityType: T): Promise<StockSyncDB[T][]> {
    if (!this.db) throw new Error("Database not initialized")

    try {
      return await this.db.getAll(entityType)
    } catch (error) {
      console.error(`Error getting all ${entityType}:`, error)
      throw error
    }
  }

  // Get a specific item by ID
  async getById<T extends EntityType>(entityType: T, id: string): Promise<StockSyncDB[T] | undefined> {
    if (!this.db) throw new Error("Database not initialized")

    try {
      return await this.db.get(entityType, id)
    } catch (error) {
      console.error(`Error getting ${entityType} with ID ${id}:`, error)
      throw error
    }
  }

  // Create a new item
  async create<T extends EntityType>(
    entityType: T,
    data: Omit<StockSyncDB[T], "id" | "lastModified" | "pendingSync" | "syncStatus">,
  ): Promise<StockSyncDB[T]> {
    if (!this.db) throw new Error("Database not initialized")

    try {
      // Generate a temporary local ID
      const localId = `local_${uuidv4()}`
      const id = this.networkStatus === "online" ? uuidv4() : localId

      // Prepare the item for storage
      const item = {
        ...data,
        id,
        lastModified: Date.now(),
        isDeleted: false,
        pendingSync: true,
        syncStatus: "pending" as const,
        ...(this.networkStatus === "offline" ? { localId } : {}),
      } as StockSyncDB[T]

      // Store the item in IndexedDB
      await this.db.add(entityType, item)

      // Add to sync queue if offline
      if (this.networkStatus === "offline") {
        this.syncQueue.push({
          id: uuidv4(),
          entityType,
          entityId: id,
          operation: "create",
          data: item,
          timestamp: Date.now(),
          attempts: 0,
        })
      } else {
        // Try to sync immediately if online
        this.syncData()
      }

      return item
    } catch (error) {
      console.error(`Error creating ${entityType}:`, error)
      throw error
    }
  }

  // Update an existing item
  async update<T extends EntityType>(
    entityType: T,
    id: string,
    data: Partial<StockSyncDB[T]>,
  ): Promise<StockSyncDB[T]> {
    if (!this.db) throw new Error("Database not initialized")

    try {
      // Get the existing item
      const existingItem = await this.db.get(entityType, id)
      if (!existingItem) {
        throw new Error(`${entityType} with ID ${id} not found`)
      }

      // Prepare the updated item
      const updatedItem = {
        ...existingItem,
        ...data,
        lastModified: Date.now(),
        pendingSync: true,
        syncStatus: "pending" as const,
        syncError: undefined,
      }

      // Store the updated item
      await this.db.put(entityType, updatedItem)

      // Add to sync queue
      this.syncQueue.push({
        id: uuidv4(),
        entityType,
        entityId: id,
        operation: "update",
        data: updatedItem,
        timestamp: Date.now(),
        attempts: 0,
      })

      // Try to sync immediately if online
      if (this.networkStatus === "online") {
        this.syncData()
      }

      return updatedItem
    } catch (error) {
      console.error(`Error updating ${entityType} with ID ${id}:`, error)
      throw error
    }
  }

  // Delete an item
  async delete<T extends EntityType>(entityType: T, id: string): Promise<boolean> {
    if (!this.db) throw new Error("Database not initialized")

    try {
      // Get the existing item
      const existingItem = await this.db.get(entityType, id)
      if (!existingItem) {
        throw new Error(`${entityType} with ID ${id} not found`)
      }

      // If the item has a localId, it hasn't been synced to the server yet
      if (existingItem.localId) {
        // We can just delete it locally
        await this.db.delete(entityType, id)
        return true
      }

      // Mark the item as deleted but don't remove it yet
      const updatedItem = {
        ...existingItem,
        isDeleted: true,
        lastModified: Date.now(),
        pendingSync: true,
        syncStatus: "pending" as const,
      }

      // Store the updated item
      await this.db.put(entityType, updatedItem)

      // Add to sync queue
      this.syncQueue.push({
        id: uuidv4(),
        entityType,
        entityId: id,
        operation: "delete",
        data: updatedItem,
        timestamp: Date.now(),
        attempts: 0,
      })

      // Try to sync immediately if online
      if (this.networkStatus === "online") {
        this.syncData()
      }

      return true
    } catch (error) {
      console.error(`Error deleting ${entityType} with ID ${id}:`, error)
      throw error
    }
  }

  // Force a sync
  async forceSync(): Promise<boolean> {
    if (this.networkStatus === "offline") {
      return false
    }

    await this.syncData()
    return true
  }

  // Get the current network status
  getNetworkStatus(): "online" | "offline" {
    return this.networkStatus
  }

  // Get the sync queue status
  getSyncQueueStatus(): { total: number; pending: number } {
    return {
      total: this.syncQueue.length,
      pending: this.syncQueue.filter((item) => item.attempts === 0).length,
    }
  }

  // Get the sync log
  async getSyncLog(limit = 50): Promise<any[]> {
    if (!this.db) throw new Error("Database not initialized")

    try {
      const logs = await this.db.getAll("syncLog")
      return logs.sort((a, b) => b.timestamp - a.timestamp).slice(0, limit)
    } catch (error) {
      console.error("Error getting sync log:", error)
      throw error
    }
  }
}

// Create singleton instance
export const offlineDataManager = new OfflineDataManager()

