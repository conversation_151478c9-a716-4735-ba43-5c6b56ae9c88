import { type Permission, PREDEFINED_ROLES } from "./permissions"
import type { Role, UserRole } from "./role-models"
import { db } from "../db"

export class RoleService {
  /**
   * Initialize the role system by creating predefined roles if they don't exist
   */
  static async initialize(): Promise<void> {
    const existingRoles = await db.roles.findMany()

    // Create predefined roles if they don't exist
    for (const [key, roleData] of Object.entries(PREDEFINED_ROLES)) {
      const roleExists = existingRoles.some((role) => role.name === roleData.name)

      if (!roleExists) {
        await db.roles.create({
          data: {
            id: key.toLowerCase(),
            name: roleData.name,
            description: roleData.description,
            permissions: roleData.permissions,
            isCustom: false,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        })
      }
    }
  }

  /**
   * Get all roles
   */
  static async getAllRoles(): Promise<Role[]> {
    return db.roles.findMany({
      orderBy: { name: "asc" },
    })
  }

  /**
   * Get a role by ID
   */
  static async getRoleById(id: string): Promise<Role | null> {
    return db.roles.findUnique({
      where: { id },
    })
  }

  /**
   * Create a new custom role
   */
  static async createRole(name: string, description: string, permissions: Permission[]): Promise<Role> {
    return db.roles.create({
      data: {
        name,
        description,
        permissions,
        isCustom: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })
  }

  /**
   * Update an existing role
   */
  static async updateRole(
    id: string,
    data: {
      name?: string
      description?: string
      permissions?: Permission[]
    },
  ): Promise<Role> {
    return db.roles.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    })
  }

  /**
   * Delete a custom role
   */
  static async deleteRole(id: string): Promise<void> {
    const role = await db.roles.findUnique({
      where: { id },
    })

    if (!role || !role.isCustom) {
      throw new Error("Cannot delete a predefined role")
    }

    // First, remove all user assignments to this role
    await db.userRoles.deleteMany({
      where: { roleId: id },
    })

    // Then delete the role
    await db.roles.delete({
      where: { id },
    })
  }

  /**
   * Assign a role to a user
   */
  static async assignRoleToUser(userId: string, roleId: string, assignedBy: string): Promise<UserRole> {
    // Check if the role exists
    const role = await db.roles.findUnique({
      where: { id: roleId },
    })

    if (!role) {
      throw new Error(`Role with ID ${roleId} not found`)
    }

    // Check if the user already has this role
    const existingAssignment = await db.userRoles.findFirst({
      where: {
        userId,
        roleId,
      },
    })

    if (existingAssignment) {
      return existingAssignment
    }

    // Assign the role to the user
    return db.userRoles.create({
      data: {
        userId,
        roleId,
        assignedAt: new Date(),
        assignedBy,
      },
    })
  }

  /**
   * Remove a role from a user
   */
  static async removeRoleFromUser(userId: string, roleId: string): Promise<void> {
    await db.userRoles.deleteMany({
      where: {
        userId,
        roleId,
      },
    })
  }

  /**
   * Get all roles assigned to a user
   */
  static async getUserRoles(userId: string): Promise<Role[]> {
    const userRoles = await db.userRoles.findMany({
      where: { userId },
      include: { role: true },
    })

    return userRoles.map((ur) => ur.role)
  }

  /**
   * Get all permissions for a user based on their roles
   */
  static async getUserPermissions(userId: string): Promise<Permission[]> {
    const roles = await this.getUserRoles(userId)

    // Combine all permissions from all roles and remove duplicates
    const allPermissions = roles.flatMap((role) => role.permissions)
    return [...new Set(allPermissions)]
  }
}

