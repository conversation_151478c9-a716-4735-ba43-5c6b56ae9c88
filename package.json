{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@asteasolutions/zod-to-openapi": "latest", "@auth/core": "latest", "@capacitor/app": "latest", "@capacitor/cli": "latest", "@capacitor/core": "latest", "@capacitor/device": "latest", "@capacitor/haptics": "latest", "@capacitor/local-notifications": "latest", "@capacitor/network": "latest", "@capacitor/push-notifications": "latest", "@capacitor/share": "latest", "@capacitor/status-bar": "latest", "@capacitor/toast": "latest", "@hookform/resolvers": "latest", "@next-auth/prisma-adapter": "latest", "@prisma/client": "latest", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "latest", "@tanstack/react-query-devtools": "latest", "@upstash/redis": "latest", "autoprefixer": "^10.4.20", "bcrypt": "^6.0.0", "bcryptjs": "latest", "chart.js": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "latest", "embla-carousel-react": "8.6.0", "firebase-admin": "latest", "fs": "latest", "http": "latest", "i18next": "latest", "idb": "latest", "immer": "latest", "input-otp": "1.4.2", "jspdf": "latest", "jspdf-autotable": "latest", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "latest", "next-i18next": "latest", "next-themes": "^0.4.4", "node-cron": "latest", "nodemailer": "latest", "otplib": "latest", "papaparse": "latest", "path": "latest", "prisma": "latest", "react": "^19", "react-chartjs-2": "latest", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-i18next": "latest", "react-intersection-observer": "latest", "react-native": "latest", "react-resizable-panels": "^3.0.2", "recharts": "latest", "socket.io": "latest", "socket.io-client": "latest", "sonner": "^2.0.5", "stream": "latest", "stripe": "latest", "swagger-ui-react": "latest", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "three": "latest", "url": "latest", "use-sync-external-store": "latest", "uuid": "latest", "vaul": "^1.1.2", "zlib": "latest", "zod": "latest", "zustand": "latest"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "latest", "typescript": "^5"}}