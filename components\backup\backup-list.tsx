"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Loader2, Download, Trash2, RotateCcw, Plus } from "lucide-react"
import { format, formatDistanceToNow } from "date-fns"
import { useToast } from "@/hooks/use-toast"
import { formatBytes } from "@/lib/utils"

type Backup = {
  id: string
  name: string
  description: string
  filePath: string
  size: number
  tables: string
  createdAt: string
  lastRestoredAt: string | null
}

export function BackupList() {
  const { toast } = useToast()
  const [backups, setBackups] = useState<Backup[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [restoring, setRestoring] = useState<string | null>(null)
  const [deleting, setDeleting] = useState<string | null>(null)
  const [showRestoreDialog, setShowRestoreDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedBackup, setSelectedBackup] = useState<Backup | null>(null)

  // Fetch backups
  const fetchBackups = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/backups")
      const data = await response.json()

      if (data.success) {
        setBackups(data.backups)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch backups",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch backups",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Create a new backup
  const createBackup = async () => {
    try {
      setCreating(true)
      const response = await fetch("/api/backups", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          description: "Manual backup",
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Backup created successfully",
        })
        fetchBackups()
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to create backup",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create backup",
        variant: "destructive",
      })
    } finally {
      setCreating(false)
    }
  }

  // Restore a backup
  const restoreBackup = async (id: string) => {
    try {
      setRestoring(id)
      const response = await fetch(`/api/backups/${id}`, {
        method: "POST",
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message || "Backup restored successfully",
        })
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to restore backup",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to restore backup",
        variant: "destructive",
      })
    } finally {
      setRestoring(null)
      setShowRestoreDialog(false)
    }
  }

  // Delete a backup
  const deleteBackup = async (id: string) => {
    try {
      setDeleting(id)
      const response = await fetch(`/api/backups/${id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message || "Backup deleted successfully",
        })
        fetchBackups()
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to delete backup",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete backup",
        variant: "destructive",
      })
    } finally {
      setDeleting(null)
      setShowDeleteDialog(false)
    }
  }

  // Download a backup
  const downloadBackup = (id: string) => {
    window.open(`/api/backups/${id}`, "_blank")
  }

  // Handle restore dialog
  const handleRestoreClick = (backup: Backup) => {
    setSelectedBackup(backup)
    setShowRestoreDialog(true)
  }

  // Handle delete dialog
  const handleDeleteClick = (backup: Backup) => {
    setSelectedBackup(backup)
    setShowDeleteDialog(true)
  }

  // Load backups on component mount
  useEffect(() => {
    fetchBackups()
  }, [])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Backups</CardTitle>
            <CardDescription>Manage your database backups</CardDescription>
          </div>
          <Button onClick={createBackup} disabled={creating}>
            {creating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                Create Backup
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : backups.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <p className="text-muted-foreground">No backups found</p>
            <p className="text-sm text-muted-foreground">Create your first backup to protect your data</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {backups.map((backup) => {
                  const createdAt = new Date(backup.createdAt)
                  const lastRestoredAt = backup.lastRestoredAt ? new Date(backup.lastRestoredAt) : null

                  return (
                    <TableRow key={backup.id}>
                      <TableCell className="font-medium">
                        <div>
                          <div>{backup.name}</div>
                          <div className="text-xs text-muted-foreground">{backup.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div>{format(createdAt, "PPP")}</div>
                          <div className="text-xs text-muted-foreground">
                            {formatDistanceToNow(createdAt, { addSuffix: true })}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatBytes(backup.size)}</TableCell>
                      <TableCell>
                        {lastRestoredAt ? (
                          <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700 hover:bg-green-50 hover:text-green-700"
                          >
                            Restored {formatDistanceToNow(lastRestoredAt, { addSuffix: true })}
                          </Badge>
                        ) : (
                          <Badge variant="outline">Never restored</Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => downloadBackup(backup.id)}
                            title="Download backup"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleRestoreClick(backup)}
                            disabled={!!restoring}
                            title="Restore from backup"
                          >
                            {restoring === backup.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <RotateCcw className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleDeleteClick(backup)}
                            disabled={!!deleting}
                            title="Delete backup"
                          >
                            {deleting === backup.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          {backups.length} {backups.length === 1 ? "backup" : "backups"} available
        </div>
        <Button variant="outline" onClick={fetchBackups} disabled={loading}>
          Refresh
        </Button>
      </CardFooter>

      {/* Restore Confirmation Dialog */}
      <AlertDialog open={showRestoreDialog} onOpenChange={setShowRestoreDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Restore Backup</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to restore this backup? This will replace all current data with the data from the
              backup. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => selectedBackup && restoreBackup(selectedBackup.id)}
              className="bg-primary"
            >
              {restoring ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Restoring...
                </>
              ) : (
                "Restore"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Backup</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this backup? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => selectedBackup && deleteBackup(selectedBackup.id)}
              className="bg-destructive"
            >
              {deleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}

