"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { DollarSign, UserCheck, TrendingUp } from "lucide-react"

interface CustomerAnalyticsProps {
  data: any
}

export function CustomerAnalytics({ data }: CustomerAnalyticsProps) {
  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No customer analytics data available</p>
      </div>
    )
  }

  const { acquisitionCost, lifetimeValue, retentionRate, atRiskCustomers, loyalCustomers, customerSegmentation } = data

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(value)
  }

  // Generate colors for pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"]

  // Prepare data for customer segmentation chart
  const segmentationData = [
    { name: "High Value Loyal", value: customerSegmentation.segments.highValueLoyal },
    { name: "High Value New", value: customerSegmentation.segments.highValueNew },
    { name: "Low Value Loyal", value: customerSegmentation.segments.lowValueLoyal },
    { name: "Low Value New", value: customerSegmentation.segments.lowValueNew },
  ]

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Lifetime Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(lifetimeValue.clv)}</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">Average value per customer</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Acquisition Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(acquisitionCost.cac)}</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">Cost to acquire a customer</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{retentionRate.retentionRate.toFixed(1)}%</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">Annual customer retention</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ROI Ratio</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(lifetimeValue.clv / acquisitionCost.cac).toFixed(1)}x</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">CLV to CAC ratio</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Segmentation */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Segmentation</CardTitle>
          <CardDescription>Distribution of customers by value and loyalty</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={segmentationData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {segmentationData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} customers`, "Count"]} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 text-sm text-muted-foreground">
            <p>Total Customers: {customerSegmentation.totalCustomers}</p>
            <p>Median Spending: {formatCurrency(customerSegmentation.medianSpending)}</p>
            <p>Median Order Count: {customerSegmentation.medianOrderCount}</p>
          </div>
        </CardContent>
      </Card>

      {/* Loyal Customers and At-Risk Customers */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Loyal Customers</CardTitle>
            <CardDescription>Top customers by total spend</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Orders</TableHead>
                    <TableHead>Total Spent</TableHead>
                    <TableHead>Avg. Order</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loyalCustomers.slice(0, 5).map((customer: any) => (
                    <TableRow key={customer.id}>
                      <TableCell className="font-medium">{customer.name}</TableCell>
                      <TableCell>{customer.totalOrders}</TableCell>
                      <TableCell>{formatCurrency(customer.totalSpent)}</TableCell>
                      <TableCell>{formatCurrency(customer.averageOrderValue)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>At-Risk Customers</CardTitle>
            <CardDescription>Customers who may churn</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Last Order</TableHead>
                    <TableHead>Days Since</TableHead>
                    <TableHead>Total Orders</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {atRiskCustomers.slice(0, 5).map((customer: any) => (
                    <TableRow key={customer.id}>
                      <TableCell className="font-medium">{customer.name}</TableCell>
                      <TableCell>
                        {customer.lastOrderDate ? new Date(customer.lastOrderDate).toLocaleDateString() : "N/A"}
                      </TableCell>
                      <TableCell>{customer.daysSinceLastOrder || "N/A"}</TableCell>
                      <TableCell>{customer.totalOrders}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Lifetime Value Components */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Lifetime Value Analysis</CardTitle>
          <CardDescription>Components that make up customer lifetime value</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={[
                  {
                    name: "Average Order Value",
                    value: lifetimeValue.averageOrderValue,
                  },
                  {
                    name: "Purchase Frequency",
                    value: lifetimeValue.averageOrdersPerCustomer,
                  },
                  {
                    name: "Customer Lifespan",
                    value: lifetimeValue.averageLifespan,
                  },
                  {
                    name: "Profit Margin",
                    value: lifetimeValue.profitMargin * 100,
                  },
                ]}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip
                  formatter={(value, name, props) => {
                    if (props.dataKey === "value" && props.payload.name === "Average Order Value") {
                      return [formatCurrency(value as number), "Value"]
                    }
                    if (props.dataKey === "value" && props.payload.name === "Profit Margin") {
                      return [`${value}%`, "Value"]
                    }
                    return [value, "Value"]
                  }}
                />
                <Legend />
                <Bar dataKey="value" fill="#8884d8" name="Value" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 text-sm">
            <p className="font-medium">
              CLV Formula: Average Order Value × Purchase Frequency × Customer Lifespan × Profit Margin
            </p>
            <p className="text-muted-foreground mt-1">
              {formatCurrency(lifetimeValue.averageOrderValue)} × {lifetimeValue.averageOrdersPerCustomer.toFixed(1)} ×{" "}
              {lifetimeValue.averageLifespan} years × {(lifetimeValue.profitMargin * 100).toFixed(0)}% ={" "}
              {formatCurrency(lifetimeValue.clv)}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

