"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import { mobileAnalytics } from "@/lib/analytics/mobile-analytics"

interface AnalyticsContextType {
  trackEvent: (eventName: string, properties?: Record<string, any>) => void
  trackScreen: (screenName: string, properties?: Record<string, any>) => void
  trackAction: (actionName: string, category: string, properties?: Record<string, any>) => void
  trackError: (errorMessage: string, errorType: string, properties?: Record<string, any>) => void
}

const AnalyticsContext = createContext<AnalyticsContextType | null>(null)

export function AnalyticsProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize analytics
  useEffect(() => {
    const initAnalytics = async () => {
      await mobileAnalytics.init()
      setIsInitialized(true)
    }

    initAnalytics()

    return () => {
      mobileAnalytics.destroy()
    }
  }, [])

  // Track page views
  useEffect(() => {
    if (!isInitialized) return

    // Get page path
    const path = pathname

    // Get query parameters
    const queryParams: Record<string, string> = {}
    searchParams.forEach((value, key) => {
      queryParams[key] = value
    })

    // Track screen view
    mobileAnalytics.trackScreen(path, {
      query_params: Object.keys(queryParams).length > 0 ? queryParams : undefined,
      timestamp: Date.now(),
    })
  }, [pathname, searchParams, isInitialized])

  // Create context value
  const contextValue: AnalyticsContextType = {
    trackEvent: (eventName, properties) => {
      mobileAnalytics.trackEvent(eventName, properties)
    },
    trackScreen: (screenName, properties) => {
      mobileAnalytics.trackScreen(screenName, properties)
    },
    trackAction: (actionName, category, properties) => {
      mobileAnalytics.trackAction(actionName, category, properties)
    },
    trackError: (errorMessage, errorType, properties) => {
      mobileAnalytics.trackError(errorMessage, errorType, properties)
    },
  }

  return <AnalyticsContext.Provider value={contextValue}>{children}</AnalyticsContext.Provider>
}

export function useAnalytics() {
  const context = useContext(AnalyticsContext)

  if (!context) {
    throw new Error("useAnalytics must be used within an AnalyticsProvider")
  }

  return context
}

