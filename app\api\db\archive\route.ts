import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { ArchivingService } from "@/lib/services/archiving-service"
import { ApiError } from "@/lib/api-error"
import { AuditService, AuditAction } from "@/lib/services/audit-service"

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to archive data
    if (session.user.role !== "ADMIN") {
      throw ApiError.forbidden("You do not have permission to archive data")
    }

    const body = await req.json()
    const { entityType, olderThan, deleteAfterArchive } = body

    if (!entityType) {
      throw ApiError.badRequest("No entity type provided")
    }

    if (!olderThan) {
      throw ApiError.badRequest("No date provided")
    }

    // Archive data
    const result = await ArchivingService.archiveOldData({
      entityType,
      olderThan: new Date(olderThan),
      userId: session.user.id,
      deleteAfterArchive,
    })

    // Log audit event
    await AuditService.log({
      action: AuditAction.ARCHIVE,
      entityType,
      userId: session.user.id,
      details: {
        olderThan,
        deleteAfterArchive,
        recordCount: result.recordCount,
      },
      ipAddress: req.headers.get("x-forwarded-for") || req.ip,
      userAgent: req.headers.get("user-agent"),
    })

    return NextResponse.json(result)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Archiving error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to view archives
    if (session.user.role !== "ADMIN" && session.user.role !== "MANAGER") {
      throw ApiError.forbidden("You do not have permission to view archives")
    }

    const searchParams = req.nextUrl.searchParams
    const entityType = searchParams.get("entityType")
    const status = searchParams.get("status")

    // Get archives
    const result = await ArchivingService.getArchives({
      entityType: entityType || undefined,
      status: status || undefined,
    })

    return NextResponse.json(result)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Get archives error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

