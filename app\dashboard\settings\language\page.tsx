"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { useLanguage } from "@/components/language-provider"

export default function LanguageSettingsPage() {
  const { language, setLanguage, t } = useLanguage()
  const { toast } = useToast()
  const [selectedLanguage, setSelectedLanguage] = useState(language)
  const [isLoading, setIsLoading] = useState(false)

  const languages = [
    { code: "en", name: t("settings.languageSettings.english") },
    { code: "es", name: t("settings.languageSettings.spanish") },
    { code: "fr", name: t("settings.languageSettings.french") },
    { code: "de", name: t("settings.languageSettings.german") },
    { code: "zh", name: t("settings.languageSettings.chinese") },
  ]

  const handleSaveSettings = async () => {
    setIsLoading(true)

    try {
      // Update language
      setLanguage(selectedLanguage as any)

      toast({
        title: t("settings.saveChanges"),
        description: t("settings.languageSettings.description"),
      })
    } catch (error) {
      toast({
        title: t("errors.somethingWentWrong"),
        description: t("errors.tryAgain"),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">{t("settings.languageSettings.title")}</h3>
        <p className="text-sm text-muted-foreground">{t("settings.languageSettings.description")}</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("settings.languageSettings.selectLanguage")}</CardTitle>
          <CardDescription>{t("settings.languageSettings.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup value={selectedLanguage} onValueChange={setSelectedLanguage} className="space-y-4">
            {languages.map((lang) => (
              <div key={lang.code} className="flex items-center space-x-2">
                <RadioGroupItem value={lang.code} id={`language-${lang.code}`} />
                <Label htmlFor={`language-${lang.code}`}>{lang.name}</Label>
              </div>
            ))}
          </RadioGroup>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSaveSettings} disabled={isLoading}>
            {isLoading ? t("settings.saving") : t("settings.saveChanges")}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

