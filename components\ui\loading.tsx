"use client"

import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { Skeleton, TableSkeleton, CardSkeleton } from "./design-system"

interface LoadingProps {
  variant?: "spinner" | "skeleton" | "table" | "card"
  rows?: number
  className?: string
  text?: string
}

export function Loading({ variant = "spinner", rows = 5, className, text = "Loading..." }: LoadingProps) {
  if (variant === "spinner") {
    return (
      <div
        className={cn("flex flex-col items-center justify-center space-y-2 p-8", className)}
        role="status"
        aria-live="polite"
      >
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        {text && <p className="text-sm text-muted-foreground">{text}</p>}
      </div>
    )
  }

  if (variant === "table") {
    return <TableSkeleton rows={rows} />
  }

  if (variant === "card") {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: rows }).map((_, i) => (
          <CardSkeleton key={i} />
        ))}
      </div>
    )
  }

  // Default skeleton
  return (
    <div className={cn("space-y-4", className)} role="status" aria-live="polite">
      <Skeleton className="h-8 w-full max-w-sm" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-3/4" />
    </div>
  )
}

