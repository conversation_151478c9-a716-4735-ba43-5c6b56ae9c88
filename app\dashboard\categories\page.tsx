import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Edit, Trash2 } from "lucide-react"

export default function CategoriesPage() {
  const categories = [
    {
      id: "CAT001",
      name: "Electronics",
      slug: "electronics",
      productCount: 42,
      createdAt: "2023-05-12T10:30:00Z",
    },
    {
      id: "CAT002",
      name: "Food & Beverage",
      slug: "food-beverage",
      productCount: 28,
      createdAt: "2023-05-15T14:20:00Z",
    },
    {
      id: "CAT003",
      name: "Apparel",
      slug: "apparel",
      productCount: 56,
      createdAt: "2023-06-02T09:15:00Z",
    },
    {
      id: "CAT004",
      name: "Stationery",
      slug: "stationery",
      productCount: 19,
      createdAt: "2023-06-10T11:45:00Z",
    },
    {
      id: "CAT005",
      name: "Sports",
      slug: "sports",
      productCount: 31,
      createdAt: "2023-07-05T16:30:00Z",
    },
    {
      id: "CAT006",
      name: "Home",
      slug: "home",
      productCount: 47,
      createdAt: "2023-07-20T13:10:00Z",
    },
    {
      id: "CAT007",
      name: "Beauty",
      slug: "beauty",
      productCount: 23,
      createdAt: "2023-08-03T10:00:00Z",
    },
    {
      id: "CAT008",
      name: "Toys",
      slug: "toys",
      productCount: 15,
      createdAt: "2023-08-15T14:45:00Z",
    },
    {
      id: "CAT009",
      name: "Books",
      slug: "books",
      productCount: 38,
      createdAt: "2023-09-01T09:30:00Z",
    },
    {
      id: "CAT010",
      name: "Automotive",
      slug: "automotive",
      productCount: 12,
      createdAt: "2023-09-10T15:20:00Z",
    },
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Categories</h2>
        <Button>
          <Plus className="mr-2 h-4 w-4" /> Add Category
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input type="search" placeholder="Search categories..." className="w-full pl-8 md:w-[300px]" />
        </div>
        <Button variant="outline">Export</Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead>Products</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categories.map((category) => (
              <TableRow key={category.id}>
                <TableCell className="font-medium">{category.name}</TableCell>
                <TableCell>{category.slug}</TableCell>
                <TableCell>
                  <Badge variant="outline">{category.productCount}</Badge>
                </TableCell>
                <TableCell>{new Date(category.createdAt).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

