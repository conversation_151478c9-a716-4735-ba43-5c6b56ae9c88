import { z } from "zod"

// Product schema
export const productSchema = z.object({
  name: z.string().min(1, "Name is required").max(100),
  sku: z.string().min(1, "SKU is required").max(50),
  description: z.string().optional(),
  categoryId: z.string().min(1, "Category is required"),
  supplierId: z.string().min(1, "Supplier is required"),
  costPrice: z.coerce.number().min(0, "Cost price must be positive"),
  sellingPrice: z.coerce.number().min(0, "Selling price must be positive"),
  quantity: z.coerce.number().int().min(0, "Quantity must be a positive integer"),
  reorderLevel: z.coerce.number().int().min(0, "Reorder level must be a positive integer"),
  imageUrl: z.string().optional(),
})

// Order schema
export const orderSchema = z.object({
  customerId: z.string().min(1, "Customer is required"),
  items: z
    .array(
      z.object({
        productId: z.string().min(1, "Product is required"),
        quantity: z.coerce.number().int().min(1, "Quantity must be at least 1"),
        price: z.coerce.number().min(0, "Price must be positive"),
      }),
    )
    .min(1, "At least one item is required"),
  status: z.enum(["PENDING", "COMPLETED", "CANCELLED"]),
  paymentStatus: z.enum(["PAID", "UNPAID", "PARTIAL"]),
  notes: z.string().optional(),
})

// Customer schema
export const customerSchema = z.object({
  name: z.string().min(1, "Name is required").max(100),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  phone: z.string().optional(),
  address: z.string().optional(),
})

// Category schema
export const categorySchema = z.object({
  name: z.string().min(1, "Name is required").max(50),
  description: z.string().optional(),
})

// Supplier schema
export const supplierSchema = z.object({
  name: z.string().min(1, "Name is required").max(100),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  phone: z.string().optional(),
  address: z.string().optional(),
  contactPerson: z.string().optional(),
})

// User settings schema
export const userSettingsSchema = z.object({
  businessName: z.string().min(1, "Business name is required"),
  currency: z.string().min(1, "Currency is required"),
  dateFormat: z.string().min(1, "Date format is required"),
  timezone: z.string().min(1, "Timezone is required"),
  taxRate: z.coerce.number().min(0, "Tax rate must be positive").max(100, "Tax rate cannot exceed 100%"),
  lowStockThreshold: z.coerce.number().int().min(0, "Low stock threshold must be a positive integer"),
  notificationSettings: z.object({
    emailNotifications: z.boolean().default(true),
    stockAlerts: z.boolean().default(true),
    orderUpdates: z.boolean().default(true),
  }),
})

// Backup settings schema
export const backupSettingsSchema = z.object({
  autoBackup: z.boolean().default(true),
  backupFrequency: z.enum(["DAILY", "WEEKLY", "MONTHLY"]),
  backupTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
  retentionDays: z.coerce.number().int().min(1, "Retention days must be at least 1"),
  includeImages: z.boolean().default(true),
  backupLocation: z.string().min(1, "Backup location is required"),
})

