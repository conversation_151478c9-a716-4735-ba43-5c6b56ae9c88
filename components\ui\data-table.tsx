"use client"

import type React from "react"
import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Search, SortAsc, SortDesc } from "lucide-react"
import { cn } from "@/lib/utils"
import { useAccessibility } from "@/providers/accessibility-provider"
import { EmptyState } from "./design-system"
import { Loading } from "./loading"

export interface Column<T> {
  id: string
  header: string
  accessorKey?: keyof T
  cell?: (item: T) => React.ReactNode
  sortable?: boolean
}

interface DataTableProps<T> {
  columns: Column<T>[]
  data: T[]
  isLoading?: boolean
  emptyState?: {
    title: string
    description?: string
    action?: React.ReactNode
  }
  pagination?: {
    pageIndex: number
    pageSize: number
    pageCount: number
    onPageChange: (page: number) => void
  }
  sorting?: {
    sortBy: string | null
    sortDirection: "asc" | "desc" | null
    onSortChange: (column: string, direction: "asc" | "desc") => void
  }
  filtering?: {
    filter: string
    onFilterChange: (value: string) => void
  }
  selection?: {
    selectedRows: Record<string, boolean>
    onSelectRow: (id: string, selected: boolean) => void
    onSelectAll: (selected: boolean) => void
    getRowId: (row: T) => string
  }
  onRowClick?: (row: T) => void
  className?: string
}

export function DataTable<T>({
  columns,
  data,
  isLoading = false,
  emptyState,
  pagination,
  sorting,
  filtering,
  selection,
  onRowClick,
  className,
}: DataTableProps<T>) {
  const { announceMessage } = useAccessibility()
  const [focusedRowIndex, setFocusedRowIndex] = useState<number | null>(null)

  // Handle keyboard navigation within the table
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTableRowElement>, rowIndex: number) => {
    if (e.key === "ArrowDown") {
      e.preventDefault()
      const nextIndex = Math.min(rowIndex + 1, data.length - 1)
      setFocusedRowIndex(nextIndex)
      document.getElementById(`table-row-${nextIndex}`)?.focus()
    } else if (e.key === "ArrowUp") {
      e.preventDefault()
      const prevIndex = Math.max(rowIndex - 1, 0)
      setFocusedRowIndex(prevIndex)
      document.getElementById(`table-row-${prevIndex}`)?.focus()
    } else if (e.key === "Enter" || e.key === " ") {
      e.preventDefault()
      if (onRowClick && rowIndex < data.length) {
        onRowClick(data[rowIndex])
      }
    }
  }

  // Handle sort change
  const handleSort = (column: Column<T>) => {
    if (!sorting || !column.sortable) return

    const isCurrentColumn = sorting.sortBy === column.id
    const newDirection = isCurrentColumn && sorting.sortDirection === "asc" ? "desc" : "asc"

    sorting.onSortChange(column.id, newDirection)
    announceMessage(
      `Table sorted by ${column.header} in ${newDirection === "asc" ? "ascending" : "descending"} order`,
      "polite",
    )
  }

  // Render loading state
  if (isLoading) {
    return <Loading variant="table" />
  }

  // Render empty state
  if (data.length === 0 && emptyState) {
    return <EmptyState title={emptyState.title} description={emptyState.description} action={emptyState.action} />
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Filter */}
      {filtering && (
        <div className="flex items-center">
          <div className="relative flex-1 md:max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              value={filtering.filter}
              onChange={(e) => filtering.onFilterChange(e.target.value)}
              className="pl-8"
              aria-label="Filter table"
            />
          </div>
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {selection && (
                <TableHead className="w-[40px]">
                  <div className="flex items-center justify-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      checked={data.length > 0 && data.every((row) => selection.selectedRows[selection.getRowId(row)])}
                      onChange={(e) => selection.onSelectAll(e.target.checked)}
                      aria-label="Select all rows"
                    />
                  </div>
                </TableHead>
              )}
              {columns.map((column) => (
                <TableHead
                  key={column.id}
                  className={cn({
                    "cursor-pointer select-none": column.sortable && sorting,
                  })}
                  onClick={() => column.sortable && sorting && handleSort(column)}
                  aria-sort={
                    sorting?.sortBy === column.id
                      ? sorting.sortDirection === "asc"
                        ? "ascending"
                        : "descending"
                      : undefined
                  }
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.header}</span>
                    {column.sortable && sorting && (
                      <span className="inline-flex">
                        {sorting.sortBy === column.id ? (
                          sorting.sortDirection === "asc" ? (
                            <SortAsc className="h-4 w-4" />
                          ) : (
                            <SortDesc className="h-4 w-4" />
                          )
                        ) : (
                          <div className="h-4 w-4" />
                        )}
                      </span>
                    )}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, rowIndex) => (
              <TableRow
                key={selection ? selection.getRowId(row) : rowIndex}
                id={`table-row-${rowIndex}`}
                tabIndex={0}
                onKeyDown={(e) => handleKeyDown(e, rowIndex)}
                onClick={() => onRowClick && onRowClick(row)}
                className={cn({
                  "cursor-pointer hover:bg-muted/50": !!onRowClick,
                  "bg-muted/50": focusedRowIndex === rowIndex,
                })}
                aria-selected={selection ? selection.selectedRows[selection.getRowId(row)] : undefined}
              >
                {selection && (
                  <TableCell className="w-[40px]">
                    <div className="flex items-center justify-center" onClick={(e) => e.stopPropagation()}>
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        checked={selection.selectedRows[selection.getRowId(row)] || false}
                        onChange={(e) => selection.onSelectRow(selection.getRowId(row), e.target.checked)}
                        aria-label={`Select row ${rowIndex + 1}`}
                      />
                    </div>
                  </TableCell>
                )}
                {columns.map((column) => (
                  <TableCell key={column.id}>
                    {column.cell
                      ? column.cell(row)
                      : column.accessorKey
                        ? (row[column.accessorKey] as React.ReactNode)
                        : null}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && pagination.pageCount > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Page {pagination.pageIndex + 1} of {pagination.pageCount}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(0)}
              disabled={pagination.pageIndex === 0}
              aria-label="Go to first page"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.pageIndex - 1)}
              disabled={pagination.pageIndex === 0}
              aria-label="Go to previous page"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.pageIndex + 1)}
              disabled={pagination.pageIndex === pagination.pageCount - 1}
              aria-label="Go to next page"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.pageCount - 1)}
              disabled={pagination.pageIndex === pagination.pageCount - 1}
              aria-label="Go to last page"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

