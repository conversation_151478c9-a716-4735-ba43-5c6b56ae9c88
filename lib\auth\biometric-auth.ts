import { Capacitor } from "@capacitor/core"
import { BiometricAuth } from "@capacitor-community/biometric-auth"
import { NativeStorage } from "@/lib/native/native-features"

// Constants
const BIOMETRIC_ENABLED_KEY = "biometric_auth_enabled"
const BIOMETRIC_TOKEN_KEY = "biometric_auth_token"

export interface BiometricAuthResult {
  success: boolean
  error?: string
  token?: string
}

export const BiometricAuthService = {
  /**
   * Check if biometric authentication is available on the device
   */
  async isAvailable(): Promise<boolean> {
    if (!Capacitor.isNativePlatform()) {
      return false
    }

    try {
      const result = await BiometricAuth.isAvailable()
      return result.has
    } catch (error) {
      console.error("Error checking biometric availability:", error)
      return false
    }
  },

  /**
   * Check if biometric authentication is enabled by the user
   */
  async isEnabled(): Promise<boolean> {
    const enabled = await NativeStorage.get(BIOMETRIC_ENABLED_KEY)
    return enabled === true
  },

  /**
   * Enable biometric authentication for the user
   * @param authToken The authentication token to store
   */
  async enable(authToken: string): Promise<BiometricAuthResult> {
    if (!Capacitor.isNativePlatform()) {
      return { success: false, error: "Biometric authentication is only available on native platforms" }
    }

    try {
      // Check if biometrics are available
      const available = await this.isAvailable()
      if (!available) {
        return { success: false, error: "Biometric authentication is not available on this device" }
      }

      // Verify biometrics before enabling
      const result = await BiometricAuth.verify({
        reason: "Enable biometric login for StockSync",
        title: "Biometric Login",
        subtitle: "Use your fingerprint or face to enable biometric login",
        cancelButtonTitle: "Cancel",
      })

      if (result.verified) {
        // Store the auth token securely
        await NativeStorage.set(BIOMETRIC_TOKEN_KEY, authToken)
        await NativeStorage.set(BIOMETRIC_ENABLED_KEY, true)

        return { success: true }
      } else {
        return { success: false, error: "Biometric verification failed" }
      }
    } catch (error: any) {
      console.error("Error enabling biometric auth:", error)
      return {
        success: false,
        error: error.message || "Failed to enable biometric authentication",
      }
    }
  },

  /**
   * Disable biometric authentication for the user
   */
  async disable(): Promise<void> {
    await NativeStorage.remove(BIOMETRIC_TOKEN_KEY)
    await NativeStorage.set(BIOMETRIC_ENABLED_KEY, false)
  },

  /**
   * Authenticate the user using biometrics
   */
  async authenticate(): Promise<BiometricAuthResult> {
    if (!Capacitor.isNativePlatform()) {
      return { success: false, error: "Biometric authentication is only available on native platforms" }
    }

    try {
      // Check if biometrics are enabled
      const enabled = await this.isEnabled()
      if (!enabled) {
        return { success: false, error: "Biometric authentication is not enabled" }
      }

      // Verify biometrics
      const result = await BiometricAuth.verify({
        reason: "Log in to StockSync",
        title: "Biometric Login",
        subtitle: "Use your fingerprint or face to log in",
        cancelButtonTitle: "Use Password Instead",
      })

      if (result.verified) {
        // Retrieve the stored auth token
        const token = await NativeStorage.get(BIOMETRIC_TOKEN_KEY)

        if (!token) {
          return { success: false, error: "Authentication token not found" }
        }

        return { success: true, token }
      } else {
        return { success: false, error: "Biometric verification failed" }
      }
    } catch (error: any) {
      console.error("Error during biometric auth:", error)

      // Handle specific error cases
      if (error.message?.includes("canceled")) {
        return { success: false, error: "Authentication canceled" }
      }

      return {
        success: false,
        error: error.message || "Failed to authenticate with biometrics",
      }
    }
  },
}

