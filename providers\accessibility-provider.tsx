"use client"

import React, { createContext, useContext, useRef, useState } from "react"

interface AccessibilityContextType {
  focusRef: React.RefObject<HTMLElement>
  setFocus: (id: string) => void
  announceMessage: (message: string, politeness?: "polite" | "assertive") => void
  trapFocus: (isTrapped: boolean) => void
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined)

export function AccessibilityProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const focusRef = useRef<HTMLElement>(null)
  const [announcements, setAnnouncements] = useState<
    { message: string; politeness: "polite" | "assertive"; id: number }[]
  >([])
  const [isFocusTrapped, setIsFocusTrapped] = useState(false)
  const announcementIdRef = useRef(0)

  const setFocus = (id: string) => {
    const element = document.getElementById(id)
    if (element) {
      element.focus()
      focusRef.current = element as HTMLElement
    }
  }

  const announceMessage = (message: string, politeness: "polite" | "assertive" = "polite") => {
    const id = announcementIdRef.current++
    setAnnouncements((prev) => [...prev, { message, politeness, id }])

    // Remove announcement after it's been read (5 seconds)
    setTimeout(() => {
      setAnnouncements((prev) => prev.filter((a) => a.id !== id))
    }, 5000)
  }

  const trapFocus = (isTrapped: boolean) => {
    setIsFocusTrapped(isTrapped)
  }

  // Handle keyboard navigation
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Skip if focus trapping is not active
      if (!isFocusTrapped) return

      // Handle Tab key for focus trapping
      if (e.key === "Tab") {
        if (!focusRef.current) return

        const focusableElements = focusRef.current.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
        )

        if (focusableElements.length === 0) return

        const firstElement = focusableElements[0] as HTMLElement
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault()
          lastElement.focus()
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault()
          firstElement.focus()
        }
      }

      // Handle Escape key to exit modals, dropdowns, etc.
      if (e.key === "Escape" && isFocusTrapped) {
        setIsFocusTrapped(false)
        announceMessage("Dialog closed", "polite")
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [isFocusTrapped])

  return (
    <AccessibilityContext.Provider value={{ focusRef, setFocus, announceMessage, trapFocus }}>
      {children}
      {/* Screen reader announcements */}
      <div className="sr-only" aria-live="polite" role="status">
        {announcements
          .filter((a) => a.politeness === "polite")
          .map((a) => (
            <div key={a.id}>{a.message}</div>
          ))}
      </div>
      <div className="sr-only" aria-live="assertive" role="alert">
        {announcements
          .filter((a) => a.politeness === "assertive")
          .map((a) => (
            <div key={a.id}>{a.message}</div>
          ))}
      </div>
    </AccessibilityContext.Provider>
  )
}

export function useAccessibility() {
  const context = useContext(AccessibilityContext)
  if (!context) {
    throw new Error("useAccessibility must be used within an AccessibilityProvider")
  }
  return context
}

