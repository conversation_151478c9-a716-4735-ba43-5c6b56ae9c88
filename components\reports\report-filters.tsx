"use client"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { useCachedData } from "@/hooks/use-cached-data"
import { fetchApi } from "@/lib/api-client"

interface ReportFiltersProps {
  reportType: "sales" | "inventory" | "customers"
  filters: {
    category: string
    store: string
    product: string
    groupBy: string
  }
  onFiltersChange: (filters: any) => void
}

export function ReportFilters({ reportType, filters, onFiltersChange }: ReportFiltersProps) {
  // Fetch categories
  const { data: categories } = useCachedData<{ id: string; name: string }[]>({
    key: "categories",
    fetcher: () => fetchApi("/api/categories"),
  })

  // Fetch stores
  const { data: stores } = useCachedData<{ id: string; name: string }[]>({
    key: "stores",
    fetcher: () => fetchApi("/api/stores"),
  })

  // Fetch products (only when needed)
  const { data: products } = useCachedData<{ id: string; name: string }[]>({
    key: "products",
    fetcher: () => fetchApi("/api/products?fields=id,name"),
  })

  const handleFilterChange = (key: string, value: string) => {
    onFiltersChange({ ...filters, [key]: value })
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Group By Filter */}
          <div className="space-y-2">
            <Label htmlFor="groupBy">Group By</Label>
            <Select value={filters.groupBy} onValueChange={(value) => handleFilterChange("groupBy", value)}>
              <SelectTrigger id="groupBy">
                <SelectValue placeholder="Select grouping" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Day</SelectItem>
                <SelectItem value="week">Week</SelectItem>
                <SelectItem value="month">Month</SelectItem>
                {reportType === "inventory" && <SelectItem value="category">Category</SelectItem>}
                {reportType === "customers" && <SelectItem value="customer">Customer</SelectItem>}
              </SelectContent>
            </Select>
          </div>

          {/* Category Filter */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={filters.category} onValueChange={(value) => handleFilterChange("category", value)}>
              <SelectTrigger id="category">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories?.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Store Filter */}
          <div className="space-y-2">
            <Label htmlFor="store">Store</Label>
            <Select value={filters.store} onValueChange={(value) => handleFilterChange("store", value)}>
              <SelectTrigger id="store">
                <SelectValue placeholder="Select store" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Stores</SelectItem>
                {stores?.map((store) => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Product Filter (only for sales and inventory reports) */}
          {reportType !== "customers" && (
            <div className="space-y-2">
              <Label htmlFor="product">Product</Label>
              <Select value={filters.product} onValueChange={(value) => handleFilterChange("product", value)}>
                <SelectTrigger id="product">
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Products</SelectItem>
                  {products?.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

