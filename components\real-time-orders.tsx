"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useSocket } from "@/components/socket-provider"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"
import type { SocketPayload } from "@/lib/socket"

interface Order {
  id: string
  orderNumber: string
  status: string
  total: number
  customer?: {
    name: string
  }
}

interface RealTimeOrdersProps {
  orderId?: string
  showNotifications?: boolean
  onOrderUpdate?: (order: Order) => void
  children?: React.ReactNode
}

export function RealTimeOrders({ orderId, showNotifications = true, onOrderUpdate, children }: RealTimeOrdersProps) {
  const { subscribe } = useSocket()
  const { toast } = useToast()
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  useEffect(() => {
    // Subscribe to order created events
    const unsubscribeCreated = subscribe("order_created", (payload: SocketPayload) => {
      // If we're watching a specific order, only process updates for that order
      if (orderId && payload.data.id !== orderId) {
        return
      }

      setLastUpdate(new Date())

      // Show notification if enabled
      if (showNotifications) {
        toast({
          title: "New Order",
          description: `Order #${payload.data.orderNumber} created for $${payload.data.total.toFixed(2)}`,
        })
      }

      // Call the callback if provided
      if (onOrderUpdate) {
        onOrderUpdate(payload.data)
      }
    })

    // Subscribe to order updated events
    const unsubscribeUpdated = subscribe("order_updated", (payload: SocketPayload) => {
      // If we're watching a specific order, only process updates for that order
      if (orderId && payload.data.id !== orderId) {
        return
      }

      setLastUpdate(new Date())

      // Show notification if enabled
      if (showNotifications) {
        toast({
          title: "Order Updated",
          description: `Order #${payload.data.orderNumber} status: ${payload.data.status}`,
        })
      }

      // Call the callback if provided
      if (onOrderUpdate) {
        onOrderUpdate(payload.data)
      }
    })

    // Subscribe to order completed events
    const unsubscribeCompleted = subscribe("order_completed", (payload: SocketPayload) => {
      // If we're watching a specific order, only process updates for that order
      if (orderId && payload.data.id !== orderId) {
        return
      }

      setLastUpdate(new Date())

      // Show notification if enabled
      if (showNotifications) {
        toast({
          title: "Order Completed",
          description: `Order #${payload.data.orderNumber} has been completed`,
          variant: "success",
        })
      }

      // Call the callback if provided
      if (onOrderUpdate) {
        onOrderUpdate(payload.data)
      }
    })

    // Clean up subscriptions
    return () => {
      unsubscribeCreated()
      unsubscribeUpdated()
      unsubscribeCompleted()
    }
  }, [orderId, showNotifications, subscribe, toast, onOrderUpdate])

  // If this is just a wrapper component, render children
  if (children) {
    return <>{children}</>
  }

  // Otherwise render a status indicator
  return (
    <div className="flex items-center gap-2">
      <Badge variant="outline" className="text-xs">
        {lastUpdate ? `Last updated: ${lastUpdate.toLocaleTimeString()}` : "Waiting for updates..."}
      </Badge>
    </div>
  )
}

