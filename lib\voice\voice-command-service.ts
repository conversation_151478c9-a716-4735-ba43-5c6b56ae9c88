class VoiceCommandService {
  private recognition: SpeechRecognition | null = null
  private isListening = false
  private commands: Map<string, Function> = new Map()
  private commandPatterns: Map<RegExp, Function> = new Map()
  private onListeningChange: ((isListening: boolean) => void) | null = null
  private onResult: ((result: string) => void) | null = null
  private onError: ((error: string) => void) | null = null
  private continuousMode = false
  private language = "en-US"

  constructor() {
    this.initSpeechRecognition()
  }

  // Initialize speech recognition
  private initSpeechRecognition(): boolean {
    if (typeof window === "undefined") return false

    // Check if the browser supports speech recognition
    const SpeechRecognition: SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition

    if (!SpeechRecognition) {
      console.error("Speech recognition not supported in this browser")
      return false
    }

    try {
      this.recognition = new SpeechRecognition()
      this.recognition.continuous = false
      this.recognition.interimResults = false
      this.recognition.lang = this.language

      // Set up event handlers
      this.recognition.onstart = this.handleStart.bind(this)
      this.recognition.onend = this.handleEnd.bind(this)
      this.recognition.onresult = this.handleResult.bind(this)
      this.recognition.onerror = this.handleError.bind(this)

      return true
    } catch (error) {
      console.error("Error initializing speech recognition:", error)
      return false
    }
  }

  // Start listening for voice commands
  startListening(): boolean {
    if (!this.recognition) {
      if (!this.initSpeechRecognition()) {
        if (this.onError) this.onError("Speech recognition not supported")
        return false
      }
    }

    if (this.isListening) return true

    try {
      this.recognition!.start()
      return true
    } catch (error) {
      console.error("Error starting speech recognition:", error)
      if (this.onError) this.onError("Failed to start listening")
      return false
    }
  }

  // Stop listening for voice commands
  stopListening(): boolean {
    if (!this.recognition || !this.isListening) return false

    try {
      this.recognition.stop()
      return true
    } catch (error) {
      console.error("Error stopping speech recognition:", error)
      return false
    }
  }

  // Set continuous mode
  setContinuousMode(continuous: boolean): void {
    this.continuousMode = continuous

    if (this.recognition) {
      this.recognition.continuous = continuous
    }
  }

  // Set recognition language
  setLanguage(language: string): void {
    this.language = language

    if (this.recognition) {
      this.recognition.lang = language
    }
  }

  // Register a command
  registerCommand(command: string, callback: Function): void {
    this.commands.set(command.toLowerCase(), callback)
  }

  // Register a command pattern (regex)
  registerCommandPattern(pattern: RegExp, callback: Function): void {
    this.commandPatterns.set(pattern, callback)
  }

  // Unregister a command
  unregisterCommand(command: string): boolean {
    return this.commands.delete(command.toLowerCase())
  }

  // Unregister a command pattern
  unregisterCommandPattern(pattern: RegExp): boolean {
    return this.commandPatterns.delete(pattern)
  }

  // Clear all commands
  clearCommands(): void {
    this.commands.clear()
    this.commandPatterns.clear()
  }

  // Set listening change callback
  onListeningChangeCallback(callback: (isListening: boolean) => void): void {
    this.onListeningChange = callback
  }

  // Set result callback
  onResultCallback(callback: (result: string) => void): void {
    this.onResult = callback
  }

  // Set error callback
  onErrorCallback(callback: (error: string) => void): void {
    this.onError = callback
  }

  // Handle recognition start
  private handleStart(): void {
    this.isListening = true
    if (this.onListeningChange) this.onListeningChange(true)
  }

  // Handle recognition end
  private handleEnd(): void {
    this.isListening = false
    if (this.onListeningChange) this.onListeningChange(false)

    // Restart if in continuous mode
    if (this.continuousMode) {
      this.startListening()
    }
  }

  // Handle recognition result
  private handleResult(event: SpeechRecognitionEvent): void {
    const result = event.results[0][0].transcript.trim().toLowerCase()

    if (this.onResult) this.onResult(result)

    // Check for exact command matches
    if (this.commands.has(result)) {
      this.commands.get(result)!()
      return
    }

    // Check for pattern matches
    for (const [pattern, callback] of this.commandPatterns.entries()) {
      const match = result.match(pattern)
      if (match) {
        callback(match)
        return
      }
    }
  }

  // Handle recognition error
  private handleError(event: SpeechRecognitionErrorEvent): void {
    this.isListening = false

    if (this.onListeningChange) this.onListeningChange(false)
    if (this.onError) this.onError(`Speech recognition error: ${event.error}`)

    console.error("Speech recognition error:", event.error)
  }

  // Check if speech recognition is supported
  isSupported(): boolean {
    return !!(window.SpeechRecognition || window.webkitSpeechRecognition)
  }

  // Check if currently listening
  getIsListening(): boolean {
    return this.isListening
  }
}

// Create singleton instance
export const voiceCommandService = new VoiceCommandService()

