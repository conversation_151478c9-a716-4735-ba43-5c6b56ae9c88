import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const { amount, paymentMethod, notes } = body

    // Validate the layaway exists
    const layaway = await prisma.layaway.findUnique({
      where: { id: params.id },
    })

    if (!layaway) {
      return NextResponse.json({ error: "Layaway not found" }, { status: 404 })
    }

    // Validate payment amount
    if (amount <= 0) {
      return NextResponse.json({ error: "Payment amount must be greater than 0" }, { status: 400 })
    }

    if (amount > layaway.balanceDue) {
      return NextResponse.json({ error: "Payment amount cannot exceed balance due" }, { status: 400 })
    }

    // Add payment and update layaway in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create payment record
      const payment = await tx.layawayPayment.create({
        data: {
          layawayId: params.id,
          amount,
          paymentMethod,
          notes: notes || "Payment received",
        },
      })

      // Update layaway balance
      const updatedLayaway = await tx.layaway.update({
        where: { id: params.id },
        data: {
          balanceDue: {
            decrement: amount,
          },
        },
      })

      // Check if balance is fully paid
      if (updatedLayaway.balanceDue <= 0) {
        // Update status to COMPLETED
        await tx.layaway.update({
          where: { id: params.id },
          data: {
            status: "COMPLETED",
          },
        })

        // Create notification for completed payment
        await tx.notification.create({
          data: {
            title: "Layaway Fully Paid",
            message: `Layaway #${layaway.layawayNumber} has been fully paid and is ready for completion.`,
            type: "SUCCESS",
            userId: session.user.id,
          },
        })
      } else {
        // Create notification for partial payment
        await tx.notification.create({
          data: {
            title: "Layaway Payment Received",
            message: `Payment of ${amount} received for Layaway #${layaway.layawayNumber}. Remaining balance: ${updatedLayaway.balanceDue}.`,
            type: "INFO",
            userId: session.user.id,
          },
        })
      }

      return { payment, updatedLayaway }
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error("Error adding layaway payment:", error)
    return NextResponse.json({ error: "Failed to add payment" }, { status: 500 })
  }
}

