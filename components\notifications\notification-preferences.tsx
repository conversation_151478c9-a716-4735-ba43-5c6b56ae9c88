"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Bell, BellOff, Clock } from "lucide-react"
import {
  pushNotificationService,
  type NotificationPreferences,
  type NotificationChannel,
} from "@/lib/notifications/push-notification-service"
import { useToast } from "@/hooks/use-toast"

export default function NotificationPreferences() {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    async function initializeNotifications() {
      try {
        const success = await pushNotificationService.initialize()
        setInitialized(success)

        const prefs = await pushNotificationService.loadPreferences()
        setPreferences(prefs)
      } catch (error) {
        console.error("Error initializing notifications:", error)
      } finally {
        setLoading(false)
      }
    }

    initializeNotifications()
  }, [])

  const handleToggleNotifications = async (enabled: boolean) => {
    if (!preferences) return

    try {
      const updatedPreferences = { ...preferences, enabled }
      await pushNotificationService.savePreferences(updatedPreferences)
      setPreferences(updatedPreferences)

      toast({
        title: enabled ? "Notifications enabled" : "Notifications disabled",
        description: enabled
          ? "You will now receive push notifications"
          : "You will no longer receive push notifications",
        variant: "default",
      })
    } catch (error) {
      console.error("Error toggling notifications:", error)
      toast({
        title: "Error",
        description: "Failed to update notification settings",
        variant: "destructive",
      })
    }
  }

  const handleToggleChannel = async (channel: NotificationChannel, enabled: boolean) => {
    if (!preferences) return

    try {
      await pushNotificationService.updateChannelPreference(channel, enabled)
      setPreferences({
        ...preferences,
        channels: {
          ...preferences.channels,
          [channel]: enabled,
        },
      })
    } catch (error) {
      console.error(`Error toggling ${channel} channel:`, error)
      toast({
        title: "Error",
        description: "Failed to update channel settings",
        variant: "destructive",
      })
    }
  }

  const handleToggleQuietHours = async (enabled: boolean) => {
    if (!preferences) return

    try {
      await pushNotificationService.updateQuietHours(enabled)
      setPreferences({
        ...preferences,
        quiet_hours: {
          ...preferences.quiet_hours,
          enabled,
        },
      })
    } catch (error) {
      console.error("Error toggling quiet hours:", error)
      toast({
        title: "Error",
        description: "Failed to update quiet hours settings",
        variant: "destructive",
      })
    }
  }

  const handleUpdateQuietHours = async () => {
    if (!preferences) return

    try {
      await pushNotificationService.updateQuietHours(
        preferences.quiet_hours.enabled,
        preferences.quiet_hours.start,
        preferences.quiet_hours.end,
      )

      toast({
        title: "Quiet hours updated",
        description: "Your quiet hours settings have been saved",
        variant: "default",
      })
    } catch (error) {
      console.error("Error updating quiet hours:", error)
      toast({
        title: "Error",
        description: "Failed to update quiet hours settings",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!initialized) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Push Notifications</CardTitle>
          <CardDescription>
            Push notifications are not available on this device or permission was denied.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-4">
            <BellOff className="h-12 w-12 text-muted-foreground" />
          </div>
          <p className="text-center text-sm text-muted-foreground">
            Push notifications require a native app installation or browser permission.
          </p>
        </CardContent>
      </Card>
    )
  }

  if (!preferences) return null

  return (
    <Card>
      <CardHeader>
        <CardTitle>Push Notifications</CardTitle>
        <CardDescription>Configure how and when you receive push notifications</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {preferences.enabled ? (
              <Bell className="h-6 w-6 text-primary" />
            ) : (
              <BellOff className="h-6 w-6 text-muted-foreground" />
            )}
            <div>
              <p className="font-medium">Push Notifications</p>
              <p className="text-sm text-muted-foreground">
                {preferences.enabled ? "You will receive push notifications" : "Push notifications are disabled"}
              </p>
            </div>
          </div>
          <Switch checked={preferences.enabled} onCheckedChange={handleToggleNotifications} />
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Notification Channels</h3>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="inventory-notifications" className="flex-1">
                Inventory Updates
              </Label>
              <Switch
                id="inventory-notifications"
                checked={preferences.channels.inventory}
                onCheckedChange={(checked) => handleToggleChannel("inventory", checked)}
                disabled={!preferences.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="order-notifications" className="flex-1">
                Order Alerts
              </Label>
              <Switch
                id="order-notifications"
                checked={preferences.channels.orders}
                onCheckedChange={(checked) => handleToggleChannel("orders", checked)}
                disabled={!preferences.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="customer-notifications" className="flex-1">
                Customer Updates
              </Label>
              <Switch
                id="customer-notifications"
                checked={preferences.channels.customers}
                onCheckedChange={(checked) => handleToggleChannel("customers", checked)}
                disabled={!preferences.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="system-notifications" className="flex-1">
                System Notifications
              </Label>
              <Switch
                id="system-notifications"
                checked={preferences.channels.system}
                onCheckedChange={(checked) => handleToggleChannel("system", checked)}
                disabled={!preferences.enabled}
              />
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Clock className="h-6 w-6 text-primary" />
              <div>
                <p className="font-medium">Quiet Hours</p>
                <p className="text-sm text-muted-foreground">
                  {preferences.quiet_hours.enabled
                    ? `No notifications between ${preferences.quiet_hours.start} and ${preferences.quiet_hours.end}`
                    : "Receive notifications at any time"}
                </p>
              </div>
            </div>
            <Switch
              checked={preferences.quiet_hours.enabled}
              onCheckedChange={handleToggleQuietHours}
              disabled={!preferences.enabled}
            />
          </div>

          {preferences.quiet_hours.enabled && (
            <div className="grid grid-cols-2 gap-4 pt-2">
              <div className="space-y-2">
                <Label htmlFor="quiet-start">Start Time</Label>
                <Input
                  id="quiet-start"
                  type="time"
                  value={preferences.quiet_hours.start}
                  onChange={(e) =>
                    setPreferences({
                      ...preferences,
                      quiet_hours: {
                        ...preferences.quiet_hours,
                        start: e.target.value,
                      },
                    })
                  }
                  disabled={!preferences.enabled}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="quiet-end">End Time</Label>
                <Input
                  id="quiet-end"
                  type="time"
                  value={preferences.quiet_hours.end}
                  onChange={(e) =>
                    setPreferences({
                      ...preferences,
                      quiet_hours: {
                        ...preferences.quiet_hours,
                        end: e.target.value,
                      },
                    })
                  }
                  disabled={!preferences.enabled}
                />
              </div>
              <Button className="col-span-2" onClick={handleUpdateQuietHours} disabled={!preferences.enabled}>
                Update Quiet Hours
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

