import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { AuditService } from "@/lib/services/audit-service"
import { ApiError } from "@/lib/api-error"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to view audit logs
    if (session.user.role !== "ADMIN" && session.user.role !== "MANAGER") {
      throw ApiError.forbidden("You do not have permission to view audit logs")
    }

    const searchParams = req.nextUrl.searchParams
    const userId = searchParams.get("userId")
    const entityType = searchParams.get("entityType")
    const entityId = searchParams.get("entityId")
    const action = searchParams.get("action")
    const startDate = searchParams.get("startDate")
    const endDate = searchParams.get("endDate")
    const page = searchParams.get("page") ? Number.parseInt(searchParams.get("page")!) : 1
    const pageSize = searchParams.get("pageSize") ? Number.parseInt(searchParams.get("pageSize")!) : 20

    // Get audit logs
    const result = await AuditService.getAuditLogs({
      userId: userId || undefined,
      entityType: entityType || undefined,
      entityId: entityId || undefined,
      action: action || undefined,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      page,
      pageSize,
    })

    return NextResponse.json(result)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Get audit logs error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

