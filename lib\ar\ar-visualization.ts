import * as THREE from "three"
import { ARButton } from "three/examples/jsm/webxr/ARButton.js"
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js"

export interface ARVisualizationOptions {
  container: HTMLElement
  onARStarted?: () => void
  onAREnded?: () => void
  onError?: (error: Error) => void
}

export interface ProductModel {
  id: string
  name: string
  modelUrl: string
  scale?: number
  position?: { x: number; y: number; z: number }
  rotation?: { x: number; y: number; z: number }
}

class ARVisualization {
  private container: HTMLElement | null = null
  private scene: THREE.Scene | null = null
  private camera: THREE.PerspectiveCamera | null = null
  private renderer: THREE.WebGLRenderer | null = null
  private arButton: HTMLElement | null = null
  private models: Map<string, THREE.Object3D> = new Map()
  private raycaster: THREE.Raycaster = new THREE.Raycaster()
  private isARMode = false
  private onARStarted?: () => void
  private onAREnded?: () => void
  private onError?: (error: Error) => void

  // Initialize the AR visualization
  initialize(options: ARVisualizationOptions): boolean {
    try {
      this.container = options.container
      this.onARStarted = options.onARStarted
      this.onAREnded = options.onAREnded
      this.onError = options.onError

      // Check if WebXR is supported
      if (!this.isWebXRSupported()) {
        throw new Error("WebXR not supported on this device")
      }

      // Create the scene
      this.scene = new THREE.Scene()

      // Create the camera
      this.camera = new THREE.PerspectiveCamera(70, window.innerWidth / window.innerHeight, 0.01, 20)

      // Create the renderer
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
      })
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.setSize(window.innerWidth, window.innerHeight)
      this.renderer.xr.enabled = true

      // Add the renderer to the container
      this.container.appendChild(this.renderer.domElement)

      // Add lighting
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
      this.scene.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(0, 5, 0)
      this.scene.add(directionalLight)

      // Create the AR button
      this.arButton = ARButton.createButton(this.renderer, {
        requiredFeatures: ["hit-test"],
        optionalFeatures: ["dom-overlay"],
        domOverlay: { root: document.body },
      })

      // Add event listeners for AR session
      this.renderer.xr.addEventListener("sessionstart", () => {
        this.isARMode = true
        if (this.onARStarted) this.onARStarted()
      })

      this.renderer.xr.addEventListener("sessionend", () => {
        this.isARMode = false
        if (this.onAREnded) this.onAREnded()
      })

      // Add the AR button to the container
      this.container.appendChild(this.arButton)

      // Set up the animation loop
      this.renderer.setAnimationLoop(this.render.bind(this))

      // Handle window resize
      window.addEventListener("resize", this.onWindowResize.bind(this))

      return true
    } catch (error) {
      console.error("Error initializing AR visualization:", error)
      if (this.onError && error instanceof Error) {
        this.onError(error)
      }
      return false
    }
  }

  // Check if WebXR is supported
  private isWebXRSupported(): boolean {
    return "xr" in navigator && "isSessionSupported" in (navigator as any).xr
  }

  // Handle window resize
  private onWindowResize(): void {
    if (!this.camera || !this.renderer) return

    this.camera.aspect = window.innerWidth / window.innerHeight
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(window.innerWidth, window.innerHeight)
  }

  // Render the scene
  private render(): void {
    if (!this.scene || !this.camera || !this.renderer) return
    this.renderer.render(this.scene, this.camera)
  }

  // Load a 3D model
  async loadModel(product: ProductModel): Promise<boolean> {
    if (!this.scene) return false

    try {
      const loader = new GLTFLoader()

      return new Promise((resolve, reject) => {
        loader.load(
          product.modelUrl,
          (gltf) => {
            const model = gltf.scene

            // Apply scale
            const scale = product.scale || 1
            model.scale.set(scale, scale, scale)

            // Apply position
            if (product.position) {
              model.position.set(product.position.x, product.position.y, product.position.z)
            }

            // Apply rotation
            if (product.rotation) {
              model.rotation.set(product.rotation.x, product.rotation.y, product.rotation.z)
            }

            // Add the model to the scene
            this.scene.add(model)

            // Store the model for later reference
            this.models.set(product.id, model)

            // Hide the model initially
            model.visible = false

            resolve(true)
          },
          undefined,
          (error) => {
            console.error("Error loading model:", error)
            reject(error)
          },
        )
      })
    } catch (error) {
      console.error("Error loading model:", error)
      if (this.onError && error instanceof Error) {
        this.onError(error)
      }
      return false
    }
  }

  // Show a model at a specific position
  showModel(productId: string, position?: THREE.Vector3): boolean {
    const model = this.models.get(productId)
    if (!model) return false

    if (position) {
      model.position.copy(position)
    }

    model.visible = true
    return true
  }

  // Hide a model
  hideModel(productId: string): boolean {
    const model = this.models.get(productId)
    if (!model) return false

    model.visible = false
    return true
  }

  // Remove a model
  removeModel(productId: string): boolean {
    const model = this.models.get(productId)
    if (!model || !this.scene) return false

    this.scene.remove(model)
    this.models.delete(productId)
    return true
  }

  // Clean up resources
  dispose(): void {
    if (this.renderer) {
      this.renderer.setAnimationLoop(null)
      this.renderer.dispose()
    }

    if (this.arButton && this.arButton.parentNode) {
      this.arButton.parentNode.removeChild(this.arButton)
    }

    window.removeEventListener("resize", this.onWindowResize.bind(this))

    this.models.clear()
    this.scene = null
    this.camera = null
    this.renderer = null
    this.container = null
  }

  // Check if AR is currently active
  isARActive(): boolean {
    return this.isARMode
  }

  // Get the AR button
  getARButton(): HTMLElement | null {
    return this.arButton
  }
}

// Create singleton instance
export const arVisualization = new ARVisualization()

