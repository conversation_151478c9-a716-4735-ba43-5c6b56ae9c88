import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const customerId = params.id

    const customer = await prisma.customer.findUnique({
      where: {
        id: customerId,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        storeCredit: true,
        creditLimit: true,
        creditHold: true,
      },
    })

    if (!customer) {
      return new NextResponse("Customer not found", { status: 404 })
    }

    const transactions = await prisma.creditTransaction.findMany({
      where: {
        customerId,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json({
      customer,
      transactions,
    })
  } catch (error) {
    console.error("[CUSTOMER_CREDIT_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function POST(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const customerId = params.id
    const body = await req.json()
    const { amount, type, description } = body

    const customer = await prisma.customer.findUnique({
      where: {
        id: customerId,
      },
    })

    if (!customer) {
      return new NextResponse("Customer not found", { status: 404 })
    }

    // Calculate new balance
    let newBalance = customer.storeCredit
    if (type === "ADD") {
      newBalance += amount
    } else if (type === "USE" || type === "EXPIRE") {
      newBalance -= amount
      if (newBalance < 0) {
        return new NextResponse("Insufficient store credit", { status: 400 })
      }
    } else if (type === "ADJUST") {
      newBalance = amount
    }

    // Create transaction
    const transaction = await prisma.creditTransaction.create({
      data: {
        customerId,
        amount,
        balance: newBalance,
        type,
        description,
      },
    })

    // Update customer credit
    await prisma.customer.update({
      where: {
        id: customerId,
      },
      data: {
        storeCredit: newBalance,
      },
    })

    return NextResponse.json({
      transaction,
      currentBalance: newBalance,
    })
  } catch (error) {
    console.error("[CUSTOMER_CREDIT_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const customerId = params.id
    const body = await req.json()
    const { creditLimit, creditHold } = body

    const customer = await prisma.customer.findUnique({
      where: {
        id: customerId,
      },
    })

    if (!customer) {
      return new NextResponse("Customer not found", { status: 404 })
    }

    // Update customer credit settings
    const updatedCustomer = await prisma.customer.update({
      where: {
        id: customerId,
      },
      data: {
        creditLimit: creditLimit === undefined ? customer.creditLimit : creditLimit,
        creditHold: creditHold === undefined ? customer.creditHold : creditHold,
      },
    })

    return NextResponse.json(updatedCustomer)
  } catch (error) {
    console.error("[CUSTOMER_CREDIT_PUT]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

