{"app": {"name": "StockSync", "tagline": "Inventory & POS System"}, "navigation": {"dashboard": "Dashboard", "pos": "Point of Sale", "inventory": "Inventory", "reports": "Reports", "users": "Users", "settings": "Settings", "profile": "Profile", "logout": "Log out"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot password?", "signIn": "Sign in", "signUp": "Sign up", "createAccount": "Create an account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?"}, "dashboard": {"title": "Dashboard", "overview": "Overview", "recentSales": "Recent Sales", "stockAlerts": "<PERSON> Alerts", "totalRevenue": "Total Revenue", "orders": "Orders", "products": "Products", "customers": "Customers", "from": "from last month"}, "inventory": {"title": "Inventory", "products": "Products", "categories": "Categories", "suppliers": "Suppliers", "addProduct": "Add Product", "editProduct": "Edit Product", "productName": "Product Name", "sku": "SKU", "barcode": "Barcode", "price": "Price", "cost": "Cost", "stockQuantity": "Stock Quantity", "reorderPoint": "Reorder Point", "category": "Category", "supplier": "Supplier", "description": "Description", "status": {"inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock"}, "search": "Search products...", "filters": "Filters", "export": "Export"}, "pos": {"title": "Point of Sale", "currentSale": "Current Sale", "scanBarcode": "Scan Barcode", "searchProducts": "Search products...", "emptyCart": "Your cart is empty", "addProducts": "Add products to get started", "heldSales": "Held Sales", "subtotal": "Subtotal", "tax": "Tax", "total": "Total", "pay": "Pay", "hold": "Hold", "receipt": "Receipt", "item": "<PERSON><PERSON>", "qty": "Qty", "price": "Price", "actions": "Actions", "offlineMode": "Offline Mode"}, "orders": {"title": "Orders", "orderID": "Order ID", "customer": "Customer", "date": "Date", "status": {"completed": "Completed", "processing": "Processing", "pending": "Pending", "cancelled": "Cancelled"}, "payment": "Payment", "items": "Items", "total": "Total", "actions": "Actions", "search": "Search orders...", "print": "Print", "export": "Export", "viewDetails": "View Details", "printReceipt": "Print Receipt", "cancelOrder": "Cancel Order"}, "customers": {"title": "Customers", "addCustomer": "Add Customer", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "totalOrders": "Total Orders", "totalSpent": "Total Spent", "lastOrder": "Last Order", "status": "Status", "search": "Search customers...", "allCustomers": "All Customers", "active": "Active", "inactive": "Inactive"}, "reports": {"title": "Reports & Analytics", "analyze": "Analyze your business performance and generate custom reports", "salesReports": "Sales Reports", "inventoryReports": "Inventory Reports", "customerReports": "Customer Reports", "dateRange": "Date Range", "filters": "Filters", "export": "Export", "metrics": {"totalRevenue": "Total Revenue", "totalOrders": "Total Orders", "averageOrderValue": "Average Order Value", "itemsSold": "Items Sold", "totalProducts": "Total Products", "lowStockItems": "Low Stock Items", "inventoryValue": "Inventory Value", "stockTurnover": "Stock Turnover", "outOfStock": "Out of Stock", "totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "newCustomers": "New Customers", "averageSpend": "Average Spend", "repeatPurchaseRate": "Repeat Purchase Rate", "customerRetention": "Customer Retention"}, "charts": {"salesPerformance": "Sales Performance", "inventoryAnalysis": "Inventory Analysis", "customerInsights": "Customer Insights", "line": "Line", "bar": "Bar", "area": "Area", "pie": "Pie"}, "groupBy": {"label": "Group By", "day": "Day", "week": "Week", "month": "Month", "category": "Category", "customer": "Customer"}}, "settings": {"title": "Settings", "company": "Company", "tax": "Tax", "receipt": "Receipt", "system": "System", "notifications": "Notifications", "language": "Language", "theme": "Theme", "companyInfo": {"name": "Company Name", "address": "Address", "phone": "Phone", "email": "Email", "website": "Website", "logo": "Logo"}, "taxSettings": {"rate": "Tax Rate (%)", "includeInPrice": "Include tax in product prices", "taxNumber": "Tax Registration Number"}, "receiptSettings": {"showLogo": "Show logo on receipts", "footer": "Receipt Footer", "includeItemSku": "Include product SKU on receipts", "includeTaxDetails": "Include detailed tax information"}, "systemSettings": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "dateFormat": "Date Format", "timeFormat": "Time Format", "lowStockThreshold": "Low Stock Threshold", "enableNotifications": "Enable notifications", "autoBackup": "Enable automatic backups"}, "languageSettings": {"title": "Language Settings", "description": "Choose your preferred language", "selectLanguage": "Select Language", "english": "English", "spanish": "Spanish", "french": "French", "german": "German", "chinese": "Chinese"}, "themeSettings": {"title": "Theme Settings", "description": "Choose your preferred theme", "light": "Light", "dark": "Dark", "system": "System"}, "saveChanges": "Save Changes", "saving": "Saving..."}, "notifications": {"title": "Notifications", "manage": "View and manage your notifications", "markAllAsRead": "Mark all as read", "clearAll": "Clear All", "search": "Search notifications...", "all": "All", "unread": "Unread", "info": "Info", "success": "Success", "warnings": "Warnings", "errors": "Errors", "noNotifications": "No notifications", "noMatch": "No notifications match your search", "viewDetails": "View details", "markAsRead": "<PERSON> as read", "delete": "Delete", "clearConfirm": {"title": "Clear all notifications?", "description": "This action cannot be undone. This will permanently delete all your notifications.", "cancel": "Cancel", "continue": "Continue"}, "settings": {"title": "Notification Settings", "description": "Configure how and when you receive notifications", "deliveryMethods": "Delivery Methods", "notificationTypes": "Notification Types", "emailNotifications": "Email Notifications", "emailDescription": "Receive notifications via email", "pushNotifications": "Push Notifications", "pushDescription": "Receive notifications in the browser", "lowStockAlerts": "Low Stock Alerts", "lowStockDescription": "Get notified when products are running low", "newOrderAlerts": "New Order Alerts", "newOrderDescription": "Get notified when new orders are placed", "orderStatusChanges": "Order Status Changes", "orderStatusDescription": "Get notified when order statuses change", "inventoryUpdates": "Inventory Updates", "inventoryDescription": "Get notified about inventory changes", "dailySummary": "Daily Summary", "dailySummaryDescription": "Receive a daily summary of activity"}}, "profile": {"title": "Profile", "profileInfo": "Profile Information", "updateProfile": "Update your profile information", "name": "Name", "email": "Email", "profilePicture": "Profile Picture", "changePassword": "Change Password", "updatePassword": "Update your password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "accountSettings": "Account <PERSON><PERSON>", "twoFactorAuth": "Two-Factor Authentication", "twoFactorDescription": "Add an extra layer of security to your account", "emailNotifications": "Email Notifications", "emailNotificationsDescription": "Receive email notifications for important events", "deleteAccount": "Delete Account", "deleteAccountDescription": "Permanently delete your account and all data", "enable": "Enable", "configure": "Configure", "delete": "Delete"}, "common": {"search": "Search", "add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "noData": "No data available", "actions": "Actions", "status": "Status", "details": "Details", "view": "View", "print": "Print", "export": "Export", "refresh": "Refresh", "filters": "Filters", "all": "All", "from": "From", "to": "To", "date": "Date", "time": "Time", "name": "Name", "description": "Description", "category": "Category", "categories": "Categories", "product": "Product", "products": "Products", "order": "Order", "orders": "Orders", "customer": "Customer", "customers": "Customers", "supplier": "Supplier", "suppliers": "Suppliers", "price": "Price", "quantity": "Quantity", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "payment": "Payment", "created": "Created", "updated": "Updated", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info"}, "errors": {"somethingWentWrong": "Something went wrong", "tryAgain": "Please try again", "pageNotFound": "Page not found", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "serverError": "Server error", "connectionError": "Connection error", "validationError": "Validation error", "requiredField": "This field is required", "invalidEmail": "Invalid email address", "invalidPassword": "Password must be at least 8 characters", "passwordMismatch": "Passwords do not match"}}