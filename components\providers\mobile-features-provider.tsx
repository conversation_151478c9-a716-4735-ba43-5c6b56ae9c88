"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import { usePathname } from "next/navigation"
import { Capacitor } from "@capacitor/core"
import { useToast } from "@/hooks/use-toast"

// Lazy imports to prevent SSR issues
let pushNotificationService: any = null
let offlineDataManager: any = null
let voiceCommandService: any = null

// Initialize services only on client side
const initializeServices = async () => {
  if (typeof window !== "undefined" && !pushNotificationService) {
    const { pushNotificationService: pns } = await import("@/lib/notifications/push-notification-service")
    const { offlineDataManager: odm } = await import("@/lib/offline/offline-data-manager")
    const { voiceCommandService: vcs } = await import("@/lib/voice/voice-command-service")

    pushNotificationService = pns
    offlineDataManager = odm
    voiceCommandService = vcs
  }
}

interface MobileFeaturesContextType {
  isNative: boolean
  isOnline: boolean
  syncStatus: { total: number; pending: number }
  isListening: boolean
  startVoiceListening: () => void
  stopVoiceListening: () => void
  forceSync: () => Promise<void>
}

const MobileFeaturesContext = createContext<MobileFeaturesContextType>({
  isNative: false,
  isOnline: true,
  syncStatus: { total: 0, pending: 0 },
  isListening: false,
  startVoiceListening: () => {},
  stopVoiceListening: () => {},
  forceSync: async () => {},
})

export const useMobileFeatures = () => useContext(MobileFeaturesContext)

interface MobileFeaturesProviderProps {
  children: ReactNode
}

export function MobileFeaturesProvider({ children }: MobileFeaturesProviderProps) {
  const [isNative, setIsNative] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [syncStatus, setSyncStatus] = useState({ total: 0, pending: 0 })
  const [isListening, setIsListening] = useState(false)
  const pathname = usePathname()
  const { toast } = useToast()

  // Initialize mobile features
  useEffect(() => {
    const initMobileFeatures = async () => {
      // Initialize services first
      await initializeServices()

      // Check if running in a native app
      setIsNative(Capacitor.isNativePlatform())

      // Initialize push notifications if in native app
      if (Capacitor.isNativePlatform() && pushNotificationService) {
        pushNotificationService.initialize().catch((error) => {
          console.error("Error initializing push notifications:", error)
        })
      }

      // Set up network status listeners
      const handleNetworkChange = ({ status }: { status: "online" | "offline" }) => {
        setIsOnline(status === "online")
      }

      // Set up sync status listeners
      const handleSyncComplete = (data: { remaining: number }) => {
        setSyncStatus({ total: data.remaining, pending: data.remaining })
      }

      // Set up voice command listeners
      const handleListeningChange = (listening: boolean) => {
        setIsListening(listening)
      }

      // Add event listeners if services are available
      if (offlineDataManager) {
        offlineDataManager.addEventListener("networkStatusChange", handleNetworkChange)
        offlineDataManager.addEventListener("syncComplete", handleSyncComplete)

        // Initialize with current status
        setIsOnline(offlineDataManager.getNetworkStatus() === "online")
        setSyncStatus(offlineDataManager.getSyncQueueStatus())
      }

      if (voiceCommandService) {
        voiceCommandService.onListeningChangeCallback(handleListeningChange)
      }

      // Set up interval to update sync status
      const interval = setInterval(() => {
        if (offlineDataManager) {
          setSyncStatus(offlineDataManager.getSyncQueueStatus())
        }
      }, 5000)

      // Clean up
      return () => {
        if (offlineDataManager) {
          offlineDataManager.removeEventListener("networkStatusChange", handleNetworkChange)
          offlineDataManager.removeEventListener("syncComplete", handleSyncComplete)
        }
        clearInterval(interval)
      }
    }

    initMobileFeatures()
  }, [])

  // Handle path changes for voice commands
  useEffect(() => {
    // Stop voice listening when navigating
    if (isListening && voiceCommandService) {
      voiceCommandService.stopListening()
    }
  }, [pathname, isListening])

  // Start voice listening
  const startVoiceListening = () => {
    if (!voiceCommandService) return

    const success = voiceCommandService.startListening()

    if (!success) {
      toast({
        title: "Error",
        description: "Failed to start voice recognition",
        variant: "destructive",
      })
    }
  }

  // Stop voice listening
  const stopVoiceListening = () => {
    if (!voiceCommandService) return
    voiceCommandService.stopListening()
  }

  // Force data sync
  const forceSync = async () => {
    if (!isOnline || !offlineDataManager) {
      toast({
        title: "Offline",
        description: "Cannot sync while offline",
        variant: "destructive",
      })
      return
    }

    try {
      await offlineDataManager.forceSync()
      toast({
        title: "Sync Started",
        description: "Synchronizing your data...",
        variant: "default",
      })
    } catch (error) {
      console.error("Error forcing sync:", error)
      toast({
        title: "Sync Error",
        description: "Failed to synchronize data",
        variant: "destructive",
      })
    }
  }

  const value = {
    isNative,
    isOnline,
    syncStatus,
    isListening,
    startVoiceListening,
    stopVoiceListening,
    forceSync,
  }

  return <MobileFeaturesContext.Provider value={value}>{children}</MobileFeaturesContext.Provider>
}

