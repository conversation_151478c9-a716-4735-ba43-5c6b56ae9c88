"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { fetchApi } from "@/lib/api-client"
import { useToast } from "@/hooks/use-toast"
import { format, addDays } from "date-fns"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
} from "@/components/ui/chart"
import { AlertCircle, Calendar, TrendingUp, TrendingDown, Package, AlertTriangle, Download } from "lucide-react"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useCachedData } from "@/lib/hooks/use-cached-data"

interface ForecastResult {
  productId: string
  productName: string
  sku: string
  currentStock: number
  reorderPoint: number
  averageDailySales: number
  projectedStockDate: string | null
  daysUntilStockout: number | null
  recommendedOrder: number
  confidence: "high" | "medium" | "low"
  historicalData: {
    date: string
    sales: number
    stock: number
  }[]
  forecastData: {
    date: string
    projectedSales: number
    projectedStock: number
  }[]
}

export function InventoryForecast() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [forecastData, setForecastData] = useState<ForecastResult[]>([])
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [forecastDays, setForecastDays] = useState(30)
  const [startDate, setStartDate] = useState(format(addDays(new Date(), -90), "yyyy-MM-dd"))
  const [endDate, setEndDate] = useState(format(new Date(), "yyyy-MM-dd"))
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedForecast, setSelectedForecast] = useState<ForecastResult | null>(null)

  const { data: products } = useCachedData<{ id: string; name: string }[]>({
    key: "products-simple",
    fetcher: () => fetchApi("/api/products?fields=id,name"),
  })

  const { data: categories } = useCachedData<{ id: string; name: string }[]>({
    key: "categories",
    fetcher: () => fetchApi("/api/categories"),
  })

  useEffect(() => {
    loadForecastData()
  }, [])

  const loadForecastData = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams()

      if (selectedProduct) {
        params.append("productId", selectedProduct)
      }

      if (selectedCategory) {
        params.append("categoryId", selectedCategory)
      }

      params.append("startDate", startDate)
      params.append("endDate", endDate)
      params.append("forecastDays", forecastDays.toString())

      const data = await fetchApi(`/api/inventory/forecast?${params.toString()}`)
      setForecastData(data)

      if (data.length > 0 && !selectedForecast) {
        setSelectedForecast(data[0])
      }
    } catch (error) {
      toast({
        title: "Failed to load forecast data",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleApplyFilters = () => {
    loadForecastData()
  }

  const handleExportCSV = () => {
    if (!forecastData.length) return

    // Create CSV content
    let csvContent =
      "Product Name,SKU,Current Stock,Reorder Point,Avg Daily Sales,Days Until Stockout,Recommended Order,Confidence\n"

    forecastData.forEach((item) => {
      csvContent += `"${item.productName}","${item.sku}",${item.currentStock},${item.reorderPoint},${item.averageDailySales.toFixed(2)},${item.daysUntilStockout || "N/A"},${item.recommendedOrder},${item.confidence}\n`
    })

    // Create download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `inventory-forecast-${format(new Date(), "yyyy-MM-dd")}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const getConfidenceBadge = (confidence: string) => {
    switch (confidence) {
      case "high":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">High Confidence</Badge>
      case "medium":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Medium Confidence</Badge>
      case "low":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Low Confidence</Badge>
      default:
        return null
    }
  }

  const renderStockStatus = (forecast: ForecastResult) => {
    if (forecast.currentStock <= forecast.reorderPoint) {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Low Stock Alert</AlertTitle>
          <AlertDescription>Current stock is below reorder point. Immediate action required.</AlertDescription>
        </Alert>
      )
    } else if (forecast.daysUntilStockout !== null && forecast.daysUntilStockout <= 14) {
      return (
        <Alert variant="warning">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Stock Warning</AlertTitle>
          <AlertDescription>
            Stock will reach reorder point in {forecast.daysUntilStockout} days. Consider ordering soon.
          </AlertDescription>
        </Alert>
      )
    } else {
      return (
        <Alert>
          <Package className="h-4 w-4" />
          <AlertTitle>Stock Status</AlertTitle>
          <AlertDescription>Stock levels are healthy. No immediate action required.</AlertDescription>
        </Alert>
      )
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Inventory Forecast</CardTitle>
          <CardDescription>Predict future inventory levels and optimize reorder points</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div>
              <label className="text-sm font-medium mb-1 block">Product</label>
              <Select value={selectedProduct || ""} onValueChange={(value) => setSelectedProduct(value || null)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Products" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Products</SelectItem>
                  {products?.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Category</label>
              <Select value={selectedCategory || ""} onValueChange={(value) => setSelectedCategory(value || null)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories?.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Start Date</label>
              <Input type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">End Date</label>
              <Input type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Forecast Days:</label>
              <Select
                value={forecastDays.toString()}
                onValueChange={(value) => setForecastDays(Number.parseInt(value, 10))}
              >
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="30" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7</SelectItem>
                  <SelectItem value="14">14</SelectItem>
                  <SelectItem value="30">30</SelectItem>
                  <SelectItem value="60">60</SelectItem>
                  <SelectItem value="90">90</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={handleExportCSV} disabled={isLoading || forecastData.length === 0}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Button onClick={handleApplyFilters} disabled={isLoading}>
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {isLoading ? (
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      ) : forecastData.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <p className="text-muted-foreground">No forecast data available</p>
              <p className="text-sm text-muted-foreground mt-2">
                Try adjusting your filters or adding more sales history
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {forecastData.slice(0, 3).map((forecast) => (
              <Card
                key={forecast.productId}
                className="cursor-pointer hover:border-primary/50"
                onClick={() => {
                  setSelectedForecast(forecast)
                  setActiveTab("details")
                }}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">{forecast.productName}</CardTitle>
                  <CardDescription>{forecast.sku}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Current Stock</p>
                      <p className="text-2xl font-bold">{forecast.currentStock}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Daily Sales</p>
                      <p className="text-2xl font-bold">{forecast.averageDailySales.toFixed(1)}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Days until reorder:</span>
                      <span className="font-medium">
                        {forecast.daysUntilStockout === null ? "N/A" : `${forecast.daysUntilStockout} days`}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Recommended order:</span>
                      <span className="font-medium">{forecast.recommendedOrder}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">{getConfidenceBadge(forecast.confidence)}</CardFooter>
              </Card>
            ))}
          </div>

          {selectedForecast && (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{selectedForecast.productName}</CardTitle>
                    <CardDescription>{selectedForecast.sku}</CardDescription>
                  </div>
                  {getConfidenceBadge(selectedForecast.confidence)}
                </div>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="mb-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="details">Forecast Details</TabsTrigger>
                    <TabsTrigger value="historical">Historical Data</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        {renderStockStatus(selectedForecast)}

                        <div className="grid grid-cols-2 gap-4">
                          <div className="border rounded-lg p-4">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                              <Package className="h-4 w-4" />
                              <span>Current Stock</span>
                            </div>
                            <p className="text-2xl font-bold">{selectedForecast.currentStock}</p>
                            <p className="text-xs text-muted-foreground">
                              Reorder Point: {selectedForecast.reorderPoint}
                            </p>
                          </div>

                          <div className="border rounded-lg p-4">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                              <TrendingUp className="h-4 w-4" />
                              <span>Avg. Daily Sales</span>
                            </div>
                            <p className="text-2xl font-bold">{selectedForecast.averageDailySales.toFixed(1)}</p>
                            <p className="text-xs text-muted-foreground">
                              Based on {selectedForecast.historicalData.filter((d) => d.sales > 0).length} days
                            </p>
                          </div>

                          <div className="border rounded-lg p-4">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                              <Calendar className="h-4 w-4" />
                              <span>Days Until Reorder</span>
                            </div>
                            <p className="text-2xl font-bold">
                              {selectedForecast.daysUntilStockout === null ? "N/A" : selectedForecast.daysUntilStockout}
                            </p>
                            {selectedForecast.projectedStockDate && (
                              <p className="text-xs text-muted-foreground">
                                Projected date: {format(new Date(selectedForecast.projectedStockDate), "MMM d, yyyy")}
                              </p>
                            )}
                          </div>

                          <div className="border rounded-lg p-4">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                              <TrendingDown className="h-4 w-4" />
                              <span>Recommended Order</span>
                            </div>
                            <p className="text-2xl font-bold">{selectedForecast.recommendedOrder}</p>
                            <p className="text-xs text-muted-foreground">For 30 days of inventory</p>
                          </div>
                        </div>
                      </div>

                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={[
                              ...selectedForecast.historicalData.slice(-30),
                              ...selectedForecast.forecastData.slice(0, 30),
                            ]}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                              dataKey="date"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => format(new Date(value), "MMM d")}
                            />
                            <YAxis />
                            <Tooltip
                              labelFormatter={(value) => format(new Date(value), "MMM d, yyyy")}
                              formatter={(value: any) => [value, "Units"]}
                            />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="stock"
                              stroke="#3b82f6"
                              name="Historical Stock"
                              strokeWidth={2}
                              dot={false}
                              activeDot={{ r: 6 }}
                            />
                            <Line
                              type="monotone"
                              dataKey="projectedStock"
                              stroke="#10b981"
                              name="Projected Stock"
                              strokeWidth={2}
                              strokeDasharray="5 5"
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="details">
                    <div className="space-y-6">
                      <div className="h-[400px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={selectedForecast.forecastData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                              dataKey="date"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => format(new Date(value), "MMM d")}
                            />
                            <YAxis />
                            <Tooltip
                              labelFormatter={(value) => format(new Date(value), "MMM d, yyyy")}
                              formatter={(value: any) => [value, "Units"]}
                            />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="projectedStock"
                              stroke="#3b82f6"
                              name="Projected Stock"
                              strokeWidth={2}
                              dot={false}
                            />
                            <Line
                              type="monotone"
                              dataKey="projectedSales"
                              stroke="#ef4444"
                              name="Projected Sales"
                              strokeWidth={2}
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>

                      <div className="border rounded-lg p-4">
                        <h3 className="text-lg font-medium mb-2">Forecast Methodology</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          This forecast is based on historical sales data and uses a simple moving average model to
                          predict future inventory levels. The confidence level is determined by the amount and quality
                          of historical data available.
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <h4 className="text-sm font-medium">Data Points</h4>
                            <p className="text-2xl font-bold">{selectedForecast.historicalData.length}</p>
                            <p className="text-xs text-muted-foreground">Days of historical data</p>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium">Sales Days</h4>
                            <p className="text-2xl font-bold">
                              {selectedForecast.historicalData.filter((d) => d.sales > 0).length}
                            </p>
                            <p className="text-xs text-muted-foreground">Days with recorded sales</p>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium">Confidence</h4>
                            <p className="text-2xl font-bold capitalize">{selectedForecast.confidence}</p>
                            <p className="text-xs text-muted-foreground">Forecast reliability</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="historical">
                    <div className="space-y-6">
                      <div className="h-[400px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={selectedForecast.historicalData.slice(-30)}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                              dataKey="date"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => format(new Date(value), "MMM d")}
                            />
                            <YAxis />
                            <Tooltip
                              labelFormatter={(value) => format(new Date(value), "MMM d, yyyy")}
                              formatter={(value: any) => [value, "Units"]}
                            />
                            <Legend />
                            <Bar dataKey="sales" fill="#ef4444" name="Daily Sales" />
                            <Bar dataKey="stock" fill="#3b82f6" name="Stock Level" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>

                      <div className="border rounded-lg p-4">
                        <h3 className="text-lg font-medium mb-2">Historical Data Analysis</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          This chart shows the historical sales and stock levels for the past 30 days. This data is used
                          to calculate the average daily sales and project future inventory levels.
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <h4 className="text-sm font-medium">Total Sales</h4>
                            <p className="text-2xl font-bold">
                              {selectedForecast.historicalData.reduce((sum, item) => sum + item.sales, 0)}
                            </p>
                            <p className="text-xs text-muted-foreground">Units sold in period</p>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium">Peak Sales Day</h4>
                            <p className="text-2xl font-bold">
                              {Math.max(...selectedForecast.historicalData.map((item) => item.sales))}
                            </p>
                            <p className="text-xs text-muted-foreground">Maximum daily sales</p>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium">Stock Trend</h4>
                            <p className="text-2xl font-bold">
                              {selectedForecast.historicalData[0].stock <
                              selectedForecast.historicalData[selectedForecast.historicalData.length - 1].stock
                                ? "Increasing"
                                : "Decreasing"}
                            </p>
                            <p className="text-xs text-muted-foreground">Overall stock direction</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  )
}

