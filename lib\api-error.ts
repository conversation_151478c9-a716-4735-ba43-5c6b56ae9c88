export class ApiError extends Error {
  status: number
  code?: string

  constructor(message: string, status: number, code?: string) {
    super(message)
    this.name = "ApiError"
    this.status = status
    this.code = code
  }

  static badRequest(message = "Bad Request", code?: string) {
    return new ApiError(message, 400, code)
  }

  static unauthorized(message = "Unauthorized", code?: string) {
    return new ApiError(message, 401, code)
  }

  static forbidden(message = "Forbidden", code?: string) {
    return new ApiError(message, 403, code)
  }

  static notFound(message = "Not Found", code?: string) {
    return new ApiError(message, 404, code)
  }

  static methodNotAllowed(message = "Method Not Allowed", code?: string) {
    return new ApiError(message, 405, code)
  }

  static conflict(message = "Conflict", code?: string) {
    return new ApiError(message, 409, code)
  }

  static unprocessableEntity(message = "Unprocessable Entity", code?: string) {
    return new ApiError(message, 422, code)
  }

  static tooManyRequests(message = "Too Many Requests", code?: string) {
    return new ApiError(message, 429, code)
  }

  static internal(message = "Internal Server Error", code?: string) {
    return new ApiError(message, 500, code)
  }
}

export function handleApiError(error: unknown): Response {
  console.error("API Error:", error)

  if (error instanceof ApiError) {
    return Response.json(
      {
        error: {
          message: error.message,
          code: error.code,
        },
      },
      { status: error.status },
    )
  }

  if (error instanceof Error) {
    return Response.json(
      {
        error: {
          message: error.message || "An unexpected error occurred",
        },
      },
      { status: 500 },
    )
  }

  return Response.json(
    {
      error: {
        message: "An unexpected error occurred",
      },
    },
    { status: 500 },
  )
}

