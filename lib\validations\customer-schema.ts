import { z } from "zod"

export const customerSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(100, "First name cannot exceed 100 characters"),
  lastName: z
    .string()
    .min(1, "Last name is required")
    .max(100, "Last name cannot exceed 100 characters")
    .optional()
    .or(z.literal("")),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  notes: z.string().optional(),
  type: z
    .enum(["INDIVIDUAL", "BUSINESS"], {
      invalid_type_error: "Invalid customer type",
    })
    .optional(),
  // Loyalty Program
  loyaltyPoints: z.number().min(0, "Loyalty points cannot be negative").optional(),
  // Credit Management
  storeCredit: z.number().min(0, "Store credit cannot be negative").optional(),
  creditLimit: z.number().min(0, "Credit limit cannot be negative").optional().nullable(),
  creditHold: z.boolean().optional(),
})

export type CustomerFormValues = z.infer<typeof customerSchema>

