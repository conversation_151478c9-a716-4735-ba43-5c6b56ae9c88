import type { StateCreator } from "zustand"
import type { Role } from "@prisma/client"

export interface User {
  id: string
  name?: string | null
  email?: string | null
  role: Role
}

export interface AuthSlice {
  isAuthenticated: boolean
  user: User | null
  setAuth: (isAuthenticated: boolean, user: User | null) => void
  clearAuth: () => void
  hasPermission: (allowedRoles: Role[]) => boolean
}

export const createAuthSlice: StateCreator<AuthSlice> = (set, get) => ({
  isAuthenticated: false,
  user: null,

  setAuth: (isAuthenticated, user) => {
    set({ isAuthenticated, user })
  },

  clearAuth: () => {
    set({ isAuthenticated: false, user: null })
  },

  hasPermission: (allowedRoles) => {
    const { isAuthenticated, user } = get()
    if (!isAuthenticated || !user) return false
    return allowedRoles.includes(user.role)
  },
})

