"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"
import { Loader2, Plus, Award, TrendingUp, TrendingDown } from "lucide-react"
import { format } from "date-fns"

interface LoyaltyTransaction {
  id: string
  points: number
  type: string
  description: string
  createdAt: string
}

interface CustomerLoyaltyProps {
  customerId: string
}

export function CustomerLoyalty({ customerId }: CustomerLoyaltyProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [customer, setCustomer] = useState<any>(null)
  const [transactions, setTransactions] = useState<LoyaltyTransaction[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newTransaction, setNewTransaction] = useState({
    points: 0,
    type: "EARN",
    description: "",
  })

  const fetchLoyaltyData = async () => {
    try {
      setIsLoading(true)
      const response = await fetchApi(`/api/customers/${customerId}/loyalty`)
      setCustomer(response.customer)
      setTransactions(response.transactions)
    } catch (error) {
      console.error("Failed to fetch loyalty data:", error)
      toast({
        title: "Error",
        description: "Failed to load loyalty program data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchLoyaltyData()
  }, [customerId, toast])

  const handleAddTransaction = async () => {
    try {
      await fetchApi(`/api/customers/${customerId}/loyalty`, {
        method: "POST",
        body: JSON.stringify(newTransaction),
      })

      toast({
        title: "Success",
        description: "Loyalty transaction added successfully",
      })

      setIsAddDialogOpen(false)
      setNewTransaction({
        points: 0,
        type: "EARN",
        description: "",
      })

      fetchLoyaltyData()
    } catch (error) {
      console.error("Failed to add loyalty transaction:", error)
      toast({
        title: "Error",
        description: "Failed to add loyalty transaction",
        variant: "destructive",
      })
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "EARN":
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case "REDEEM":
        return <TrendingDown className="h-4 w-4 text-amber-500" />
      case "EXPIRE":
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case "ADJUST":
        return <Award className="h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Loyalty Program</CardTitle>
              <CardDescription>Manage customer loyalty points and rewards</CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Points
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Loyalty Transaction</DialogTitle>
                  <DialogDescription>Add or remove loyalty points for this customer.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="points" className="text-right">
                      Points
                    </Label>
                    <Input
                      id="points"
                      type="number"
                      className="col-span-3"
                      value={newTransaction.points}
                      onChange={(e) =>
                        setNewTransaction({ ...newTransaction, points: Number.parseInt(e.target.value) })
                      }
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="type" className="text-right">
                      Type
                    </Label>
                    <Select
                      value={newTransaction.type}
                      onValueChange={(value) => setNewTransaction({ ...newTransaction, type: value })}
                    >
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="EARN">Earn</SelectItem>
                        <SelectItem value="REDEEM">Redeem</SelectItem>
                        <SelectItem value="EXPIRE">Expire</SelectItem>
                        <SelectItem value="ADJUST">Adjust</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="description" className="text-right">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      className="col-span-3"
                      value={newTransaction.description}
                      onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" onClick={handleAddTransaction}>
                    Add Transaction
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Current Points</div>
              <div className="mt-2 text-3xl font-bold">{customer?.loyaltyPoints || 0}</div>
            </div>

            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Loyalty Tier</div>
              <div className="mt-2 flex items-center">
                <Award className="mr-2 h-5 w-5 text-amber-500" />
                <span className="text-xl font-semibold">{customer?.loyaltyTier || "None"}</span>
              </div>
            </div>

            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Lifetime Points</div>
              <div className="mt-2 text-3xl font-bold">
                {transactions.reduce((sum, t) => sum + (t.type === "EARN" ? t.points : 0), 0)}
              </div>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-medium mb-4">Transaction History</h3>
            {transactions.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead>Description</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>{format(new Date(transaction.createdAt), "MMM d, yyyy")}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {getTransactionIcon(transaction.type)}
                            <Badge
                              variant={
                                transaction.type === "EARN"
                                  ? "default"
                                  : transaction.type === "REDEEM"
                                    ? "secondary"
                                    : transaction.type === "EXPIRE"
                                      ? "destructive"
                                      : "outline"
                              }
                              className="ml-2"
                            >
                              {transaction.type}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span
                            className={
                              transaction.type === "EARN"
                                ? "text-green-600"
                                : transaction.type === "REDEEM" || transaction.type === "EXPIRE"
                                  ? "text-red-600"
                                  : ""
                            }
                          >
                            {transaction.type === "EARN"
                              ? "+"
                              : transaction.type === "REDEEM" || transaction.type === "EXPIRE"
                                ? "-"
                                : ""}
                            {transaction.points}
                          </span>
                        </TableCell>
                        <TableCell>{transaction.description}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">No transactions found</div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

