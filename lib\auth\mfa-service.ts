import { authenticator } from "otplib"
import { db } from "../db"
import crypto from "crypto"

export enum MfaMethod {
  AUTHENTICATOR = "authenticator",
  EMAIL = "email",
  SMS = "sms",
}

export interface MfaSetup {
  userId: string
  method: MfaMethod
  secret?: string
  phoneNumber?: string
  verified: boolean
  createdAt: Date
  updatedAt: Date
}

export class MfaService {
  /**
   * Generate a new TOTP secret for a user
   */
  static generateSecret(userId: string): {
    secret: string
    uri: string
    qrCode: string
  } {
    const secret = authenticator.generateSecret()
    const uri = authenticator.keyuri(userId, "StockSync", secret)

    // In a real implementation, you would generate a QR code image
    // For simplicity, we're just returning the URI
    const qrCode = uri

    return { secret, uri, qrCode }
  }

  /**
   * Start the MFA setup process for a user
   */
  static async setupMfa(userId: string, method: MfaMethod, phoneNumber?: string): Promise<MfaSetup> {
    // Check if the user already has this MFA method
    const existingSetup = await db.mfaSetups.findFirst({
      where: {
        userId,
        method,
      },
    })

    if (existingSetup) {
      throw new Error(`User already has ${method} MFA set up`)
    }

    let secret: string | undefined

    if (method === MfaMethod.AUTHENTICATOR) {
      const { secret: newSecret } = this.generateSecret(userId)
      secret = newSecret
    }

    // Create the MFA setup record
    return db.mfaSetups.create({
      data: {
        userId,
        method,
        secret,
        phoneNumber,
        verified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })
  }

  /**
   * Verify an MFA setup with a code
   */
  static async verifyMfaSetup(userId: string, method: MfaMethod, code: string): Promise<boolean> {
    const setup = await db.mfaSetups.findFirst({
      where: {
        userId,
        method,
      },
    })

    if (!setup) {
      throw new Error(`No ${method} MFA setup found for user`)
    }

    let isValid = false

    if (method === MfaMethod.AUTHENTICATOR) {
      if (!setup.secret) {
        throw new Error("No secret found for authenticator setup")
      }

      isValid = authenticator.verify({
        token: code,
        secret: setup.secret,
      })
    } else if (method === MfaMethod.EMAIL || method === MfaMethod.SMS) {
      // In a real implementation, you would verify against a stored code
      // For simplicity, we're just checking if the code is '123456'
      isValid = code === "123456"
    }

    if (isValid) {
      // Mark the setup as verified
      await db.mfaSetups.update({
        where: { id: setup.id },
        data: {
          verified: true,
          updatedAt: new Date(),
        },
      })
    }

    return isValid
  }

  /**
   * Generate and send a verification code for email or SMS
   */
  static async generateAndSendCode(userId: string, method: MfaMethod): Promise<void> {
    if (method !== MfaMethod.EMAIL && method !== MfaMethod.SMS) {
      throw new Error(`Cannot generate code for ${method}`)
    }

    const setup = await db.mfaSetups.findFirst({
      where: {
        userId,
        method,
      },
    })

    if (!setup) {
      throw new Error(`No ${method} MFA setup found for user`)
    }

    // Generate a 6-digit code
    const code = Math.floor(100000 + Math.random() * 900000).toString()

    // Store the code (in a real implementation, you would hash it)
    await db.mfaCodes.create({
      data: {
        userId,
        method,
        code,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
      },
    })

    // Send the code (in a real implementation)
    if (method === MfaMethod.EMAIL) {
      // Send email with code
      console.log(`Sending email with code ${code}`)
    } else if (method === MfaMethod.SMS) {
      // Send SMS with code
      console.log(`Sending SMS with code ${code} to ${setup.phoneNumber}`)
    }
  }

  /**
   * Verify a code for email or SMS
   */
  static async verifyCode(userId: string, method: MfaMethod, code: string): Promise<boolean> {
    if (method !== MfaMethod.EMAIL && method !== MfaMethod.SMS) {
      throw new Error(`Cannot verify code for ${method}`)
    }

    // Find the most recent unexpired code
    const storedCode = await db.mfaCodes.findFirst({
      where: {
        userId,
        method,
        expiresAt: {
          gt: new Date(),
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    if (!storedCode) {
      return false
    }

    const isValid = storedCode.code === code

    if (isValid) {
      // Delete the code to prevent reuse
      await db.mfaCodes.delete({
        where: { id: storedCode.id },
      })
    }

    return isValid
  }

  /**
   * Get all MFA methods for a user
   */
  static async getUserMfaMethods(userId: string): Promise<MfaSetup[]> {
    return db.mfaSetups.findMany({
      where: {
        userId,
        verified: true,
      },
    })
  }

  /**
   * Check if a user has MFA enabled
   */
  static async hasMfaEnabled(userId: string): Promise<boolean> {
    const methods = await this.getUserMfaMethods(userId)
    return methods.length > 0
  }

  /**
   * Disable an MFA method for a user
   */
  static async disableMfa(userId: string, method: MfaMethod): Promise<void> {
    await db.mfaSetups.deleteMany({
      where: {
        userId,
        method,
      },
    })
  }

  /**
   * Generate recovery codes for a user
   */
  static async generateRecoveryCodes(userId: string): Promise<string[]> {
    // Generate 10 recovery codes
    const codes = Array.from({ length: 10 }, () => {
      // Generate a random 10-character code
      return crypto.randomBytes(5).toString("hex")
    })

    // Hash the codes for storage
    const hashedCodes = codes.map((code) => {
      return crypto.createHash("sha256").update(code).digest("hex")
    })

    // Store the hashed codes
    for (const hashedCode of hashedCodes) {
      await db.recoveryCode.create({
        data: {
          userId,
          code: hashedCode,
          used: false,
          createdAt: new Date(),
        },
      })
    }

    // Return the plain text codes to the user
    return codes
  }

  /**
   * Verify a recovery code
   */
  static async verifyRecoveryCode(userId: string, code: string): Promise<boolean> {
    // Hash the provided code
    const hashedCode = crypto.createHash("sha256").update(code).digest("hex")

    // Find the recovery code
    const recoveryCode = await db.recoveryCode.findFirst({
      where: {
        userId,
        code: hashedCode,
        used: false,
      },
    })

    if (!recoveryCode) {
      return false
    }

    // Mark the code as used
    await db.recoveryCode.update({
      where: { id: recoveryCode.id },
      data: { used: true },
    })

    return true
  }
}

