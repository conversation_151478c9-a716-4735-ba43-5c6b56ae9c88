"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "@/hooks/use-toast"

const archiveFormSchema = z.object({
  entityType: z.string().min(1, "Entity type is required"),
  olderThan: z.date({
    required_error: "Date is required",
  }),
  deleteAfterArchive: z.boolean().default(false),
})

type ArchiveFormValues = z.infer<typeof archiveFormSchema>

export function DataArchivingPanel() {
  const [activeTab, setActiveTab] = useState("archive")
  const [archives, setArchives] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [archiving, setArchiving] = useState(false)
  const [restoring, setRestoring] = useState<string | null>(null)
  const [deleting, setDeleting] = useState<string | null>(null)
  const [archiveResult, setArchiveResult] = useState<any>(null)
  
  const archiveForm = useForm<ArchiveFormValues>({
    resolver: zodResolver(archiveFormSchema),
    defaultValues: {
      entityType: "",
      olderThan: new Date(),
      deleteAfterArchive: false
    }
  })
  
  const loadArchives = async () => {
    setLoading(true)
    
    try {
      const response = await fetch("/api/db/archive")
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to load archives")
      }
      
      setArchives(result.archives)
    } catch (error) {
      console.error("Load archives error:", error)
      
      toast({
        title: "Failed to load archives",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }
  
  const handleArchive = async (values: ArchiveFormValues) => {
    setArchiving(true)
    setArchiveResult(null)
    
    try {
      const response = await fetch("/api/db/archive", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          entityType: values.entityType,
          olderThan: values.olderThan.toISOString(),
          deleteAfterArchive: values.deleteAfterArchive
        })
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to archive data")
      }
      
      setArchiveResult(result)
      
      toast({
        title: "Archive successful",
        description: `Archived ${result.recordCount} records`,
        variant: "default"
      })
      
      // Reload archives
      loadArchives()
    } catch (error) {
      console.error("Archive error:", error)
      
      toast({
        title: "Archive failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setArchiving(false)
    }
  }
  
  const handleRestore = async (archiveId: string) => {
    setRestoring(archiveId)
    
    try {
      const response = await fetch(`/api/db/archive/${archiveId}/restore`, {
        method: "POST"
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to restore archive")
      }
      
      toast({
        title: "Restore successful",
        description: `Restored ${result.recordCount} records`,
        variant: "default"
      })
      
      // Reload archives\
      loadArch  records`,
        variant: "default"
      })
      
      // Reload archives
      loadArchives()
    } catch (error) {
      console.error("Restore error:", error)
      
      toast({
        title: "Restore failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setRestoring(null)
    }
  }
  
  const handleDelete = async (archiveId: string) => {
    setDeleting(archiveId)
    
    try {
      const response = await fetch(`/api/db/archive/$archiveId`, {
        method: "DELETE"
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to delete archive")
      }
      
      toast({
        title: "Delete successful",
        description: result.message,
        variant: "default"
      })
      
      // Reload archives
      loadArchives()
    } catch (error) {
      console.error("Delete error:", error)
      
      toast({
        title: "Delete failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setDeleting(null)
    }
  }
  
  // Load archives when tab changes to "manage"
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    
    if (value === "manage") {
      loadArchives()
    }
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Data Archiving</CardTitle>
        <CardDescription>Archive and manage historical data</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="archive">Archive Data</TabsTrigger>
            <TabsTrigger value="manage">Manage Archives</TabsTrigger>
          </TabsList>
          
          <TabsContent value="archive" className="space-y-4 pt-4">
            <form onSubmit={archiveForm.handleSubmit(handleArchive)}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="entityType">Entity Type</Label>
                  <Select
                    onValueChange={(value) => archiveForm.setValue("entityType", value)}
                    defaultValue={archiveForm.watch("entityType")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select entity type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="orders">Orders</SelectItem>
                      <SelectItem value="products">Products</SelectItem>
                      <SelectItem value="customers">Customers</SelectItem>
                      <SelectItem value="notifications">Notifications</SelectItem>
                      <SelectItem value="inventoryHistory">Inventory History</SelectItem>
                    </SelectContent>
                  </Select>
                  {archiveForm.formState.errors.entityType && (
                    <p className="text-sm text-red-500">{archiveForm.formState.errors.entityType.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="olderThan">Older Than</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !archiveForm.watch("olderThan") && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {archiveForm.watch("olderThan") ? (
                          format(archiveForm.watch("olderThan"), "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={archiveForm.watch("olderThan")}
                        onSelect={(date) => date && archiveForm.setValue("olderThan", date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {archiveForm.formState.errors.olderThan && (
                    <p className="text-sm text-red-500">{archiveForm.formState.errors.olderThan.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="deleteAfterArchive"
                      checked={archiveForm.watch("deleteAfterArchive")}
                      onCheckedChange={(checked) => archiveForm.setValue("deleteAfterArchive", checked as boolean)}
                    />
                    <Label htmlFor="deleteAfterArchive">Delete data after archiving</Label>
                  </div>
                </div>
                
                <Alert variant="warning">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <AlertTitle>Warning</AlertTitle>
                  <AlertDescription>
                    Archiving data will move it to long-term storage. If you choose to delete after archiving,
                    the data will no longer be available in the main database.
                  </AlertDescription>
                </Alert>
                
                {archiveResult && (
                  <Alert variant={archiveResult.success ? "default" : "destructive"}>
                    <div className="flex items-center">
                      {archiveResult.success ? (
                        <CheckCircle className="h-4 w-4 mr-2" />
                      ) : (
                        <AlertCircle className="h-4 w-4 mr-2" />
                      )}
                      <AlertTitle>
                        {archiveResult.success ? "Archive Successful" : "Archive Failed"}
                      </AlertTitle>
                    </div>
                    <AlertDescription>
                      {archiveResult.success ? (
                        <div className="mt-2">
                          <p>Successfully archived {archiveResult.recordCount} records.</p>
                        </div>
                      ) : (
                        <p>{archiveResult.error?.message || "An unexpected error occurred"}</p>
                      )}
                    </AlertDescription>
                  </Alert>
                )}
                
                <Button type="submit" disabled={archiving}>
                  {archiving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Archiving...
                    </>
                  ) : (
                    <>
                      <Archive className="mr-2 h-4 w-4" />
                      Archive Data
                    </>
                  )}
                </Button>
              </div>
            </form>
          </TabsContent>
          
          <TabsContent value="manage" className="space-y-4 pt-4">
            <div className="space-y-4">
              <div className="flex justify-end">
                <Button variant="outline" onClick={loadArchives} disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Refresh
                    </>
                  )}
                </Button>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Entity Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Record Count</TableHead>
                    <TableHead>Created At</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {archives.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center">
                        No archives found
                      </TableCell>
                    </TableRow>
                  ) : (
                    archives.map((archive) => (
                      <TableRow key={archive.id}>
                        <TableCell>{archive.name}</TableCell>
                        <TableCell>{archive.entityType}</TableCell>
                        <TableCell>{archive.status}</TableCell>
                        <TableCell>{archive.recordCount || "N/A"}</TableCell>
                        <TableCell>{format(new Date(archive.createdAt), "PPP")}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRestore(archive.id)}
                              disabled={restoring === archive.id || archive.status === "RESTORING"}
                            >
                              {restoring === archive.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <RotateCcw className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(archive.id)}
                              disabled={deleting === archive.id}
                            >
                              {deleting === archive.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

