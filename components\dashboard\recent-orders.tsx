"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useSocket } from "@/components/socket-provider"
import { formatDistanceToNow } from "date-fns"

interface Order {
  id: string
  orderNumber: string
  customer: {
    firstName: string
    lastName: string
  }
  total: number
  status: string
  createdAt: string
}

export function RecentOrders() {
  const [orders, setOrders] = useState<Order[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { emit, socket, status } = useSocket()

  useEffect(() => {
    if (status === "connected") {
      setIsLoading(true)

      const syncId = `recent_orders_sync_${Date.now()}`

      const handleSyncResponse = (response: any) => {
        if (response.id === syncId) {
          setOrders(response.data.orders)
          setIsLoading(false)

          // Remove the event listener
          socket?.off("sync_response", handleSyncResponse)
        }
      }

      // Listen for the response
      socket?.on("sync_response", handleSyncResponse)

      // Request recent orders
      emit("sync_request", {
        id: syncId,
        timestamp: Date.now(),
        type: "recent_orders",
        limit: 5,
      })

      // Fallback in case we don't get a response
      const timeout = setTimeout(() => {
        if (isLoading) {
          // Use mock data if we don't get a response
          setOrders([
            {
              id: "ord1",
              orderNumber: "ORD-1001",
              customer: { firstName: "John", lastName: "Doe" },
              total: 79.99,
              status: "COMPLETED",
              createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
            },
            {
              id: "ord2",
              orderNumber: "ORD-1002",
              customer: { firstName: "Jane", lastName: "Smith" },
              total: 149.95,
              status: "PROCESSING",
              createdAt: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
            },
            {
              id: "ord3",
              orderNumber: "ORD-1003",
              customer: { firstName: "Robert", lastName: "Johnson" },
              total: 24.99,
              status: "COMPLETED",
              createdAt: new Date(Date.now() - 1000 * 60 * 90).toISOString(), // 1.5 hours ago
            },
            {
              id: "ord4",
              orderNumber: "ORD-1004",
              customer: { firstName: "Emily", lastName: "Williams" },
              total: 199.99,
              status: "PENDING",
              createdAt: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
            },
            {
              id: "ord5",
              orderNumber: "ORD-1005",
              customer: { firstName: "Michael", lastName: "Brown" },
              total: 59.95,
              status: "COMPLETED",
              createdAt: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago
            },
          ])
          setIsLoading(false)
          socket?.off("sync_response", handleSyncResponse)
        }
      }, 3000)

      return () => {
        clearTimeout(timeout)
        socket?.off("sync_response", handleSyncResponse)
      }
    }
  }, [status, emit, socket, isLoading])

  // Listen for new orders
  useEffect(() => {
    if (!socket) return

    const handleNewOrder = (data: any) => {
      const newOrder = data.data

      // Add the new order to the top of the list and remove the last one
      setOrders((prev) => [
        {
          id: newOrder.id,
          orderNumber: newOrder.orderNumber,
          customer: newOrder.customer || { firstName: "Guest", lastName: "" },
          total: newOrder.total,
          status: newOrder.status,
          createdAt: new Date().toISOString(),
        },
        ...prev.slice(0, 4),
      ])
    }

    socket.on("order_created", handleNewOrder)

    return () => {
      socket.off("order_created", handleNewOrder)
    }
  }, [socket])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return "bg-green-500"
      case "PROCESSING":
        return "bg-blue-500"
      case "PENDING":
        return "bg-yellow-500"
      case "CANCELLED":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle>Recent Orders</CardTitle>
        <CardDescription>Latest orders across your stores</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {isLoading
            ? Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center">
                  <Skeleton className="h-12 w-full" />
                </div>
              ))
            : orders.map((order) => (
                <div key={order.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="space-y-1">
                      <p className="text-sm font-medium leading-none">{order.orderNumber}</p>
                      <p className="text-sm text-muted-foreground">
                        {order.customer.firstName} {order.customer.lastName}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm font-medium">${order.total.toFixed(2)}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(order.createdAt), { addSuffix: true })}
                      </p>
                    </div>
                    <Badge className={getStatusColor(order.status)}>
                      {order.status.charAt(0) + order.status.slice(1).toLowerCase()}
                    </Badge>
                  </div>
                </div>
              ))}
        </div>
      </CardContent>
    </Card>
  )
}

