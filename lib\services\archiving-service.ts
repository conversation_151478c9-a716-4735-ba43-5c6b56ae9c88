import { prisma } from "@/lib/prisma"
import { format } from "date-fns"
import { createGzip } from "zlib"
import { pipeline } from "stream/promises"
import { createReadStream, createWriteStream } from "fs"
import { mkdir } from "fs/promises"
import path from "path"

// Define archive directory
const ARCHIVE_DIR = process.env.ARCHIVE_DIR || path.join(process.cwd(), "archives")

/**
 * Service for data archiving
 */
export class ArchivingService {
  /**
   * Archive old data
   */
  static async archiveOldData(options: {
    entityType: string
    olderThan: Date
    userId: string
    deleteAfterArchive?: boolean
  }) {
    const { entityType, olderThan, userId, deleteAfterArchive = false } = options

    try {
      // Ensure archive directory exists
      await this.ensureArchiveDir()

      // Create archive record
      const archive = await prisma.archive.create({
        data: {
          name: `${entityType}-${format(new Date(), "yyyy-MM-dd-HH-mm-ss")}`,
          entityType,
          status: "PROCESSING",
          userId,
        },
      })

      // Archive data based on entity type
      const result = await this.archiveEntityData(entityType, olderThan, archive.id, deleteAfterArchive)

      // Update archive record
      await prisma.archive.update({
        where: { id: archive.id },
        data: {
          status: "COMPLETED",
          recordCount: result.recordCount,
          fileSize: result.fileSize,
          filePath: result.filePath,
          completedAt: new Date(),
        },
      })

      return {
        success: true,
        archiveId: archive.id,
        recordCount: result.recordCount,
        fileSize: result.fileSize,
      }
    } catch (error) {
      console.error(`Error archiving ${entityType}:`, error)

      // Update archive record with error
      await prisma.archive.update({
        where: { id: options.archiveId },
        data: {
          status: "FAILED",
          error: error instanceof Error ? error.message : "Unknown error",
          completedAt: new Date(),
        },
      })

      throw error
    }
  }

  /**
   * Restore archived data
   */
  static async restoreArchivedData(archiveId: string) {
    try {
      // Get archive record
      const archive = await prisma.archive.findUnique({
        where: { id: archiveId },
      })

      if (!archive) {
        throw new Error(`Archive with ID ${archiveId} not found`)
      }

      // Update archive status
      await prisma.archive.update({
        where: { id: archiveId },
        data: {
          status: "RESTORING",
        },
      })

      // Restore data based on entity type
      const result = await this.restoreEntityData(archive)

      // Update archive record
      await prisma.archive.update({
        where: { id: archiveId },
        data: {
          status: "RESTORED",
          restoredAt: new Date(),
        },
      })

      return {
        success: true,
        recordCount: result.recordCount,
      }
    } catch (error) {
      console.error(`Error restoring archive:`, error)

      // Update archive record with error
      await prisma.archive.update({
        where: { id: archiveId },
        data: {
          status: "FAILED",
          error: error instanceof Error ? error.message : "Unknown error",
        },
      })

      throw error
    }
  }

  /**
   * Get all archives
   */
  static async getArchives(options?: {
    entityType?: string
    status?: string
  }) {
    const { entityType, status } = options || {}

    const where: any = {}

    if (entityType) {
      where.entityType = entityType
    }

    if (status) {
      where.status = status
    }

    const archives = await prisma.archive.findMany({
      where,
      orderBy: { createdAt: "desc" },
    })

    return {
      success: true,
      archives,
    }
  }

  /**
   * Delete archive
   */
  static async deleteArchive(archiveId: string) {
    try {
      // Get archive record
      const archive = await prisma.archive.findUnique({
        where: { id: archiveId },
      })

      if (!archive) {
        throw new Error(`Archive with ID ${archiveId} not found`)
      }

      // Delete archive file
      if (archive.filePath) {
        const fs = require("fs")
        if (fs.existsSync(archive.filePath)) {
          fs.unlinkSync(archive.filePath)
        }
      }

      // Delete archive record
      await prisma.archive.delete({
        where: { id: archiveId },
      })

      return {
        success: true,
        message: `Archive ${archive.name} deleted successfully`,
      }
    } catch (error) {
      console.error(`Error deleting archive:`, error)
      throw error
    }
  }

  /**
   * Ensure archive directory exists
   */
  private static async ensureArchiveDir() {
    try {
      await mkdir(ARCHIVE_DIR, { recursive: true })
    } catch (error) {
      console.error("Failed to create archive directory:", error)
      throw new Error("Failed to create archive directory")
    }
  }

  /**
   * Archive entity data
   */
  private static async archiveEntityData(
    entityType: string,
    olderThan: Date,
    archiveId: string,
    deleteAfterArchive: boolean,
  ) {
    // Get data to archive
    const data = await this.getDataToArchive(entityType, olderThan)

    if (data.length === 0) {
      return {
        recordCount: 0,
        fileSize: 0,
        filePath: null,
      }
    }

    // Create archive file path
    const filePath = path.join(
      ARCHIVE_DIR,
      `${entityType}-${format(new Date(), "yyyy-MM-dd-HH-mm-ss")}-${archiveId}.json.gz`,
    )

    // Write data to file with compression
    const jsonData = JSON.stringify(data, null, 2)

    // Create a gzip compressed file
    const gzip = createGzip()
    const source = Buffer.from(jsonData)
    const destination = createWriteStream(filePath)

    // Use pipeline for better error handling
    await pipeline(createReadStream(null, { read: () => source }), gzip, destination)

    // Get file size
    const fs = require("fs")
    const stats = fs.statSync(filePath)
    const fileSize = stats.size

    // Delete archived data if requested
    if (deleteAfterArchive) {
      await this.deleteArchivedData(entityType, olderThan)
    }

    return {
      recordCount: data.length,
      fileSize,
      filePath,
    }
  }

  /**
   * Get data to archive
   */
  private static async getDataToArchive(entityType: string, olderThan: Date) {
    switch (entityType) {
      case "orders":
        return prisma.order.findMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
            status: {
              in: ["COMPLETED", "CANCELLED"],
            },
          },
          include: {
            items: true,
            statusHistory: true,
            customer: true,
          },
        })
      case "products":
        return prisma.product.findMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
            isActive: false,
          },
          include: {
            category: true,
            supplier: true,
            images: true,
            variants: true,
          },
        })
      case "customers":
        return prisma.customer.findMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
            isActive: false,
          },
        })
      case "notifications":
        return prisma.notification.findMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
            read: true,
          },
        })
      case "inventoryHistory":
        return prisma.inventoryHistory.findMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
          },
          include: {
            product: true,
          },
        })
      default:
        throw new Error(`Unsupported entity type for archiving: ${entityType}`)
    }
  }

  /**
   * Delete archived data
   */
  private static async deleteArchivedData(entityType: string, olderThan: Date) {
    switch (entityType) {
      case "orders":
        // Delete order items first
        await prisma.orderItem.deleteMany({
          where: {
            order: {
              createdAt: {
                lt: olderThan,
              },
              status: {
                in: ["COMPLETED", "CANCELLED"],
              },
            },
          },
        })

        // Delete order status history
        await prisma.orderStatusHistory.deleteMany({
          where: {
            order: {
              createdAt: {
                lt: olderThan,
              },
              status: {
                in: ["COMPLETED", "CANCELLED"],
              },
            },
          },
        })

        // Delete orders
        await prisma.order.deleteMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
            status: {
              in: ["COMPLETED", "CANCELLED"],
            },
          },
        })
        break
      case "products":
        // Delete product images first
        await prisma.productImage.deleteMany({
          where: {
            product: {
              createdAt: {
                lt: olderThan,
              },
              isActive: false,
            },
          },
        })

        // Delete product variants
        await prisma.productVariant.deleteMany({
          where: {
            product: {
              createdAt: {
                lt: olderThan,
              },
              isActive: false,
            },
          },
        })

        // Delete products
        await prisma.product.deleteMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
            isActive: false,
          },
        })
        break
      case "customers":
        await prisma.customer.deleteMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
            isActive: false,
          },
        })
        break
      case "notifications":
        await prisma.notification.deleteMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
            read: true,
          },
        })
        break
      case "inventoryHistory":
        await prisma.inventoryHistory.deleteMany({
          where: {
            createdAt: {
              lt: olderThan,
            },
          },
        })
        break
      default:
        throw new Error(`Unsupported entity type for deleting archived data: ${entityType}`)
    }
  }

  /**
   * Restore entity data
   */
  private static async restoreEntityData(archive: any) {
    // Read and decompress the archive file
    const fs = require("fs")
    const zlib = require("zlib")

    if (!archive.filePath || !fs.existsSync(archive.filePath)) {
      throw new Error(`Archive file not found: ${archive.filePath}`)
    }

    // Read and decompress the archive file
    const compressedData = fs.readFileSync(archive.filePath)
    const decompressedData = await new Promise<string>((resolve, reject) => {
      const gunzip = zlib.createGunzip()
      let data = ""

      gunzip.on("data", (chunk) => {
        data += chunk
      })

      gunzip.on("end", () => {
        resolve(data)
      })

      gunzip.on("error", reject)

      gunzip.end(compressedData)
    })

    const data = JSON.parse(decompressedData)

    if (data.length === 0) {
      return {
        recordCount: 0,
      }
    }

    // Restore data based on entity type
    switch (archive.entityType) {
      case "orders":
        await this.restoreOrders(data)
        break
      case "products":
        await this.restoreProducts(data)
        break
      case "customers":
        await this.restoreCustomers(data)
        break
      case "notifications":
        await this.restoreNotifications(data)
        break
      case "inventoryHistory":
        await this.restoreInventoryHistory(data)
        break
      default:
        throw new Error(`Unsupported entity type for restoring: ${archive.entityType}`)
    }

    return {
      recordCount: data.length,
    }
  }

  /**
   * Restore orders
   */
  private static async restoreOrders(data: any[]) {
    for (const order of data) {
      // Check if order already exists
      const existingOrder = await prisma.order.findUnique({
        where: { id: order.id },
      })

      if (!existingOrder) {
        // Create order
        await prisma.order.create({
          data: {
            id: order.id,
            orderNumber: order.orderNumber,
            status: order.status,
            paymentStatus: order.paymentStatus,
            paymentMethod: order.paymentMethod,
            subtotal: order.subtotal,
            tax: order.tax,
            shipping: order.shipping,
            discount: order.discount,
            total: order.total,
            notes: order.notes,
            shippingAddress: order.shippingAddress,
            billingAddress: order.billingAddress,
            fulfillmentDate: order.fulfillmentDate,
            createdAt: new Date(order.createdAt),
            updatedAt: new Date(order.updatedAt),
            customerId: order.customerId,
            userId: order.userId,
          },
        })

        // Create order items
        if (order.items && order.items.length > 0) {
          for (const item of order.items) {
            await prisma.orderItem.create({
              data: {
                id: item.id,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                subtotal: item.subtotal,
                tax: item.tax,
                total: item.total,
                orderId: order.id,
                productId: item.productId,
                variantId: item.variantId,
              },
            })
          }
        }

        // Create order status history
        if (order.statusHistory && order.statusHistory.length > 0) {
          for (const history of order.statusHistory) {
            await prisma.orderStatusHistory.create({
              data: {
                id: history.id,
                status: history.status,
                notes: history.notes,
                createdAt: new Date(history.createdAt),
                orderId: order.id,
              },
            })
          }
        }
      }
    }
  }

  /**
   * Restore products
   */
  private static async restoreProducts(data: any[]) {
    for (const product of data) {
      // Check if product already exists
      const existingProduct = await prisma.product.findUnique({
        where: { id: product.id },
      })

      if (!existingProduct) {
        // Create product
        await prisma.product.create({
          data: {
            id: product.id,
            name: product.name,
            description: product.description,
            sku: product.sku,
            barcode: product.barcode,
            price: product.price,
            compareAtPrice: product.compareAtPrice,
            costPrice: product.costPrice,
            inventoryLevel: product.inventoryLevel,
            reorderPoint: product.reorderPoint,
            weight: product.weight,
            dimensions: product.dimensions,
            taxable: product.taxable,
            taxCode: product.taxCode,
            hasVariants: product.hasVariants,
            isDigital: product.isDigital,
            isActive: product.isActive,
            trackInventory: product.trackInventory,
            requiresShipping: product.requiresShipping,
            allowBackorder: product.allowBackorder,
            hasSerialNumbers: product.hasSerialNumbers,
            createdAt: new Date(product.createdAt),
            updatedAt: new Date(product.updatedAt),
            userId: product.userId,
            categoryId: product.categoryId,
            supplierId: product.supplierId,
          },
        })

        // Create product images
        if (product.images && product.images.length > 0) {
          for (const image of product.images) {
            await prisma.productImage.create({
              data: {
                id: image.id,
                url: image.url,
                alt: image.alt,
                position: image.position,
                productId: product.id,
                createdAt: new Date(image.createdAt),
                updatedAt: new Date(image.updatedAt),
              },
            })
          }
        }

        // Create product variants
        if (product.variants && product.variants.length > 0) {
          for (const variant of product.variants) {
            await prisma.productVariant.create({
              data: {
                id: variant.id,
                sku: variant.sku,
                barcode: variant.barcode,
                price: variant.price,
                compareAtPrice: variant.compareAtPrice,
                costPrice: variant.costPrice,
                inventoryLevel: variant.inventoryLevel,
                weight: variant.weight,
                dimensions: variant.dimensions,
                isDefault: variant.isDefault,
                isActive: variant.isActive,
                productId: product.id,
                createdAt: new Date(variant.createdAt),
                updatedAt: new Date(variant.updatedAt),
              },
            })
          }
        }
      }
    }
  }

  /**
   * Restore customers
   */
  private static async restoreCustomers(data: any[]) {
    for (const customer of data) {
      // Check if customer already exists
      const existingCustomer = await prisma.customer.findUnique({
        where: { id: customer.id },
      })

      if (!existingCustomer) {
        // Create customer
        await prisma.customer.create({
          data: {
            id: customer.id,
            firstName: customer.firstName,
            lastName: customer.lastName,
            email: customer.email,
            phone: customer.phone,
            address: customer.address,
            city: customer.city,
            state: customer.state,
            postalCode: customer.postalCode,
            country: customer.country,
            notes: customer.notes,
            isActive: customer.isActive,
            loyaltyPoints: customer.loyaltyPoints || 0,
            loyaltyTier: customer.loyaltyTier,
            storeCredit: customer.storeCredit || 0,
            creditLimit: customer.creditLimit,
            creditHold: customer.creditHold || false,
            createdAt: new Date(customer.createdAt),
            updatedAt: new Date(customer.updatedAt),
            userId: customer.userId,
          },
        })
      }
    }
  }

  /**
   * Restore notifications
   */
  private static async restoreNotifications(data: any[]) {
    for (const notification of data) {
      // Check if notification already exists
      const existingNotification = await prisma.notification.findUnique({
        where: { id: notification.id },
      })

      if (!existingNotification) {
        // Create notification
        await prisma.notification.create({
          data: {
            id: notification.id,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            read: notification.read,
            createdAt: new Date(notification.createdAt),
            userId: notification.userId,
          },
        })
      }
    }
  }

  /**
   * Restore inventory history
   */
  private static async restoreInventoryHistory(data: any[]) {
    for (const history of data) {
      // Check if inventory history already exists
      const existingHistory = await prisma.inventoryHistory.findUnique({
        where: { id: history.id },
      })

      if (!existingHistory) {
        // Create inventory history
        await prisma.inventoryHistory.create({
          data: {
            id: history.id,
            productId: history.productId,
            quantity: history.quantity,
            type: history.type,
            reference: history.reference,
            notes: history.notes,
            createdAt: new Date(history.createdAt),
          },
        })
      }
    }
  }
}

