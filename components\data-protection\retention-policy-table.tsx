"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Loader2, Save } from "lucide-react"
import type { DataCategory, RetentionPolicy } from "@/lib/retention/retention-policy"
import { updateRetentionPolicy } from "@/app/actions/data-protection"
import { toast } from "@/components/ui/use-toast"

interface RetentionPolicyTableProps {
  policies: RetentionPolicy[]
}

export function RetentionPolicyTable({ policies }: RetentionPolicyTableProps) {
  const [formData, setFormData] = useState<{
    [key in DataCategory]?: {
      retentionPeriodDays: number
      archiveBeforeDelete: boolean
      isSubmitting: boolean
    }
  }>(
    policies.reduce((acc, policy) => {
      acc[policy.dataCategory] = {
        retentionPeriodDays: policy.retentionPeriodDays,
        archiveBeforeDelete: policy.archiveBeforeDelete,
        isSubmitting: false,
      }
      return acc
    }, {} as any),
  )

  const handleInputChange = (category: DataCategory, value: number) => {
    setFormData((prev) => ({
      ...prev,
      [category]: {
        ...prev[category],
        retentionPeriodDays: value,
      },
    }))
  }

  const handleSwitchChange = (category: DataCategory, value: boolean) => {
    setFormData((prev) => ({
      ...prev,
      [category]: {
        ...prev[category],
        archiveBeforeDelete: value,
      },
    }))
  }

  const handleSubmit = async (category: DataCategory) => {
    const data = formData[category]
    if (!data) return

    // Update submitting state
    setFormData((prev) => ({
      ...prev,
      [category]: {
        ...prev[category]!,
        isSubmitting: true,
      },
    }))

    try {
      await updateRetentionPolicy(category, data.retentionPeriodDays, data.archiveBeforeDelete)

      toast({
        title: "Policy Updated",
        description: `Retention policy for ${formatCategory(category)} has been updated.`,
      })
    } catch (error) {
      console.error("Error updating retention policy:", error)
      toast({
        title: "Error",
        description: "Failed to update retention policy. Please try again.",
        variant: "destructive",
      })
    } finally {
      // Reset submitting state
      setFormData((prev) => ({
        ...prev,
        [category]: {
          ...prev[category]!,
          isSubmitting: false,
        },
      }))
    }
  }

  const formatCategory = (category: DataCategory): string => {
    return category
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ")
  }

  const formatRetentionPeriod = (days: number): string => {
    if (days >= 365) {
      const years = Math.floor(days / 365)
      const remainingDays = days % 365

      if (remainingDays === 0) {
        return `${years} ${years === 1 ? "year" : "years"}`
      } else {
        return `${years} ${years === 1 ? "year" : "years"} and ${remainingDays} ${remainingDays === 1 ? "day" : "days"}`
      }
    } else {
      return `${days} ${days === 1 ? "day" : "days"}`
    }
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Data Category</TableHead>
          <TableHead>Retention Period</TableHead>
          <TableHead>Archive Before Delete</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {policies.map((policy) => {
          const data = formData[policy.dataCategory]
          if (!data) return null

          return (
            <TableRow key={policy.id}>
              <TableCell className="font-medium">{formatCategory(policy.dataCategory)}</TableCell>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    min="1"
                    value={data.retentionPeriodDays}
                    onChange={(e) => handleInputChange(policy.dataCategory, Number.parseInt(e.target.value) || 1)}
                    className="w-24"
                  />
                  <span className="text-sm text-muted-foreground">days</span>
                  <span className="text-sm text-muted-foreground">
                    ({formatRetentionPeriod(data.retentionPeriodDays)})
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <Switch
                  checked={data.archiveBeforeDelete}
                  onCheckedChange={(value) => handleSwitchChange(policy.dataCategory, value)}
                />
              </TableCell>
              <TableCell>
                <Button size="sm" onClick={() => handleSubmit(policy.dataCategory)} disabled={data.isSubmitting}>
                  {data.isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save
                    </>
                  )}
                </Button>
              </TableCell>
            </TableRow>
          )
        })}
      </TableBody>
    </Table>
  )
}

