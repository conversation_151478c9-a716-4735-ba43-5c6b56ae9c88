import * as React from "react"
import { cn } from "@/lib/utils"
import { Button, type ButtonProps } from "@/components/ui/button"

export interface TouchButtonProps extends ButtonProps {
  touchClassName?: string
}

export const TouchButton = React.forwardRef<HTMLButtonElement, TouchButtonProps>(
  ({ className, touchClassName, ...props }, ref) => {
    return (
      <Button
        className={cn(
          "min-h-[48px] min-w-[48px]", // Ensure minimum touch target size
          "touch:py-3 touch:px-4 touch:text-base", // Larger padding and text on touch devices
          className,
          touchClassName,
        )}
        ref={ref}
        {...props}
      />
    )
  },
)
TouchButton.displayName = "TouchButton"

