import Link from "next/link"
import { formatCurrency } from "@/lib/utils"
import type { Product } from "@/types"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { OptimizedImage } from "@/components/ui/optimized-image"

interface ProductCardProps {
  product: Product
}

export function ProductCard({ product }: ProductCardProps) {
  const stockStatus =
    product.quantity <= 0 ? "out-of-stock" : product.quantity <= product.reorderLevel ? "low-stock" : "in-stock"

  const stockStatusMap = {
    "out-of-stock": { label: "Out of Stock", variant: "destructive" as const },
    "low-stock": { label: "Low Stock", variant: "warning" as const },
    "in-stock": { label: "In Stock", variant: "success" as const },
  }

  return (
    <Card className="overflow-hidden transition-all hover:shadow-md">
      <Link href={`/products/${product.id}`}>
        <OptimizedImage
          src={product.imageUrl || "/placeholder.svg?height=300&width=300"}
          alt={product.name}
          aspectRatio="square"
          className="h-48 w-full"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </Link>
      <CardContent className="p-4">
        <div className="mb-2 flex items-start justify-between">
          <h3 className="font-medium line-clamp-1">{product.name}</h3>
          <Badge variant={stockStatusMap[stockStatus].variant}>{stockStatusMap[stockStatus].label}</Badge>
        </div>
        <div className="mb-1 text-sm text-muted-foreground">SKU: {product.sku}</div>
        <div className="mb-1 text-sm text-muted-foreground">Qty: {product.quantity}</div>
        <div className="text-lg font-bold">{formatCurrency(product.sellingPrice)}</div>
      </CardContent>
      <CardFooter className="border-t bg-muted/50 p-4">
        <Link
          href={`/products/${product.id}`}
          className="w-full text-center text-sm font-medium text-primary hover:underline"
        >
          View Details
        </Link>
      </CardFooter>
    </Card>
  )
}

