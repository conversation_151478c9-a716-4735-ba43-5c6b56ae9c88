"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Mi<PERSON>, MicOff, RefreshCw, Smartphone } from "lucide-react"
import { useMobileFeatures } from "@/components/providers/mobile-features-provider"
import OfflineStatus from "@/components/offline/offline-status"

export default function MobileFeaturesToolbar() {
  const { isNative, isOnline, syncStatus, isListening, startVoiceListening, stopVoiceListening, forceSync } =
    useMobileFeatures()
  const [isOpen, setIsOpen] = useState(false)

  const handleVoiceToggle = () => {
    if (isListening) {
      stopVoiceListening()
    } else {
      startVoiceListening()
    }
  }

  return (
    <div className="fixed bottom-16 right-4 z-50 flex flex-col items-end space-y-2">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="default" size="icon" className="h-12 w-12 rounded-full shadow-lg">
            <Smartphone className="h-6 w-6" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-72 p-0" align="end" alignOffset={-20} forceMount>
          <div className="space-y-2 p-4">
            <h3 className="font-medium">Mobile Features</h3>
            <div className="flex items-center justify-between">
              <span className="text-sm">Voice Commands</span>
              <Button
                variant={isListening ? "destructive" : "outline"}
                size="sm"
                className="h-8"
                onClick={handleVoiceToggle}
              >
                {isListening ? (
                  <>
                    <MicOff className="mr-2 h-3.5 w-3.5" />
                    Stop
                  </>
                ) : (
                  <>
                    <Mic className="mr-2 h-3.5 w-3.5" />
                    Start
                  </>
                )}
              </Button>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm">Network Status</span>
              <OfflineStatus />
            </div>

            {syncStatus.total > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm">Pending Changes</span>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8"
                  onClick={() => {
                    forceSync()
                    setIsOpen(false)
                  }}
                  disabled={!isOnline}
                >
                  <RefreshCw className="mr-2 h-3.5 w-3.5" />
                  Sync Now
                </Button>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {isListening && (
        <Button
          variant="destructive"
          size="icon"
          className="h-12 w-12 rounded-full shadow-lg animate-pulse"
          onClick={stopVoiceListening}
        >
          <Mic className="h-6 w-6" />
        </Button>
      )}
    </div>
  )
}

