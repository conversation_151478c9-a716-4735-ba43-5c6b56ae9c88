"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Loader2, Play, AlertTriangle } from "lucide-react"
import { executeRetentionPolicies } from "@/app/actions/data-protection"
import { toast } from "@/components/ui/use-toast"

export function RetentionExecutionButton() {
  const [isOpen, setIsOpen] = useState(false)
  const [isExecuting, setIsExecuting] = useState(false)
  const [results, setResults] = useState<any[] | null>(null)

  const handleExecute = async () => {
    setIsExecuting(true)
    setResults(null)

    try {
      const response = await executeRetentionPolicies()

      if (response.success) {
        setResults(response.results)
        toast({
          title: "Retention Policies Executed",
          description: "Data retention policies have been successfully executed.",
        })
      }
    } catch (error) {
      console.error("Error executing retention policies:", error)
      toast({
        title: "Error",
        description: "Failed to execute retention policies. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExecuting(false)
    }
  }

  const formatCategory = (category: string): string => {
    return category
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ")
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>
          <Play className="mr-2 h-4 w-4" />
          Execute Retention Policies
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Execute Data Retention Policies</DialogTitle>
          <DialogDescription>
            This will process all data according to the configured retention policies. Data that exceeds the retention
            period will be archived and/or deleted.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="flex items-center space-x-2 text-amber-500 mb-4">
            <AlertTriangle className="h-5 w-5" />
            <p className="font-medium">This action cannot be undone.</p>
          </div>

          {results && (
            <div className="mt-4 border rounded-md p-4">
              <h3 className="font-medium mb-2">Execution Results:</h3>
              <ul className="space-y-2">
                {results.map((result, index) => (
                  <li key={index} className="text-sm">
                    <span className="font-medium">{formatCategory(result.category)}:</span>{" "}
                    {result.archived > 0 && <span>{result.archived} items archived, </span>}
                    {result.deleted} items deleted
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isExecuting}>
            Cancel
          </Button>
          <Button variant="default" onClick={handleExecute} disabled={isExecuting}>
            {isExecuting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Executing...
              </>
            ) : (
              "Execute Now"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

