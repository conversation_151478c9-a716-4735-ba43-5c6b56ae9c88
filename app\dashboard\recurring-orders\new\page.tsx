import { RecurringOrderForm } from "@/components/recurring-orders/recurring-order-form"
import prisma from "@/lib/prisma"

async function getCustomers() {
  const customers = await prisma.customer.findMany({
    orderBy: {
      firstName: "asc",
    },
  })

  return customers
}

async function getProducts() {
  const products = await prisma.product.findMany({
    include: {
      variants: true,
    },
    where: {
      isActive: true,
    },
    orderBy: {
      name: "asc",
    },
  })

  return products
}

export default async function NewRecurringOrderPage() {
  const customers = await getCustomers()
  const products = await getProducts()

  return (
    <div className="space-y-4">
      <h2 className="text-3xl font-bold tracking-tight">Create Recurring Order</h2>
      <RecurringOrderForm customers={customers} products={products} />
    </div>
  )
}

