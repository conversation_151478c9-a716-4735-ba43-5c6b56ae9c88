"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { usePathname } from "next/navigation"
import { useSettingsStore } from "@/lib/store"

type Language = "en" | "es" | "fr" | "de" | "zh"

type LanguageContextType = {
  language: Language
  setLanguage: (language: Language) => void
  t: (key: string, options?: Record<string, any>) => string
}

const LanguageContext = createContext<LanguageContextType>({
  language: "en",
  setLanguage: () => {},
  t: () => "",
})

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const pathname = usePathname()
  const { settings, updateSettings } = useSettingsStore()
  const [language, setLanguageState] = useState<Language>((settings.system?.language as Language) || "en")
  const [translations, setTranslations] = useState<Record<string, any>>({})

  // Load translations for the current language
  useEffect(() => {
    const loadTranslations = async () => {
      try {
        const response = await fetch(`/locales/${language}/common.json`)
        const data = await response.json()
        setTranslations(data)
      } catch (error) {
        console.error(`Failed to load translations for ${language}:`, error)
        // Fallback to English if translations fail to load
        if (language !== "en") {
          const fallbackResponse = await fetch("/locales/en/common.json")
          const fallbackData = await fallbackResponse.json()
          setTranslations(fallbackData)
        }
      }
    }

    loadTranslations()
  }, [language])

  // Function to change language
  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage)

    // Update settings store
    updateSettings({
      system: {
        ...settings.system,
        language: newLanguage,
      },
    })

    // Redirect to the same page with the new locale
    // This is a simplified approach - in a real app, you might want to use
    // next-i18next's built-in locale switching
    const currentPath = pathname || "/"

    // Store language preference in localStorage
    if (typeof window !== "undefined") {
      localStorage.setItem("language", newLanguage)
    }
  }

  // Translation function
  const t = (key: string, options?: Record<string, any>): string => {
    // Split the key by dots to navigate the translations object
    const keys = key.split(".")
    let value = translations

    // Navigate through the translations object
    for (const k of keys) {
      if (value && typeof value === "object" && k in value) {
        value = value[k]
      } else {
        // Return the key if translation is not found
        return key
      }
    }

    // If the value is not a string, return the key
    if (typeof value !== "string") {
      return key
    }

    // Replace placeholders with values from options
    if (options) {
      return Object.entries(options).reduce((acc, [k, v]) => {
        return acc.replace(new RegExp(`{{${k}}}`, "g"), String(v))
      }, value)
    }

    return value
  }

  // Initialize language from localStorage or browser preference
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedLanguage = localStorage.getItem("language") as Language

      if (storedLanguage && ["en", "es", "fr", "de", "zh"].includes(storedLanguage)) {
        setLanguageState(storedLanguage)
      } else {
        // Detect browser language
        const browserLanguage = navigator.language.split("-")[0] as Language

        if (["en", "es", "fr", "de", "zh"].includes(browserLanguage)) {
          setLanguageState(browserLanguage)
        }
      }
    }
  }, [])

  return <LanguageContext.Provider value={{ language, setLanguage, t }}>{children}</LanguageContext.Provider>
}

export const useLanguage = () => useContext(LanguageContext)

