"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, RefreshCw, Upload, Download, MoreHorizontal } from "lucide-react"
import { RealTimeInventory } from "@/components/real-time-inventory"
import { useRealTimeSync } from "@/hooks/use-real-time-sync"
import { BatchImportDialog } from "@/components/inventory/batch-import-dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useRouter } from "next/navigation"

interface Product {
  id: string
  name: string
  sku: string
  category: {
    name: string
  }
  price: number
  stockQuantity: number
  reorderPoint: number
}

interface InventoryData {
  products: Product[]
}

export default function ProductsPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [importDialogOpen, setImportDialogOpen] = useState(false)

  // Use real-time sync hook to get products
  const { data, isLoading, lastUpdated, refresh, isOnline } = useRealTimeSync<InventoryData>({
    type: "inventory",
    limit: 100,
  })

  // Filter products based on search query
  const filteredProducts = data?.products
    ? data.products.filter(
        (product) =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.name.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : []

  // Determine product status based on stock levels
  const getProductStatus = (product: Product) => {
    if (product.stockQuantity <= 0) {
      return "Out of Stock"
    } else if (product.stockQuantity <= (product.reorderPoint || 10)) {
      return "Low Stock"
    } else {
      return "In Stock"
    }
  }

  const handleExportProducts = () => {
    // Create URL with search parameters if needed
    let url = "/api/batch/export"
    if (searchQuery) {
      url += `?query=${encodeURIComponent(searchQuery)}`
    }

    // Trigger file download
    window.location.href = url
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Products</h2>
        <div className="flex items-center gap-2">
          <Button onClick={() => router.push("/dashboard/products/new")}>
            <Plus className="mr-2 h-4 w-4" /> Add Product
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setImportDialogOpen(true)}>
                <Upload className="mr-2 h-4 w-4" />
                Import Products
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExportProducts}>
                <Download className="mr-2 h-4 w-4" />
                Export Products
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push("/dashboard/products/batch")}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Batch Operations
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search products..."
            className="w-full pl-8 md:w-[300px]"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button variant="outline" onClick={refresh} disabled={!isOnline}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          Refresh
        </Button>
        <Button variant="outline">Filters</Button>
        <Button variant="outline">Export</Button>
      </div>

      <RealTimeInventory showNotifications={true} />

      {lastUpdated && <p className="text-xs text-muted-foreground">Last updated: {lastUpdated.toLocaleString()}</p>}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>SKU</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Stock</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Loading state
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`loading-${index}`}>
                  <TableCell colSpan={7} className="h-12 animate-pulse bg-muted"></TableCell>
                </TableRow>
              ))
            ) : filteredProducts.length === 0 ? (
              // Empty state
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  {searchQuery ? "No products match your search" : "No products found"}
                </TableCell>
              </TableRow>
            ) : (
              // Products list
              filteredProducts.map((product) => {
                const status = getProductStatus(product)

                return (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.sku}</TableCell>
                    <TableCell>{product.category.name}</TableCell>
                    <TableCell>${product.price.toFixed(2)}</TableCell>
                    <TableCell>{product.stockQuantity}</TableCell>
                    <TableCell>
                      <Badge
                        variant={status === "In Stock" ? "default" : status === "Low Stock" ? "warning" : "destructive"}
                      >
                        {status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>

      <BatchImportDialog open={importDialogOpen} onOpenChange={setImportDialogOpen} />
    </div>
  )
}

