import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Search, Edit, ExternalLink, Phone, Mail, MapPin } from "lucide-react"

export default function SuppliersPage() {
  const suppliers = [
    {
      id: "SUP001",
      name: "Tech Supplies Inc.",
      email: "<EMAIL>",
      phone: "+****************",
      address: "123 Tech Blvd, San Francisco, CA 94107",
      productCount: 42,
      lastOrder: "2023-09-15",
    },
    {
      id: "SUP002",
      name: "Global Foods Ltd.",
      email: "<EMAIL>",
      phone: "+****************",
      address: "456 Food Ave, Chicago, IL 60607",
      productCount: 28,
      lastOrder: "2023-09-10",
    },
    {
      id: "SUP003",
      name: "Fashion Wholesale Co.",
      email: "<EMAIL>",
      phone: "+****************",
      address: "789 Fashion St, New York, NY 10001",
      productCount: 56,
      lastOrder: "2023-09-05",
    },
    {
      id: "SUP004",
      name: "Office Essentials",
      email: "<EMAIL>",
      phone: "+****************",
      address: "101 Office Pkwy, Austin, TX 78701",
      productCount: 19,
      lastOrder: "2023-08-28",
    },
    {
      id: "SUP005",
      name: "Sports Gear Direct",
      email: "<EMAIL>",
      phone: "+****************",
      address: "202 Sports Ctr, Denver, CO 80202",
      productCount: 31,
      lastOrder: "2023-08-20",
    },
    {
      id: "SUP006",
      name: "Home Goods Supply",
      email: "<EMAIL>",
      phone: "+****************",
      address: "303 Home Rd, Seattle, WA 98101",
      productCount: 47,
      lastOrder: "2023-08-15",
    },
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Suppliers</h2>
        <Button>
          <Plus className="mr-2 h-4 w-4" /> Add Supplier
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input type="search" placeholder="Search suppliers..." className="w-full pl-8 md:w-[300px]" />
        </div>
        <Button variant="outline">Export</Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {suppliers.map((supplier) => (
          <Card key={supplier.id}>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle>{supplier.name}</CardTitle>
                <div className="flex gap-1">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                {supplier.productCount} products • Last order: {new Date(supplier.lastOrder).toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent className="pb-4">
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{supplier.phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{supplier.email}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{supplier.address}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

