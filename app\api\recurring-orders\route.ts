import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const customerId = searchParams.get("customerId")
    const status = searchParams.get("status")

    const where: any = {}

    if (customerId) where.customerId = customerId
    if (status) where.status = status

    const recurringOrders = await prisma.recurringOrder.findMany({
      where,
      include: {
        customer: true,
        items: {
          include: {
            product: true,
            variant: true,
          },
        },
        orderHistory: {
          orderBy: {
            scheduledDate: "desc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(recurringOrders)
  } catch (error) {
    console.error("Error fetching recurring orders:", error)
    return NextResponse.json({ error: "Failed to fetch recurring orders" }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()

    const {
      customerId,
      frequency,
      nextOrderDate,
      endDate,
      paymentMethod,
      shippingAddress,
      billingAddress,
      notes,
      items,
    } = body

    // Create the recurring order and its items in a transaction
    const newRecurringOrder = await prisma.$transaction(async (tx) => {
      // Create the recurring order
      const recurringOrder = await tx.recurringOrder.create({
        data: {
          status: "ACTIVE",
          frequency,
          nextOrderDate: new Date(nextOrderDate),
          endDate: endDate ? new Date(endDate) : null,
          paymentMethod,
          shippingAddress,
          billingAddress,
          notes,
          customerId,
        },
      })

      // Create recurring order items
      for (const item of items) {
        await tx.recurringOrderItem.create({
          data: {
            recurringOrderId: recurringOrder.id,
            productId: item.productId,
            variantId: item.variantId || null,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            total: item.quantity * item.unitPrice,
          },
        })
      }

      // Create notification for the recurring order
      await tx.notification.create({
        data: {
          title: "New Recurring Order Created",
          message: `A new recurring order has been created for ${recurringOrder.frequency.toLowerCase()} delivery.`,
          type: "INFO",
          userId: session.user.id,
        },
      })

      return recurringOrder
    })

    return NextResponse.json(newRecurringOrder, { status: 201 })
  } catch (error) {
    console.error("Error creating recurring order:", error)
    return NextResponse.json({ error: "Failed to create recurring order" }, { status: 500 })
  }
}

