import { db } from "../db"

export interface PrivacySettings {
  id: string
  userId: string
  dataMinimization: boolean
  anonymizeAnalytics: boolean
  limitDataSharing: boolean
  enhancedSecurity: boolean
  updatedAt: Date
}

export class PrivacySettingsService {
  /**
   * Get privacy settings for a user
   */
  static async getSettings(userId: string): Promise<PrivacySettings> {
    // Try to find existing settings
    const settings = await db.privacySettings.findUnique({
      where: { userId },
    })

    if (settings) {
      return settings
    }

    // Create default settings if none exist
    return db.privacySettings.create({
      data: {
        userId,
        dataMinimization: true,
        anonymizeAnalytics: true,
        limitDataSharing: true,
        enhancedSecurity: true,
        updatedAt: new Date(),
      },
    })
  }

  /**
   * Update privacy settings for a user
   */
  static async updateSettings(
    userId: string,
    settings: Partial<Omit<PrivacySettings, "id" | "userId" | "updatedAt">>,
  ): Promise<PrivacySettings> {
    // Check if settings exist
    const existingSettings = await db.privacySettings.findUnique({
      where: { userId },
    })

    if (existingSettings) {
      // Update existing settings
      return db.privacySettings.update({
        where: { userId },
        data: {
          ...settings,
          updatedAt: new Date(),
        },
      })
    } else {
      // Create new settings
      return db.privacySettings.create({
        data: {
          userId,
          ...settings,
          dataMinimization: settings.dataMinimization ?? true,
          anonymizeAnalytics: settings.anonymizeAnalytics ?? true,
          limitDataSharing: settings.limitDataSharing ?? true,
          enhancedSecurity: settings.enhancedSecurity ?? true,
          updatedAt: new Date(),
        },
      })
    }
  }

  /**
   * Apply privacy settings to data
   * This is a helper function to filter data based on privacy settings
   */
  static async applyPrivacySettings<T extends Record<string, any>>(
    userId: string,
    data: T,
    sensitiveFields: string[] = [],
  ): Promise<Partial<T>> {
    const settings = await this.getSettings(userId)

    // If data minimization is enabled, remove sensitive fields
    if (settings.dataMinimization && sensitiveFields.length > 0) {
      const filteredData = { ...data }
      for (const field of sensitiveFields) {
        delete filteredData[field]
      }
      return filteredData
    }

    return data
  }
}

