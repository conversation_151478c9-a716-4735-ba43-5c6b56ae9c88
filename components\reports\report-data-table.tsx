"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Search } from "lucide-react"

interface ReportDataTableProps {
  data: any[]
  reportType: "sales" | "inventory" | "customers"
  isLoading: boolean
}

export function ReportDataTable({ data, reportType, isLoading }: ReportDataTableProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-64" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Detailed Data</CardTitle>
          <CardDescription>No data available for the selected period and filters</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32 text-muted-foreground">No data to display</div>
        </CardContent>
      </Card>
    )
  }

  // Filter data based on search query
  const filteredData = data.filter((item) => {
    const searchString = searchQuery.toLowerCase()
    return Object.values(item).some((value) => value && value.toString().toLowerCase().includes(searchString))
  })

  // Paginate data
  const totalPages = Math.ceil(filteredData.length / itemsPerPage)
  const paginatedData = filteredData.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // Get table headers based on report type
  const getTableHeaders = () => {
    const firstItem = data[0]
    if (!firstItem) return []

    return Object.keys(firstItem)
  }

  // Format cell value based on key and value
  const formatCellValue = (key: string, value: any) => {
    if (value === null || value === undefined) return "-"

    if (key.toLowerCase().includes("date") || key.toLowerCase().includes("time")) {
      return new Date(value).toLocaleString()
    }

    if (typeof value === "number") {
      if (
        key.toLowerCase().includes("price") ||
        key.toLowerCase().includes("revenue") ||
        key.toLowerCase().includes("value") ||
        key.toLowerCase().includes("total") ||
        key.toLowerCase().includes("spend")
      ) {
        return `$${value.toFixed(2)}`
      }

      if (key.toLowerCase().includes("percentage") || key.toLowerCase().includes("rate")) {
        return `${value.toFixed(1)}%`
      }

      return value.toString()
    }

    return value.toString()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Detailed Data</CardTitle>
        <CardDescription>
          {reportType === "sales" && "Detailed sales data for the selected period"}
          {reportType === "inventory" && "Detailed inventory data for the selected period"}
          {reportType === "customers" && "Detailed customer data for the selected period"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search data..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value)
                  setCurrentPage(1) // Reset to first page on search
                }}
              />
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  {getTableHeaders().map((header) => (
                    <TableHead key={header}>
                      {header.charAt(0).toUpperCase() + header.slice(1).replace(/([A-Z])/g, " $1")}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((item, index) => (
                  <TableRow key={index}>
                    {getTableHeaders().map((header) => (
                      <TableCell key={header}>{formatCellValue(header, item[header])}</TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                {Math.min(currentPage * itemsPerPage, filteredData.length)} of {filteredData.length} entries
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <div className="text-sm">
                  Page {currentPage} of {totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

