import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { MigrationService } from "@/lib/services/migration-service"
import { ApiError } from "@/lib/api-error"
import { AuditService, AuditAction } from "@/lib/services/audit-service"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to export data
    if (session.user.role !== "ADMIN" && session.user.role !== "MANAGER") {
      throw ApiError.forbidden("You do not have permission to export data")
    }

    const searchParams = req.nextUrl.searchParams
    const entityType = searchParams.get("entityType")
    const format = searchParams.get("format") || "csv"
    const filters = searchParams.get("filters") ? JSON.parse(searchParams.get("filters")!) : {}
    const fields = searchParams.get("fields") ? JSON.parse(searchParams.get("fields")!) : []

    if (!entityType) {
      throw ApiError.badRequest("No entity type provided")
    }

    // Export data
    const result = await MigrationService.exportData({
      entityType: entityType as any,
      format: format as any,
      filters,
      fields,
      userId: session.user.id,
    })

    // Log audit event
    await AuditService.log({
      action: AuditAction.EXPORT,
      entityType,
      userId: session.user.id,
      details: {
        format,
        filters,
        fields,
      },
      ipAddress: req.headers.get("x-forwarded-for") || req.ip,
      userAgent: req.headers.get("user-agent"),
    })

    // Return file
    return new NextResponse(result.fileContent, {
      headers: {
        "Content-Type": result.contentType,
        "Content-Disposition": `attachment; filename="${result.fileName}"`,
      },
    })
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Export error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

