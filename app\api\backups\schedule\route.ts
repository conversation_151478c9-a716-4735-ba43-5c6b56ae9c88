import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { scheduleBackups } from "@/lib/services/backup-service"
import { prisma } from "@/lib/prisma"

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get backup schedule settings
    const settings = await prisma.setting.findUnique({
      where: { key: "backup_schedule" },
    })

    const scheduleSettings = settings
      ? JSON.parse(settings.value)
      : {
          enabled: false,
          frequency: "daily",
          time: "00:00",
          retentionCount: 7,
        }

    return NextResponse.json({
      success: true,
      settings: scheduleSettings,
    })
  } catch (error) {
    console.error("Error in get backup schedule API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const { enabled, frequency, time, retentionCount } = body

    const result = await scheduleBackups({
      enabled,
      frequency,
      time,
      retentionCount,
    })

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 })
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error in update backup schedule API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

