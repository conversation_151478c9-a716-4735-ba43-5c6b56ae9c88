import { Suspense } from "react"
import { ReturnsList } from "@/components/returns/returns-list"
import prisma from "@/lib/prisma"

async function getReturns() {
  const returns = await prisma.return.findMany({
    include: {
      customer: true,
      order: true,
      items: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  })

  return returns
}

export default async function ReturnsPage() {
  const returns = await getReturns()

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Returns</h2>
      </div>

      <Suspense fallback={<div>Loading returns...</div>}>
        <ReturnsList returns={returns} />
      </Suspense>
    </div>
  )
}

