"use client"

import { useState, useEffect } from "react"

export function useMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Check if window is defined (client-side)
    if (typeof window !== "undefined") {
      // Initial check
      const checkMobile = () => {
        const userAgent = navigator.userAgent.toLowerCase()
        const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
        const isSmallScreen = window.innerWidth < 768

        setIsMobile(isMobileDevice || isSmallScreen)
      }

      // Check on mount
      checkMobile()

      // Check on resize
      window.addEventListener("resize", checkMobile)

      // Cleanup
      return () => {
        window.removeEventListener("resize", checkMobile)
      }
    }
  }, [])

  return isMobile
}

