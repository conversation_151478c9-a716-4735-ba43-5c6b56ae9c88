"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Trash2, RefreshCw, AlertCircle } from "lucide-react"
import type { OptionGroupFormValues, VariantFormValues } from "@/lib/validations/variant-schema"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface VariantManagerProps {
  initialOptionGroups?: OptionGroupFormValues[]
  initialVariants?: VariantFormValues[]
  onOptionGroupsChange: (optionGroups: OptionGroupFormValues[]) => void
  onVariantsChange: (variants: VariantFormValues[]) => void
  hasVariants: boolean
  onHasVariantsChange: (hasVariants: boolean) => void
}

export function VariantManager({
  initialOptionGroups = [],
  initialVariants = [],
  onOptionGroupsChange,
  onVariantsChange,
  hasVariants,
  onHasVariantsChange,
}: VariantManagerProps) {
  const [optionGroups, setOptionGroups] = useState<OptionGroupFormValues[]>(initialOptionGroups)
  const [variants, setVariants] = useState<VariantFormValues[]>(initialVariants)
  const [activeTab, setActiveTab] = useState<string>(hasVariants ? "options" : "simple")
  const [newOptionGroup, setNewOptionGroup] = useState<OptionGroupFormValues>({
    name: "",
    displayName: "",
    options: [""],
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isGeneratingVariants, setIsGeneratingVariants] = useState(false)

  useEffect(() => {
    if (initialOptionGroups.length > 0) {
      setOptionGroups(initialOptionGroups)
    }
    if (initialVariants.length > 0) {
      setVariants(initialVariants)
    }
  }, [initialOptionGroups, initialVariants])

  useEffect(() => {
    onOptionGroupsChange(optionGroups)
  }, [optionGroups, onOptionGroupsChange])

  useEffect(() => {
    onVariantsChange(variants)
  }, [variants, onVariantsChange])

  const handleHasVariantsChange = (checked: boolean) => {
    onHasVariantsChange(checked)
    setActiveTab(checked ? "options" : "simple")
  }

  const handleOptionGroupNameChange = (value: string) => {
    setNewOptionGroup({
      ...newOptionGroup,
      name: value,
      displayName: value, // Auto-fill display name with the same value
    })

    if (value) {
      setErrors((prev) => ({ ...prev, optionGroupName: "" }))
    }
  }

  const handleOptionGroupDisplayNameChange = (value: string) => {
    setNewOptionGroup({
      ...newOptionGroup,
      displayName: value,
    })

    if (value) {
      setErrors((prev) => ({ ...prev, optionGroupDisplayName: "" }))
    }
  }

  const handleOptionValueChange = (index: number, value: string) => {
    const updatedOptions = [...newOptionGroup.options]
    updatedOptions[index] = value

    setNewOptionGroup({
      ...newOptionGroup,
      options: updatedOptions,
    })

    if (value) {
      setErrors((prev) => ({ ...prev, [`optionValue_${index}`]: "" }))
    }
  }

  const addOptionValue = () => {
    setNewOptionGroup({
      ...newOptionGroup,
      options: [...newOptionGroup.options, ""],
    })
  }

  const removeOptionValue = (index: number) => {
    if (newOptionGroup.options.length <= 1) {
      setErrors((prev) => ({
        ...prev,
        optionValues: "At least one option value is required",
      }))
      return
    }

    const updatedOptions = [...newOptionGroup.options]
    updatedOptions.splice(index, 1)

    setNewOptionGroup({
      ...newOptionGroup,
      options: updatedOptions,
    })
  }

  const addOptionGroup = () => {
    // Validate
    const newErrors: Record<string, string> = {}

    if (!newOptionGroup.name) {
      newErrors.optionGroupName = "Option name is required"
    } else if (optionGroups.some((group) => group.name === newOptionGroup.name)) {
      newErrors.optionGroupName = "Option name must be unique"
    }

    if (!newOptionGroup.displayName) {
      newErrors.optionGroupDisplayName = "Display name is required"
    }

    if (newOptionGroup.options.some((option) => !option)) {
      newOptionGroup.options.forEach((option, index) => {
        if (!option) {
          newErrors[`optionValue_${index}`] = "Option value is required"
        }
      })
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    // Add the new option group
    const updatedOptionGroups = [...optionGroups, newOptionGroup]
    setOptionGroups(updatedOptionGroups)

    // Reset form
    setNewOptionGroup({
      name: "",
      displayName: "",
      options: [""],
    })
    setErrors({})
  }

  const removeOptionGroup = (index: number) => {
    const updatedOptionGroups = [...optionGroups]
    updatedOptionGroups.splice(index, 1)
    setOptionGroups(updatedOptionGroups)

    // If we removed all option groups, reset variants
    if (updatedOptionGroups.length === 0) {
      setVariants([])
    }
  }

  const generateVariantCombinations = () => {
    if (optionGroups.length === 0) {
      setErrors({ general: "You need to add at least one option group to generate variants" })
      return
    }

    setIsGeneratingVariants(true)
    setErrors({})

    try {
      // Generate all possible combinations of options
      const generateCombinations = (
        optionGroups: OptionGroupFormValues[],
        currentIndex: number,
        currentCombination: Record<string, string> = {},
        result: Record<string, string>[] = [],
      ): Record<string, string>[] => {
        if (currentIndex === optionGroups.length) {
          result.push({ ...currentCombination })
          return result
        }

        const currentGroup = optionGroups[currentIndex]

        for (const option of currentGroup.options) {
          currentCombination[currentGroup.name] = option
          generateCombinations(optionGroups, currentIndex + 1, currentCombination, result)
        }

        return result
      }

      const combinations = generateCombinations(optionGroups, 0)

      // Create variants from combinations
      const newVariants: VariantFormValues[] = combinations.map((combination, index) => {
        // Check if this combination already exists in variants
        const existingVariant = variants.find((variant) => {
          return Object.entries(combination).every(([key, value]) => variant.options[key] === value)
        })

        if (existingVariant) {
          return existingVariant
        }

        // Create a new variant
        const variantName = Object.values(combination).join(" / ")

        return {
          sku: `${index + 1}`,
          barcode: null,
          price: null,
          cost: null,
          stockQuantity: 0,
          isDefault: index === 0, // First variant is default
          options: combination,
        }
      })

      setVariants(newVariants)
      setActiveTab("variants")
    } catch (error) {
      console.error("Error generating variants:", error)
      setErrors({ general: "An error occurred while generating variants" })
    } finally {
      setIsGeneratingVariants(false)
    }
  }

  const updateVariant = (index: number, field: keyof VariantFormValues, value: any) => {
    const updatedVariants = [...variants]

    if (field === "isDefault" && value === true) {
      // Unset default for all other variants
      updatedVariants.forEach((variant, i) => {
        if (i !== index) {
          variant.isDefault = false
        }
      })
    }

    updatedVariants[index] = {
      ...updatedVariants[index],
      [field]: value,
    }

    setVariants(updatedVariants)
  }

  const getVariantName = (variant: VariantFormValues) => {
    return Object.entries(variant.options)
      .map(([group, value]) => {
        const displayGroup = optionGroups.find((g) => g.name === group)?.displayName || group
        return `${displayGroup}: ${value}`
      })
      .join(", ")
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Switch id="has-variants" checked={hasVariants} onCheckedChange={handleHasVariantsChange} />
          <Label htmlFor="has-variants">This product has multiple variants</Label>
        </div>
      </div>

      {hasVariants && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="options">Option Groups</TabsTrigger>
            <TabsTrigger value="variants">Variants</TabsTrigger>
          </TabsList>

          <TabsContent value="options" className="space-y-4">
            {errors.general && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errors.general}</AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Add Option Group</CardTitle>
                <CardDescription>Create option groups like Size, Color, Material, etc.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="option-name">
                      Option Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="option-name"
                      placeholder="e.g. size, color"
                      value={newOptionGroup.name}
                      onChange={(e) => handleOptionGroupNameChange(e.target.value)}
                      className={errors.optionGroupName ? "border-red-500" : ""}
                    />
                    {errors.optionGroupName && <p className="text-xs text-red-500">{errors.optionGroupName}</p>}
                    <p className="text-xs text-muted-foreground">Internal name used for the option (no spaces)</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="display-name">
                      Display Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="display-name"
                      placeholder="e.g. Size, Color"
                      value={newOptionGroup.displayName}
                      onChange={(e) => handleOptionGroupDisplayNameChange(e.target.value)}
                      className={errors.optionGroupDisplayName ? "border-red-500" : ""}
                    />
                    {errors.optionGroupDisplayName && (
                      <p className="text-xs text-red-500">{errors.optionGroupDisplayName}</p>
                    )}
                    <p className="text-xs text-muted-foreground">How this option will be displayed to customers</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>
                    Option Values <span className="text-red-500">*</span>
                  </Label>
                  <div className="space-y-2">
                    {newOptionGroup.options.map((option, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Input
                          placeholder={`e.g. ${
                            newOptionGroup.name === "color"
                              ? "Red, Blue"
                              : newOptionGroup.name === "size"
                                ? "Small, Medium"
                                : "Option value"
                          }`}
                          value={option}
                          onChange={(e) => handleOptionValueChange(index, e.target.value)}
                          className={errors[`optionValue_${index}`] ? "border-red-500" : ""}
                        />
                        <Button variant="ghost" size="icon" onClick={() => removeOptionValue(index)} type="button">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    {errors.optionValues && <p className="text-xs text-red-500">{errors.optionValues}</p>}
                  </div>
                  <Button variant="outline" size="sm" onClick={addOptionValue} type="button" className="mt-2">
                    <Plus className="h-4 w-4 mr-2" /> Add Option Value
                  </Button>
                </div>

                <Button onClick={addOptionGroup} type="button">
                  Add Option Group
                </Button>
              </CardContent>
            </Card>

            {optionGroups.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Option Groups</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {optionGroups.map((group, index) => (
                      <div key={index} className="border rounded-md p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <h4 className="font-medium">{group.displayName}</h4>
                            <p className="text-sm text-muted-foreground">{group.name}</p>
                          </div>
                          <Button variant="ghost" size="icon" onClick={() => removeOptionGroup(index)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {group.options.map((option, optionIndex) => (
                            <Badge key={optionIndex} variant="outline">
                              {option}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}

                    <Button onClick={generateVariantCombinations} disabled={isGeneratingVariants} className="w-full">
                      {isGeneratingVariants ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generating Variants...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Generate Variants
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="variants">
            {variants.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center p-6">
                    <h3 className="font-medium mb-2">No Variants Created Yet</h3>
                    <p className="text-muted-foreground mb-4">
                      Add option groups and generate variants to manage your product variations.
                    </p>
                    <Button onClick={() => setActiveTab("options")}>Go to Option Groups</Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Product Variants</CardTitle>
                  <CardDescription>Manage your product variants and their specific details</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Variant</TableHead>
                          <TableHead>SKU</TableHead>
                          <TableHead>Price</TableHead>
                          <TableHead>Stock</TableHead>
                          <TableHead>Default</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {variants.map((variant, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <div className="font-medium">{getVariantName(variant)}</div>
                            </TableCell>
                            <TableCell>
                              <Input
                                value={variant.sku}
                                onChange={(e) => updateVariant(index, "sku", e.target.value)}
                                className="w-full"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={variant.price || ""}
                                onChange={(e) =>
                                  updateVariant(
                                    index,
                                    "price",
                                    e.target.value ? Number.parseFloat(e.target.value) : null,
                                  )
                                }
                                placeholder="Use product price"
                                className="w-full"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                min="0"
                                value={variant.stockQuantity}
                                onChange={(e) =>
                                  updateVariant(index, "stockQuantity", Number.parseInt(e.target.value) || 0)
                                }
                                className="w-full"
                              />
                            </TableCell>
                            <TableCell>
                              <Switch
                                checked={variant.isDefault}
                                onCheckedChange={(checked) => updateVariant(index, "isDefault", checked)}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}

