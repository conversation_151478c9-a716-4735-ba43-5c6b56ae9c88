"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2, Plus, Trash, CalendarIcon } from "lucide-react"
import { recurringOrderSchema, type RecurringOrderFormValues } from "@/lib/validations/recurring-order-schema"
import { formatCurrency, formatDate } from "@/lib/utils"

interface RecurringOrderFormProps {
  customers: any[]
  products: any[]
  onSuccess?: () => void
}

export function RecurringOrderForm({ customers, products, onSuccess }: RecurringOrderFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedItems, setSelectedItems] = useState<any[]>([])
  const [selectedProduct, setSelectedProduct] = useState<string>("")
  const [selectedVariant, setSelectedVariant] = useState<string>("")
  const [quantity, setQuantity] = useState<number>(1)
  const [totalAmount, setTotalAmount] = useState<number>(0)
  const [nextOrderDate, setNextOrderDate] = useState<Date>(new Date())
  const [hasEndDate, setHasEndDate] = useState<boolean>(false)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<RecurringOrderFormValues>({
    resolver: zodResolver(recurringOrderSchema),
    defaultValues: {
      customerId: "",
      frequency: "MONTHLY",
      nextOrderDate: new Date(),
      endDate: undefined,
      paymentMethod: "CREDIT_CARD",
      shippingAddress: "",
      billingAddress: "",
      notes: "",
      items: [],
    },
  })

  // Update form values when selected items change
  useEffect(() => {
    const total = selectedItems.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0)
    setTotalAmount(total)
    setValue("items", selectedItems)
  }, [selectedItems, setValue])

  // Update dates in form
  useEffect(() => {
    setValue("nextOrderDate", nextOrderDate)
    setValue("endDate", hasEndDate ? endDate : null)
  }, [nextOrderDate, endDate, hasEndDate, setValue])

  const handleAddItem = () => {
    if (!selectedProduct) return

    const product = products.find((p) => p.id === selectedProduct)
    if (!product) return

    let itemToAdd: any = {
      productId: product.id,
      productName: product.name,
      quantity: quantity,
      unitPrice: product.price,
      total: quantity * product.price,
    }

    if (selectedVariant && product.variants) {
      const variant = product.variants.find((v: any) => v.id === selectedVariant)
      if (variant) {
        itemToAdd = {
          ...itemToAdd,
          variantId: variant.id,
          variantName: variant.sku,
          unitPrice: variant.price || product.price,
          total: quantity * (variant.price || product.price),
        }
      }
    }

    setSelectedItems([...selectedItems, itemToAdd])
    setSelectedProduct("")
    setSelectedVariant("")
    setQuantity(1)
  }

  const handleRemoveItem = (index: number) => {
    const updatedItems = [...selectedItems]
    updatedItems.splice(index, 1)
    setSelectedItems(updatedItems)
  }

  const onSubmit = async (data: RecurringOrderFormValues) => {
    try {
      setIsSubmitting(true)

      if (data.items.length === 0) {
        toast({
          title: "Error",
          description: "Please add at least one item to the recurring order",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      const response = await fetch("/api/recurring-orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to create recurring order")
      }

      const result = await response.json()

      toast({
        title: "Recurring Order Created",
        description: `Recurring order has been created successfully.`,
      })

      if (onSuccess) {
        onSuccess()
      } else {
        router.push(`/dashboard/recurring-orders/${result.id}`)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create recurring order",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get product variants
  const getProductVariants = () => {
    if (!selectedProduct) return []

    const product = products.find((p) => p.id === selectedProduct)
    return product?.variants || []
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        <CardHeader>
          <CardTitle>Create Recurring Order</CardTitle>
          <CardDescription>Set up a new recurring order for a customer</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="customerId">Customer</Label>
              <Select onValueChange={(value) => setValue("customerId", value)} defaultValue="">
                <SelectTrigger id="customerId" className="w-full">
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.firstName} {customer.lastName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.customerId && <p className="text-sm text-red-500 mt-1">{errors.customerId.message}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="frequency">Frequency</Label>
                <Select onValueChange={(value) => setValue("frequency", value)} defaultValue="MONTHLY">
                  <SelectTrigger id="frequency" className="w-full">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DAILY">Daily</SelectItem>
                    <SelectItem value="WEEKLY">Weekly</SelectItem>
                    <SelectItem value="BIWEEKLY">Bi-weekly</SelectItem>
                    <SelectItem value="MONTHLY">Monthly</SelectItem>
                    <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                    <SelectItem value="BIANNUALLY">Bi-annually</SelectItem>
                    <SelectItem value="ANNUALLY">Annually</SelectItem>
                  </SelectContent>
                </Select>
                {errors.frequency && <p className="text-sm text-red-500 mt-1">{errors.frequency.message}</p>}
              </div>

              <div>
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select onValueChange={(value) => setValue("paymentMethod", value)} defaultValue="CREDIT_CARD">
                  <SelectTrigger id="paymentMethod" className="w-full">
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CASH">Cash</SelectItem>
                    <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
                    <SelectItem value="DEBIT_CARD">Debit Card</SelectItem>
                    <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                    <SelectItem value="CHECK">Check</SelectItem>
                    <SelectItem value="PAYPAL">PayPal</SelectItem>
                    <SelectItem value="STORE_CREDIT">Store Credit</SelectItem>
                    <SelectItem value="GIFT_CARD">Gift Card</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
                {errors.paymentMethod && <p className="text-sm text-red-500 mt-1">{errors.paymentMethod.message}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="nextOrderDate">First Order Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {nextOrderDate ? formatDate(nextOrderDate) : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={nextOrderDate}
                      onSelect={(date) => date && setNextOrderDate(date)}
                      initialFocus
                      disabled={(date) => date < new Date()}
                    />
                  </PopoverContent>
                </Popover>
                {errors.nextOrderDate && <p className="text-sm text-red-500 mt-1">{errors.nextOrderDate.message}</p>}
              </div>

              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Checkbox
                    id="hasEndDate"
                    checked={hasEndDate}
                    onCheckedChange={(checked) => setHasEndDate(checked as boolean)}
                  />
                  <Label htmlFor="hasEndDate">Set End Date</Label>
                </div>

                {hasEndDate && (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? formatDate(endDate) : "Select end date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={(date) => date && setEndDate(date)}
                        initialFocus
                        disabled={(date) => date <= nextOrderDate}
                      />
                    </PopoverContent>
                  </Popover>
                )}
                {errors.endDate && <p className="text-sm text-red-500 mt-1">{errors.endDate.message}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="shippingAddress">Shipping Address</Label>
                <Textarea id="shippingAddress" placeholder="Enter shipping address" {...register("shippingAddress")} />
              </div>

              <div>
                <Label htmlFor="billingAddress">Billing Address</Label>
                <Textarea id="billingAddress" placeholder="Enter billing address" {...register("billingAddress")} />
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea id="notes" placeholder="Additional notes about this recurring order" {...register("notes")} />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Add Items</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="product">Product</Label>
                  <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                    <SelectTrigger id="product">
                      <SelectValue placeholder="Select product" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedProduct && getProductVariants().length > 0 && (
                  <div>
                    <Label htmlFor="variant">Variant</Label>
                    <Select value={selectedVariant} onValueChange={setSelectedVariant}>
                      <SelectTrigger id="variant">
                        <SelectValue placeholder="Select variant" />
                      </SelectTrigger>
                      <SelectContent>
                        {getProductVariants().map((variant: any) => (
                          <SelectItem key={variant.id} value={variant.id}>
                            {variant.sku}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div>
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={(e) => setQuantity(Number.parseInt(e.target.value))}
                  />
                </div>

                <div className="flex items-end">
                  <Button type="button" onClick={handleAddItem}>
                    <Plus className="mr-2 h-4 w-4" /> Add Item
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                {selectedItems.map((item, index) => (
                  <div key={index} className="flex justify-between items-center border p-3 rounded-md">
                    <div>
                      <p className="font-medium">{item.productName}</p>
                      {item.variantName && <p className="text-sm text-muted-foreground">Variant: {item.variantName}</p>}
                      <p className="text-sm">
                        {formatCurrency(item.unitPrice)} x {item.quantity} = {formatCurrency(item.total)}
                      </p>
                    </div>
                    <Button type="button" variant="ghost" size="icon" onClick={() => handleRemoveItem(index)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                ))}

                {selectedItems.length === 0 && (
                  <p className="text-center text-muted-foreground py-4">No items added yet</p>
                )}
              </div>
            </div>
          </div>

          <div className="bg-muted p-4 rounded-md">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Total Amount Per Order:</span>
                <span className="font-medium">{formatCurrency(totalAmount)}</span>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Recurring Order
          </Button>
        </CardFooter>
      </Card>
    </form>
  )
}

