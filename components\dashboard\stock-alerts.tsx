import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

export function StockAlerts() {
  const lowStockItems = [
    {
      id: "PRD001",
      name: "Wireless Headphones",
      sku: "WH-BT-001",
      category: "Electronics",
      currentStock: 5,
      reorderPoint: 10,
      supplier: "Tech Supplies Inc.",
    },
    {
      id: "PRD002",
      name: "Organic Coffee Beans",
      sku: "OCB-500G",
      category: "Food & Beverage",
      currentStock: 3,
      reorderPoint: 15,
      supplier: "Global Foods Ltd.",
    },
    {
      id: "PRD003",
      name: "Cotton T-Shirt (M)",
      sku: "CTS-M-BLK",
      category: "Apparel",
      currentStock: 8,
      reorderPoint: 20,
      supplier: "Fashion Wholesale Co.",
    },
    {
      id: "PRD004",
      name: "Smartphone Charger",
      sku: "SC-USB-C",
      category: "Electronics",
      currentStock: 4,
      reorderPoint: 12,
      supplier: "Tech Supplies Inc.",
    },
    {
      id: "PRD005",
      name: "Notebook Set",
      sku: "NS-3PK",
      category: "Stationery",
      currentStock: 7,
      reorderPoint: 25,
      supplier: "Office Essentials",
    },
  ]

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Product</TableHead>
          <TableHead>SKU</TableHead>
          <TableHead>Category</TableHead>
          <TableHead>Current Stock</TableHead>
          <TableHead>Reorder Point</TableHead>
          <TableHead>Supplier</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {lowStockItems.map((item) => (
          <TableRow key={item.id}>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell>{item.sku}</TableCell>
            <TableCell>{item.category}</TableCell>
            <TableCell>
              <Badge variant="destructive">{item.currentStock}</Badge>
            </TableCell>
            <TableCell>{item.reorderPoint}</TableCell>
            <TableCell>{item.supplier}</TableCell>
            <TableCell className="text-right">
              <Button size="sm" variant="outline">
                Reorder
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

