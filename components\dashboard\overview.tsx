"use client"

import { useMobile } from "@/hooks/use-mobile"
import { <PERSON>, <PERSON>hart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from "@/components/ui/chart"

export function Overview() {
  const isMobile = useMobile()

  const data = [
    {
      name: "Jan",
      total: 1800,
    },
    {
      name: "Feb",
      total: 2200,
    },
    {
      name: "<PERSON>",
      total: 2800,
    },
    {
      name: "Apr",
      total: 2400,
    },
    {
      name: "May",
      total: 2900,
    },
    {
      name: "Jun",
      total: 3200,
    },
    {
      name: "Jul",
      total: 3800,
    },
    {
      name: "Aug",
      total: 4000,
    },
    {
      name: "Sep",
      total: 4200,
    },
    {
      name: "Oct",
      total: 4500,
    },
    {
      name: "Nov",
      total: 4800,
    },
    {
      name: "Dec",
      total: 5200,
    },
  ]

  // For mobile, we'll show fewer data points
  const mobileData = isMobile ? data.filter((_, index) => index % 3 === 0 || index === data.length - 1) : data

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={isMobile ? mobileData : data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Bar dataKey="total" fill="#3b82f6" name="Sales ($)" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

