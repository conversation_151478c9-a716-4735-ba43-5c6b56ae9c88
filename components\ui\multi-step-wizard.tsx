"use client"

import type React from "react"
import { useState } from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle } from "lucide-react"
import { useAccessibility } from "@/providers/accessibility-provider"

export interface Step {
  id: string
  title: string
  description?: string
  component: React.ReactNode
  isOptional?: boolean
}

interface MultiStepWizardProps {
  steps: Step[]
  onComplete: () => void
  onCancel?: () => void
  className?: string
  initialStep?: number
}

export function MultiStepWizard({ steps, onComplete, onCancel, className, initialStep = 0 }: MultiStepWizardProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(initialStep)
  const [completedSteps, setCompletedSteps] = useState<Record<string, boolean>>({})
  const { announceMessage } = useAccessibility()

  const currentStep = steps[currentStepIndex]
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === steps.length - 1

  const handleNext = () => {
    if (isLastStep) {
      onComplete()
      return
    }

    setCompletedSteps((prev) => ({
      ...prev,
      [currentStep.id]: true,
    }))

    const nextIndex = currentStepIndex + 1
    setCurrentStepIndex(nextIndex)
    announceMessage(`Step ${nextIndex + 1} of ${steps.length}: ${steps[nextIndex].title}`)
  }

  const handleBack = () => {
    if (isFirstStep) return

    const prevIndex = currentStepIndex - 1
    setCurrentStepIndex(prevIndex)
    announceMessage(`Step ${prevIndex + 1} of ${steps.length}: ${steps[prevIndex].title}`)
  }

  const handleStepClick = (index: number) => {
    // Only allow clicking on completed steps or the next available step
    if (
      completedSteps[steps[index].id] ||
      index === 0 ||
      (index <= currentStepIndex + 1 && (index === 0 || completedSteps[steps[index - 1].id]))
    ) {
      setCurrentStepIndex(index)
      announceMessage(`Step ${index + 1} of ${steps.length}: ${steps[index].title}`)
    }
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>{currentStep.title}</CardTitle>
        {currentStep.description && <p className="text-sm text-muted-foreground">{currentStep.description}</p>}
      </CardHeader>

      {/* Progress indicator */}
      <div className="px-6">
        <div className="mb-8 flex items-center justify-center">
          <nav aria-label="Progress" className="w-full">
            <ol role="list" className="flex items-center justify-between">
              {steps.map((step, index) => {
                const isCompleted = completedSteps[step.id]
                const isCurrent = index === currentStepIndex
                const isClickable =
                  isCompleted ||
                  index === 0 ||
                  (index <= currentStepIndex + 1 && (index === 0 || completedSteps[steps[index - 1].id]))

                return (
                  <li
                    key={step.id}
                    className={cn(
                      "relative flex items-center justify-center",
                      index !== steps.length - 1 ? "flex-1" : "flex-initial",
                    )}
                  >
                    {index !== 0 && (
                      <div
                        className={cn("absolute left-0 top-1/2 h-0.5 w-full -translate-x-1/2 transform bg-gray-200", {
                          "bg-primary": isCompleted,
                        })}
                        aria-hidden="true"
                      />
                    )}

                    <button
                      type="button"
                      className={cn(
                        "relative flex h-8 w-8 items-center justify-center rounded-full border-2 bg-background text-sm font-medium",
                        {
                          "border-primary text-primary": isCurrent,
                          "border-primary bg-primary text-primary-foreground": isCompleted,
                          "border-gray-300 text-gray-500": !isCurrent && !isCompleted,
                          "cursor-pointer hover:bg-muted": isClickable,
                          "cursor-not-allowed": !isClickable,
                        },
                      )}
                      onClick={() => isClickable && handleStepClick(index)}
                      disabled={!isClickable}
                      aria-current={isCurrent ? "step" : undefined}
                      aria-label={`${step.title}${
                        step.isOptional ? " (optional)" : ""
                      }, step ${index + 1} of ${steps.length}`}
                    >
                      {isCompleted ? <CheckCircle className="h-5 w-5" aria-hidden="true" /> : <span>{index + 1}</span>}
                    </button>

                    <div className="absolute mt-10 w-32 text-center text-xs font-medium">
                      <span
                        className={cn({
                          "text-primary": isCurrent || isCompleted,
                          "text-gray-500": !isCurrent && !isCompleted,
                        })}
                      >
                        {step.title}
                        {step.isOptional && <span className="text-muted-foreground"> (optional)</span>}
                      </span>
                    </div>
                  </li>
                )
              })}
            </ol>
          </nav>
        </div>
      </div>

      <CardContent>{currentStep.component}</CardContent>

      <CardFooter className="flex justify-between">
        <div>
          {onCancel && (
            <Button variant="ghost" onClick={onCancel} type="button">
              Cancel
            </Button>
          )}
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleBack} disabled={isFirstStep} type="button">
            Back
          </Button>
          <Button onClick={handleNext} type="button">
            {isLastStep ? "Complete" : "Next"}
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

