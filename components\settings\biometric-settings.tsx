"use client"

import { useState, useEffect } from "react"
import { <PERSON>gerprint, ScanFaceIcon as Face, Check, X } from "lucide-react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { BiometricAuthService } from "@/lib/auth/biometric-auth"
import { NativeHaptics, isNative, isIOS } from "@/lib/native/native-features"
import { useSession } from "next-auth/react"

export function BiometricSettings() {
  const { data: session } = useSession()
  const [isAvailable, setIsAvailable] = useState(false)
  const [isEnabled, setIsEnabled] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    async function checkBiometrics() {
      if (!isNative()) return

      const available = await BiometricAuthService.isAvailable()
      setIsAvailable(available)

      if (available) {
        const enabled = await BiometricAuthService.isEnabled()
        setIsEnabled(enabled)
      }
    }

    checkBiometrics()
  }, [])

  // If not on a native platform, don't render
  if (!isNative()) {
    return null
  }

  const handleToggleBiometrics = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      if (isEnabled) {
        // Disable biometrics
        await BiometricAuthService.disable()
        setIsEnabled(false)
        setSuccess("Biometric authentication has been disabled")
        await NativeHaptics.impact("light")
      } else {
        // Enable biometrics
        if (!session?.user?.token) {
          setError("Authentication token not available. Please log out and log in again.")
          return
        }

        const result = await BiometricAuthService.enable(session.user.token)

        if (result.success) {
          setIsEnabled(true)
          setSuccess("Biometric authentication has been enabled")
          await NativeHaptics.notification("success")
        } else {
          setError(result.error || "Failed to enable biometric authentication")
          await NativeHaptics.notification("error")
        }
      }
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred")
      await NativeHaptics.notification("error")
    } finally {
      setIsLoading(false)
    }
  }

  const getBiometricType = () => {
    if (isIOS()) {
      return "Face ID"
    } else {
      return "Fingerprint"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isIOS() ? <Face className="h-5 w-5" /> : <Fingerprint className="h-5 w-5" />}
          Biometric Authentication
        </CardTitle>
        <CardDescription>Use {getBiometricType()} to quickly and securely log in to StockSync</CardDescription>
      </CardHeader>

      <CardContent>
        {!isAvailable ? (
          <Alert>
            <AlertTitle>Not Available</AlertTitle>
            <AlertDescription>Biometric authentication is not available on this device.</AlertDescription>
          </Alert>
        ) : (
          <>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <X className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert variant="success" className="mb-4">
                <Check className="h-4 w-4" />
                <AlertDescription>{success}</AlertDescription>
              </Alert>
            )}

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Enable {getBiometricType()} Authentication</p>
                <p className="text-sm text-muted-foreground">
                  {isEnabled
                    ? `${getBiometricType()} authentication is currently enabled`
                    : `Use ${getBiometricType()} to log in to your account`}
                </p>
              </div>
              <Switch checked={isEnabled} onCheckedChange={handleToggleBiometrics} disabled={isLoading} />
            </div>
          </>
        )}
      </CardContent>

      {isAvailable && (
        <CardFooter className="flex justify-between border-t px-6 py-4">
          <p className="text-xs text-muted-foreground">
            Your biometric data never leaves your device and is not stored on our servers.
          </p>
        </CardFooter>
      )}
    </Card>
  )
}

