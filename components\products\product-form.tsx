"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useZodForm } from "@/hooks/use-zod-form"
import { productSchema } from "@/lib/validations/schema"
import { OptimizedImage } from "@/components/ui/optimized-image"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface ProductFormProps {
  product?: any
  categories: any[]
  suppliers: any[]
}

export function ProductForm({ product, categories, suppliers }: ProductFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [imagePreview, setImagePreview] = useState<string | null>(product?.imageUrl || null)

  const { values, errors, isSubmitting, setValue, validateField, handleSubmit } = useZodForm({
    schema: productSchema,
    defaultValues: product || {
      name: "",
      sku: "",
      description: "",
      categoryId: "",
      supplierId: "",
      costPrice: 0,
      sellingPrice: 0,
      quantity: 0,
      reorderLevel: 5,
      imageUrl: "",
    },
    onSubmit: async (data) => {
      try {
        const url = product ? `/api/products/${product.id}` : "/api/products"

        const method = product ? "PUT" : "POST"

        const response = await fetch(url, {
          method,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        })

        if (!response.ok) {
          throw new Error("Failed to save product")
        }

        toast({
          title: "Success",
          description: product ? "Product updated successfully" : "Product created successfully",
        })

        router.push("/products")
        router.refresh()
      } catch (error) {
        console.error("Error saving product:", error)
        toast({
          title: "Error",
          description: "Failed to save product. Please try again.",
          variant: "destructive",
        })
      }
    },
  })

  // Initialize form with product data if editing
  useEffect(() => {
    if (product) {
      Object.entries(product).forEach(([key, value]) => {
        if (key in productSchema.shape) {
          setValue(key as any, value as any)
        }
      })

      setImagePreview(product.imageUrl || null)
    }
  }, [product, setValue])

  // Handle image upload
  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Show preview
    const reader = new FileReader()
    reader.onload = () => {
      setImagePreview(reader.result as string)
    }
    reader.readAsDataURL(file)

    // Upload to server
    try {
      const formData = new FormData()
      formData.append("image", file)

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Failed to upload image")
      }

      const data = await response.json()
      setValue("imageUrl", data.url)
    } catch (error) {
      console.error("Error uploading image:", error)
      toast({
        title: "Error",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <form onSubmit={(e) => handleSubmit(e)}>
      <Card>
        <CardHeader>
          <CardTitle>{product ? "Edit Product" : "Add New Product"}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name</Label>
                <Input
                  id="name"
                  value={values.name || ""}
                  onChange={(e) => setValue("name", e.target.value)}
                  onBlur={() => validateField("name", values.name || "")}
                  aria-invalid={!!errors.name}
                  aria-errormessage={errors.name ? "name-error" : undefined}
                />
                {errors.name && (
                  <p id="name-error" className="text-sm text-destructive">
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  value={values.sku || ""}
                  onChange={(e) => setValue("sku", e.target.value)}
                  onBlur={() => validateField("sku", values.sku || "")}
                  aria-invalid={!!errors.sku}
                  aria-errormessage={errors.sku ? "sku-error" : undefined}
                />
                {errors.sku && (
                  <p id="sku-error" className="text-sm text-destructive">
                    {errors.sku}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={values.description || ""}
                  onChange={(e) => setValue("description", e.target.value)}
                  rows={4}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="categoryId">Category</Label>
                <Select
                  value={values.categoryId || ""}
                  onValueChange={(value) => setValue("categoryId", value)}
                  onOpenChange={() => validateField("categoryId", values.categoryId || "")}
                >
                  <SelectTrigger
                    id="categoryId"
                    aria-invalid={!!errors.categoryId}
                    aria-errormessage={errors.categoryId ? "category-error" : undefined}
                  >
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.categoryId && (
                  <p id="category-error" className="text-sm text-destructive">
                    {errors.categoryId}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="supplierId">Supplier</Label>
                <Select
                  value={values.supplierId || ""}
                  onValueChange={(value) => setValue("supplierId", value)}
                  onOpenChange={() => validateField("supplierId", values.supplierId || "")}
                >
                  <SelectTrigger
                    id="supplierId"
                    aria-invalid={!!errors.supplierId}
                    aria-errormessage={errors.supplierId ? "supplier-error" : undefined}
                  >
                    <SelectValue placeholder="Select a supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.supplierId && (
                  <p id="supplier-error" className="text-sm text-destructive">
                    {errors.supplierId}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="costPrice">Cost Price</Label>
                  <Input
                    id="costPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={values.costPrice || 0}
                    onChange={(e) => setValue("costPrice", Number.parseFloat(e.target.value))}
                    onBlur={() => validateField("costPrice", values.costPrice || 0)}
                    aria-invalid={!!errors.costPrice}
                    aria-errormessage={errors.costPrice ? "cost-price-error" : undefined}
                  />
                  {errors.costPrice && (
                    <p id="cost-price-error" className="text-sm text-destructive">
                      {errors.costPrice}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sellingPrice">Selling Price</Label>
                  <Input
                    id="sellingPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={values.sellingPrice || 0}
                    onChange={(e) => setValue("sellingPrice", Number.parseFloat(e.target.value))}
                    onBlur={() => validateField("sellingPrice", values.sellingPrice || 0)}
                    aria-invalid={!!errors.sellingPrice}
                    aria-errormessage={errors.sellingPrice ? "selling-price-error" : undefined}
                  />
                  {errors.sellingPrice && (
                    <p id="selling-price-error" className="text-sm text-destructive">
                      {errors.sellingPrice}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="0"
                    step="1"
                    value={values.quantity || 0}
                    onChange={(e) => setValue("quantity", Number.parseInt(e.target.value))}
                    onBlur={() => validateField("quantity", values.quantity || 0)}
                    aria-invalid={!!errors.quantity}
                    aria-errormessage={errors.quantity ? "quantity-error" : undefined}
                  />
                  {errors.quantity && (
                    <p id="quantity-error" className="text-sm text-destructive">
                      {errors.quantity}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reorderLevel">Reorder Level</Label>
                  <Input
                    id="reorderLevel"
                    type="number"
                    min="0"
                    step="1"
                    value={values.reorderLevel || 0}
                    onChange={(e) => setValue("reorderLevel", Number.parseInt(e.target.value))}
                    onBlur={() => validateField("reorderLevel", values.reorderLevel || 0)}
                    aria-invalid={!!errors.reorderLevel}
                    aria-errormessage={errors.reorderLevel ? "reorder-level-error" : undefined}
                  />
                  {errors.reorderLevel && (
                    <p id="reorder-level-error" className="text-sm text-destructive">
                      {errors.reorderLevel}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="image">Product Image</Label>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="cursor-pointer"
                />
              </div>
              <div className="flex justify-center">
                {imagePreview ? (
                  <OptimizedImage
                    src={imagePreview}
                    alt="Product preview"
                    width={200}
                    height={200}
                    className="h-40 w-40 rounded-md object-cover"
                  />
                ) : (
                  <div className="flex h-40 w-40 items-center justify-center rounded-md border border-dashed">
                    <span className="text-sm text-muted-foreground">No image</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {product ? "Update Product" : "Create Product"}
          </Button>
        </CardFooter>
      </Card>
    </form>
  )
}

