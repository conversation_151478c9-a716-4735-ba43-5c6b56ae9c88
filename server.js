const { createServer } = require("http")
const { parse } = require("url")
const next = require("next")
const cron = require("node-cron")

const dev = process.env.NODE_ENV !== "production"
const hostname = "localhost"
const port = process.env.PORT || 3000
const app = next({ dev, hostname, port })
const handle = app.getRequestHandler()

// Import the cron job functions
const { runDailyProcessing } = require("./lib/cron/recurring-order-processor")

app.prepare().then(() => {
  const server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true)
      await handle(req, res, parsedUrl)
    } catch (err) {
      console.error("Error occurred handling", req.url, err)
      res.statusCode = 500
      res.end("internal server error")
    }
  })

  // Schedule cron jobs
  // Run daily at 1:00 AM
  cron.schedule("0 1 * * *", async () => {
    console.log("Running daily processing tasks...")
    try {
      const result = await runDailyProcessing()
      console.log("Daily processing completed:", result)
    } catch (error) {
      console.error("Error in daily processing cron job:", error)
    }
  })

  server.listen(port, (err) => {
    if (err) throw err
    console.log(`> Ready on http://${hostname}:${port}`)
  })
})

