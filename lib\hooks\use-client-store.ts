"use client"

import { useEffect, useState } from "react"
import { useStore } from "@/lib/store"
import type { StoreState } from "@/lib/store"

// Client-only store hook that prevents SSR issues
export function useClientStore<T>(selector: (state: StoreState) => T): T | null {
  const [isClient, setIsClient] = useState(false)
  const [storeValue, setStoreValue] = useState<T | null>(null)

  useEffect(() => {
    setIsClient(true)
    // Get initial value
    setStoreValue(selector(useStore.getState()))
    
    // Subscribe to changes
    const unsubscribe = useStore.subscribe((state) => {
      setStoreValue(selector(state))
    })

    return unsubscribe
  }, [selector])

  if (!isClient) {
    return null
  }

  return storeValue
}

// Specific client-safe hooks
export function useClientSettingsStore() {
  return useClientStore((state) => ({
    settings: state.settings,
    updateSettings: state.updateSettings,
  }))
}

export function useClientAuthStore() {
  return useClientStore((state) => ({
    isAuthenticated: state.isAuthenticated,
    user: state.user,
    login: state.login,
    logout: state.logout,
  }))
}

export function useClientUIStore() {
  return useClientStore((state) => ({
    sidebarOpen: state.sidebarOpen,
    setSidebarOpen: state.setSidebarOpen,
    currentTheme: state.currentTheme,
    setCurrentTheme: state.setCurrentTheme,
    activeModal: state.activeModal,
    setActiveModal: state.setActiveModal,
  }))
}
