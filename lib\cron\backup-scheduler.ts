import cron from "node-cron"
import { prisma } from "@/lib/prisma"
import { createBackup, cleanupOldBackups } from "@/lib/services/backup-service"

// Initialize backup scheduler
export async function initBackupScheduler() {
  console.log("Initializing backup scheduler...")

  // Get backup schedule settings
  const settings = await prisma.setting.findUnique({
    where: { key: "backup_schedule" },
  })

  if (!settings) {
    console.log("No backup schedule settings found. Using defaults.")
    return
  }

  const scheduleSettings = JSON.parse(settings.value)

  if (!scheduleSettings.enabled) {
    console.log("Automatic backups are disabled.")
    return
  }

  // Parse time
  const [hour, minute] = scheduleSettings.time.split(":").map(Number)

  // Create cron expression based on frequency
  let cronExpression: string

  switch (scheduleSettings.frequency) {
    case "daily":
      cronExpression = `${minute} ${hour} * * *`
      break
    case "weekly":
      cronExpression = `${minute} ${hour} * * 0` // Sunday
      break
    case "monthly":
      cronExpression = `${minute} ${hour} 1 * *` // 1st day of month
      break
    default:
      cronExpression = `${minute} ${hour} * * *` // Default to daily
  }

  // Schedule backup job
  cron.schedule(cronExpression, async () => {
    console.log(`Running scheduled backup at ${new Date().toISOString()}`)

    try {
      // Create backup
      const result = await createBackup(`Scheduled ${scheduleSettings.frequency} backup`)

      if (result.success) {
        console.log(`Scheduled backup created successfully: ${result.backupId}`)

        // Clean up old backups based on retention policy
        if (scheduleSettings.retentionCount > 0) {
          await cleanupOldBackups(scheduleSettings.retentionCount)
        }
      } else {
        console.error(`Scheduled backup failed: ${result.error}`)
      }
    } catch (error) {
      console.error("Error in scheduled backup:", error)
    }
  })

  console.log(`Backup scheduler initialized with schedule: ${cronExpression}`)
}

