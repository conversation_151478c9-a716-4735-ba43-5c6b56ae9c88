import { type NextRequest, NextResponse } from "next/server"
import { Redis } from "@upstash/redis"
import { format, subDays } from "date-fns"

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
})

export async function GET(request: NextRequest) {
  try {
    // Get time range from query params
    const searchParams = request.nextUrl.searchParams
    const range = searchParams.get("range") || "7d"

    // Calculate start time based on range
    let startTime: number
    const now = Date.now()

    switch (range) {
      case "1d":
        startTime = subDays(now, 1).getTime()
        break
      case "30d":
        startTime = subDays(now, 30).getTime()
        break
      case "90d":
        startTime = subDays(now, 90).getTime()
        break
      case "7d":
      default:
        startTime = subDays(now, 7).getTime()
        break
    }

    // Get sessions in the time range
    const sessionKeys = await redis.zrangebyscore("analytics:sessions:timeline", startTime, now)

    // Get session data
    const sessions = []
    let totalSessionDuration = 0
    const deviceCounts: Record<string, number> = {}
    const platformCounts: Record<string, number> = {}

    for (const key of sessionKeys) {
      const session = await redis.get(key)
      if (session) {
        sessions.push(session)

        // Calculate total duration
        if (session.duration) {
          totalSessionDuration += session.duration
        }

        // Count by device
        if (session.deviceInfo?.model) {
          const deviceName = session.deviceInfo.model
          deviceCounts[deviceName] = (deviceCounts[deviceName] || 0) + 1
        }

        // Count by platform
        if (session.isNative !== undefined) {
          const platform = session.isNative ? "Native App" : "Web"
          platformCounts[platform] = (platformCounts[platform] || 0) + 1
        }
      }
    }

    // Get events in the time range
    const eventKeys = await redis.zrangebyscore("analytics:events:timeline", startTime, now)

    // Get event data
    const events = []
    const eventTypeCounts: Record<string, number> = {}
    const eventsByDay: Record<string, number> = {}

    for (const key of eventKeys) {
      const event = await redis.get(key)
      if (event) {
        events.push(event)

        // Count by event type
        eventTypeCounts[event.eventName] = (eventTypeCounts[event.eventName] || 0) + 1

        // Group by day
        const day = format(new Date(event.timestamp), "yyyy-MM-dd")
        eventsByDay[day] = (eventsByDay[day] || 0) + 1
      }
    }

    // Get screen view events
    const screenViewEvents = events.filter(
      (event) => event.eventName === "screen_view" && event.properties?.screen_name,
    )

    // Count views by screen
    const screenViewCounts: Record<string, number> = {}
    const screenTimeSpent: Record<string, number[]> = {}

    for (const event of screenViewEvents) {
      const screenName = event.properties.screen_name
      screenViewCounts[screenName] = (screenViewCounts[screenName] || 0) + 1

      // If we have time spent data
      if (event.properties.time_spent) {
        if (!screenTimeSpent[screenName]) {
          screenTimeSpent[screenName] = []
        }
        screenTimeSpent[screenName].push(event.properties.time_spent)
      }
    }

    // Calculate average time spent on screens
    const avgScreenTimeSpent: Record<string, number> = {}
    for (const [screen, times] of Object.entries(screenTimeSpent)) {
      avgScreenTimeSpent[screen] = times.reduce((sum, time) => sum + time, 0) / times.length
    }

    // Get performance metrics
    const loadTimeMetrics = await redis.zrangebyscore("analytics:metrics:page_load_time:timeline", startTime, now)

    const memoryMetrics = await redis.zrangebyscore("analytics:metrics:used_js_heap_size:timeline", startTime, now)

    // Process performance metrics
    let totalLoadTime = 0
    let loadTimeCount = 0
    const loadTimeByDevice: Record<string, number[]> = {}
    const performanceByDay: Record<string, { loadTimes: number[]; memoryUsages: number[] }> = {}

    // Process load time metrics
    for (const key of loadTimeMetrics) {
      const metric = await redis.get(key)
      if (metric) {
        totalLoadTime += metric.value
        loadTimeCount++

        // Group by device
        const deviceId = key.split(":")[2]
        const deviceInfo = await redis.get(`analytics:device:${deviceId}:info`)

        if (deviceInfo?.model) {
          if (!loadTimeByDevice[deviceInfo.model]) {
            loadTimeByDevice[deviceInfo.model] = []
          }
          loadTimeByDevice[deviceInfo.model].push(metric.value)
        }

        // Group by day
        const day = format(new Date(metric.timestamp), "yyyy-MM-dd")
        if (!performanceByDay[day]) {
          performanceByDay[day] = { loadTimes: [], memoryUsages: [] }
        }
        performanceByDay[day].loadTimes.push(metric.value)
      }
    }

    // Process memory metrics
    for (const key of memoryMetrics) {
      const metric = await redis.get(key)
      if (metric) {
        // Group by day
        const day = format(new Date(metric.timestamp), "yyyy-MM-dd")
        if (!performanceByDay[day]) {
          performanceByDay[day] = { loadTimes: [], memoryUsages: [] }
        }
        // Convert bytes to MB
        performanceByDay[day].memoryUsages.push(metric.value / (1024 * 1024))
      }
    }

    // Calculate average load time
    const averageLoadTime = loadTimeCount > 0 ? Math.round(totalLoadTime / loadTimeCount) : 0

    // Calculate average load time by device
    const loadTimeByDeviceAvg: { name: string; value: number }[] = []
    for (const [device, times] of Object.entries(loadTimeByDevice)) {
      loadTimeByDeviceAvg.push({
        name: device,
        value: Math.round(times.reduce((sum, time) => sum + time, 0) / times.length),
      })
    }

    // Calculate daily performance averages
    const performanceMetrics = Object.entries(performanceByDay)
      .map(([date, metrics]) => {
        const avgLoadTime =
          metrics.loadTimes.length > 0
            ? Math.round(metrics.loadTimes.reduce((sum, time) => sum + time, 0) / metrics.loadTimes.length)
            : 0

        const avgMemoryUsage =
          metrics.memoryUsages.length > 0
            ? Math.round((metrics.memoryUsages.reduce((sum, mem) => sum + mem, 0) / metrics.memoryUsages.length) * 10) /
              10
            : 0

        return {
          date,
          loadTime: avgLoadTime,
          memoryUsage: avgMemoryUsage,
        }
      })
      .sort((a, b) => a.date.localeCompare(b.date))

    // Count active sessions today
    const today = format(new Date(), "yyyy-MM-dd")
    const activeTodayCount = sessions.filter((session) => {
      const sessionDate = format(new Date(session.startTime), "yyyy-MM-dd")
      return sessionDate === today
    }).length

    // Format data for response
    const analyticsData = {
      sessions: {
        total: sessions.length,
        activeToday: activeTodayCount,
        averageDuration: sessions.length > 0 ? Math.round(totalSessionDuration / sessions.length / 1000) : 0,
        byDevice: Object.entries(deviceCounts).map(([name, value]) => ({ name, value })),
        byPlatform: Object.entries(platformCounts).map(([name, value]) => ({ name, value })),
      },
      events: {
        total: events.length,
        byType: Object.entries(eventTypeCounts).map(([name, value]) => ({ name, value })),
        byDay: Object.entries(eventsByDay)
          .map(([date, count]) => ({ date, count }))
          .sort((a, b) => a.date.localeCompare(b.date)),
      },
      screens: {
        byViews: Object.entries(screenViewCounts)
          .map(([name, views]) => ({ name, views }))
          .sort((a, b) => b.views - a.views),
        averageTimeSpent: Object.entries(avgScreenTimeSpent)
          .map(([name, seconds]) => ({ name, seconds: Math.round(seconds) }))
          .sort((a, b) => b.seconds - a.seconds),
      },
      performance: {
        averageLoadTime,
        byDevice: loadTimeByDeviceAvg,
        metrics: performanceMetrics,
      },
    }

    return NextResponse.json(analyticsData)
  } catch (error) {
    console.error("Error fetching analytics data:", error)

    return NextResponse.json({ error: "Failed to fetch analytics data" }, { status: 500 })
  }
}

