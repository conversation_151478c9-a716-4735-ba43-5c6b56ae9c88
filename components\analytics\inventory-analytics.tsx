"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"
import { Package, AlertTriangle, TrendingUp, DollarSign } from "lucide-react"

interface InventoryAnalyticsProps {
  data: any
}

export function InventoryAnalytics({ data }: InventoryAnalyticsProps) {
  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No inventory analytics data available</p>
      </div>
    )
  }

  const {
    inventoryStatus,
    inventoryTurnover,
    slowMovingProducts,
    fastMovingProducts,
    reorderPoints,
    predictedStockouts,
  } = data

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(value)
  }

  // Generate colors for pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

  // Prepare data for inventory value by category chart
  const inventoryValueByCategory = inventoryStatus.inventoryDetails.reduce((acc: any[], item: any) => {
    const existingCategory = acc.find((c) => c.name === item.category)
    if (existingCategory) {
      existingCategory.value += item.value
    } else {
      acc.push({
        name: item.category,
        value: item.value,
      })
    }
    return acc
  }, [])

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inventory Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(inventoryStatus.totalInventoryValue)}</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">{inventoryStatus.totalProducts} products in stock</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inventory Turnover</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inventoryTurnover.turnover.toFixed(2)}x</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">Annual turnover rate</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inventoryStatus.lowStockItems}</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">Items below threshold</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inventoryStatus.outOfStockItems}</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-muted-foreground">Items with zero stock</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Inventory Value by Category */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Value by Category</CardTitle>
          <CardDescription>Distribution of inventory value across categories</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={inventoryValueByCategory}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {inventoryValueByCategory.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Fast and Slow Moving Products */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Fast Moving Products</CardTitle>
            <CardDescription>Products with highest sales velocity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={fastMovingProducts.slice(0, 5)} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    tickFormatter={(value) => (value.length > 10 ? `${value.substring(0, 10)}...` : value)}
                  />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="salesQuantity" fill="#82ca9d" name="Sales Quantity" />
                  <Bar dataKey="daysOfInventoryLeft" fill="#8884d8" name="Days of Inventory Left" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Slow Moving Products</CardTitle>
            <CardDescription>Products with lowest sales velocity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={slowMovingProducts.slice(0, 5)} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    tickFormatter={(value) => (value.length > 10 ? `${value.substring(0, 10)}...` : value)}
                  />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="salesQuantity" fill="#ff8042" name="Sales Quantity" />
                  <Bar dataKey="inventoryValue" fill="#ffc658" name="Inventory Value" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Predicted Stockouts */}
      <Card>
        <CardHeader>
          <CardTitle>Predicted Stockouts</CardTitle>
          <CardDescription>Products at risk of running out of stock</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Current Stock</TableHead>
                  <TableHead>Daily Usage</TableHead>
                  <TableHead>Days Until Stockout</TableHead>
                  <TableHead>Risk Level</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {predictedStockouts.slice(0, 10).map((product: any) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell>{product.currentStock}</TableCell>
                    <TableCell>{product.dailyUsage.toFixed(2)}</TableCell>
                    <TableCell>{product.daysUntilStockout}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          product.riskLevel === "High"
                            ? "destructive"
                            : product.riskLevel === "Medium"
                              ? "default"
                              : "secondary"
                        }
                      >
                        {product.riskLevel}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Reorder Points */}
      <Card>
        <CardHeader>
          <CardTitle>Reorder Points</CardTitle>
          <CardDescription>Recommended inventory reorder points</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={reorderPoints.filter((p: any) => p.needsReorder).slice(0, 10)}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  tickFormatter={(value) => (value.length > 10 ? `${value.substring(0, 10)}...` : value)}
                />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="currentStock" fill="#8884d8" name="Current Stock" />
                <Bar dataKey="reorderPoint" fill="#82ca9d" name="Reorder Point" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

