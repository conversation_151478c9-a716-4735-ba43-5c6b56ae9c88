"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  FileSpreadsheet,
  FileText,
  FileIcon as FilePdf,
  Loader2,
  TableIcon,
} from "lucide-react"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"
import { Bar, Line, Pie } from "react-chartjs-2"

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement, Title, Tooltip, Legend)

interface ReportPreviewProps {
  reportData: any
  isLoading: boolean
  onExport: (format: "csv" | "excel" | "pdf") => void
}

export function ReportPreview({ reportData, isLoading, onExport }: ReportPreviewProps) {
  const [activeTab, setActiveTab] = useState("table")
  const [chartType, setChartType] = useState<"bar" | "line" | "pie">("bar")

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Executing report...</p>
      </div>
    )
  }

  if (!reportData) {
    return (
      <div className="text-center py-12 border rounded-md">
        <TableIcon className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
        <p className="text-muted-foreground">No report data available</p>
        <p className="text-sm text-muted-foreground">Run the report to see results</p>
      </div>
    )
  }

  const { data, summary, report, executedAt } = reportData

  // Prepare data for table view
  const columns = data.length > 0 ? Object.keys(data[0]) : []

  // Prepare data for chart view
  const prepareChartData = () => {
    if (data.length === 0 || columns.length === 0) return null

    // Find a suitable label column (prefer string/date columns)
    const labelColumn =
      columns.find((col) => typeof data[0][col] === "string" || data[0][col] instanceof Date) || columns[0]

    // Find numeric columns for data series
    const numericColumns = columns.filter((col) => typeof data[0][col] === "number" && col !== labelColumn)

    if (numericColumns.length === 0) return null

    // Limit to first 5 numeric columns to avoid cluttered charts
    const dataColumns = numericColumns.slice(0, 5)

    // Limit to 20 data points for readability
    const limitedData = data.slice(0, 20)

    // Extract labels
    const labels = limitedData.map((row) => {
      const value = row[labelColumn]
      if (value instanceof Date) {
        return value.toLocaleDateString()
      }
      return String(value || "")
    })

    // Extract datasets
    const datasets = dataColumns.map((column, index) => {
      // Generate a color based on index
      const hue = (index * 137) % 360
      const color = `hsl(${hue}, 70%, 60%)`

      return {
        label: column,
        data: limitedData.map((row) => row[column]),
        backgroundColor: chartType === "line" ? color : `hsla(${hue}, 70%, 60%, 0.6)`,
        borderColor: color,
        borderWidth: 1,
      }
    })

    return { labels, datasets }
  }

  const chartData = prepareChartData()

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: report.name,
      },
    },
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">{report.name}</h3>
          <p className="text-sm text-muted-foreground">Executed: {new Date(executedAt).toLocaleString()}</p>
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => onExport("csv")}>
            <FileText className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" size="sm" onClick={() => onExport("excel")}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" size="sm" onClick={() => onExport("pdf")}>
            <FilePdf className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Summary metrics */}
      {summary && (
        <Card>
          <CardContent className="p-4">
            <h4 className="text-sm font-medium mb-2">Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(summary).map(([key, value]) => (
                <div key={key} className="space-y-1">
                  <p className="text-xs text-muted-foreground capitalize">{key.replace(/_/g, " ")}</p>
                  <p className="text-lg font-medium">
                    {typeof value === "number" ? (value % 1 === 0 ? value : value.toFixed(2)) : String(value)}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 w-full">
          <TabsTrigger value="table">
            <TableIcon className="h-4 w-4 mr-2" />
            Table View
          </TabsTrigger>
          <TabsTrigger value="chart" disabled={!chartData}>
            <BarChart className="h-4 w-4 mr-2" />
            Chart View
          </TabsTrigger>
        </TabsList>

        <TabsContent value="table">
          <Card>
            <CardContent className="p-4">
              <div className="rounded-md border">
                <ScrollArea className="h-[500px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {columns.map((column) => (
                          <TableHead key={column}>{column}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data.map((row, rowIndex) => (
                        <TableRow key={rowIndex}>
                          {columns.map((column) => (
                            <TableCell key={`${rowIndex}-${column}`}>{formatCellValue(row[column])}</TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </div>

              <div className="mt-2 text-xs text-muted-foreground text-right">Showing {data.length} records</div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chart">
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-sm font-medium">Chart View</h4>
                <div className="flex space-x-2">
                  <Button
                    variant={chartType === "bar" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setChartType("bar")}
                  >
                    <BarChart className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={chartType === "line" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setChartType("line")}
                  >
                    <LineChart className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={chartType === "pie" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setChartType("pie")}
                  >
                    <PieChart className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="h-[400px] w-full">
                {chartData && (
                  <>
                    {chartType === "bar" && <Bar data={chartData} options={chartOptions} />}
                    {chartType === "line" && <Line data={chartData} options={chartOptions} />}
                    {chartType === "pie" && <Pie data={chartData} options={chartOptions} />}
                  </>
                )}
              </div>

              <div className="mt-2 text-xs text-muted-foreground text-right">Showing up to 20 data points</div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Helper function to format cell values for display
function formatCellValue(value: any): string {
  if (value === null || value === undefined) {
    return "-"
  }

  if (value instanceof Date) {
    return value.toLocaleString()
  }

  if (typeof value === "boolean") {
    return value ? "Yes" : "No"
  }

  if (typeof value === "number") {
    // Format with 2 decimal places if it has decimals
    return value % 1 === 0 ? value.toString() : value.toFixed(2)
  }

  return String(value)
}

