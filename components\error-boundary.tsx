"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { AlertTriangle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useTranslation } from "next-i18next"

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  resetKeys?: any[]
}

export function ErrorBoundary({ children, fallback, onError, resetKeys = [] }: ErrorBoundaryProps) {
  const [hasError, setHasError] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [errorInfo, setErrorInfo] = useState<React.ErrorInfo | null>(null)
  const { toast } = useToast()
  const { t } = useTranslation("common")

  // Reset error state when resetKeys change
  useEffect(() => {
    if (hasError) {
      setHasError(false)
      setError(null)
      setErrorInfo(null)
    }
  }, resetKeys)

  // Handle window errors
  useEffect(() => {
    const errorHandler = (event: ErrorEvent) => {
      console.error("Caught window error:", event.error)
      setHasError(true)
      setError(event.error || new Error("An unknown error occurred"))

      // Log error to analytics or monitoring service
      logError(event.error)

      // Notify user with toast
      toast({
        title: t("error.title"),
        description: t("error.unexpected"),
        variant: "destructive",
      })
    }

    const rejectionHandler = (event: PromiseRejectionEvent) => {
      console.error("Unhandled promise rejection:", event.reason)

      // Log error to analytics or monitoring service
      logError(event.reason)

      // Only show toast for unhandled rejections, don't set error state
      // as this might not affect the UI directly
      toast({
        title: t("error.title"),
        description: t("error.promise"),
        variant: "destructive",
      })
    }

    window.addEventListener("error", errorHandler)
    window.addEventListener("unhandledrejection", rejectionHandler)

    return () => {
      window.removeEventListener("error", errorHandler)
      window.removeEventListener("unhandledrejection", rejectionHandler)
    }
  }, [toast, t])

  // This method is called when an error occurs in a child component
  const componentDidCatch = (error: Error, errorInfo: React.ErrorInfo) => {
    console.error("Error caught by ErrorBoundary:", error, errorInfo)
    setHasError(true)
    setError(error)
    setErrorInfo(errorInfo)

    // Call onError callback if provided
    if (onError) {
      onError(error, errorInfo)
    }

    // Log error to analytics or monitoring service
    logError(error, errorInfo)
  }

  if (hasError) {
    // Use custom fallback if provided
    if (fallback) return <>{fallback}</>

    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] p-6 border rounded-lg bg-destructive/10 text-destructive">
        <AlertTriangle className="h-10 w-10 mb-4" />
        <h2 className="text-xl font-bold mb-2">{t("error.title")}</h2>
        <p className="text-sm mb-4 text-center max-w-md">{error?.message || t("error.unexpected")}</p>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => {
              setHasError(false)
              setError(null)
              setErrorInfo(null)
            }}
          >
            {t("error.tryAgain")}
          </Button>
          <Button
            variant="default"
            onClick={() => {
              window.location.reload()
            }}
          >
            {t("error.refresh")}
          </Button>
        </div>
        {process.env.NODE_ENV === "development" && errorInfo && (
          <details className="mt-4 p-2 border rounded bg-background/50 w-full">
            <summary className="cursor-pointer font-medium">{t("error.details")}</summary>
            <pre className="mt-2 text-xs overflow-auto p-2 bg-background">
              {error?.stack}
              <hr className="my-2" />
              {errorInfo.componentStack}
            </pre>
          </details>
        )}
      </div>
    )
  }

  return <>{children}</>
}

// Function to log errors to your monitoring service
function logError(error: any, errorInfo?: React.ErrorInfo) {
  // In a real app, you would send this to your error monitoring service
  // Example: Sentry.captureException(error, { extra: errorInfo })
  console.error("Error logged:", error, errorInfo)
}

