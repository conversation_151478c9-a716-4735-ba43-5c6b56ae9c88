import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { StoreProvider } from "@/components/providers/store-provider"
import { MobileFeaturesProvider } from "@/components/providers/mobile-features-provider"
import MobileFeaturesToolbar from "@/components/mobile/mobile-features-toolbar"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "StockSync - Inventory Management System",
  description: "A comprehensive inventory management system",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <StoreProvider>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
            <MobileFeaturesProvider>
              {children}
              <MobileFeaturesToolbar />
            </MobileFeaturesProvider>
          </ThemeProvider>
        </StoreProvider>
      </body>
    </html>
  )
}



import './globals.css'