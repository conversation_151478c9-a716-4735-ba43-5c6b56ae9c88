import Stripe from "stripe"
import type { IntegrationConfig } from "../integration-manager"

export class StripeProvider {
  private stripe: Stripe
  private config: IntegrationConfig

  constructor(config: IntegrationConfig) {
    if (!config.apiKey) {
      throw new Error("Stripe API key is required")
    }

    this.config = config
    this.stripe = new Stripe(config.apiKey, {
      apiVersion: "2023-10-16",
    })
  }

  /**
   * Test connection to Stripe
   */
  async testConnection() {
    try {
      const balance = await this.stripe.balance.retrieve()
      return {
        success: true,
        message: "Successfully connected to Stripe",
        data: balance,
      }
    } catch (error) {
      console.error("Error connecting to Stripe:", error)
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to connect to Stripe",
      }
    }
  }

  /**
   * Create a payment intent
   */
  async createPaymentIntent(amount: number, currency = "usd", metadata: Record<string, any> = {}) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount,
        currency,
        metadata,
      })

      return {
        success: true,
        paymentIntent,
      }
    } catch (error) {
      console.error("Error creating Stripe payment intent:", error)
      throw error
    }
  }

  /**
   * Create a customer
   */
  async createCustomer(data: {
    email: string
    name?: string
    phone?: string
    address?: {
      line1?: string
      line2?: string
      city?: string
      state?: string
      postal_code?: string
      country?: string
    }
    metadata?: Record<string, any>
  }) {
    try {
      const customer = await this.stripe.customers.create({
        email: data.email,
        name: data.name,
        phone: data.phone,
        address: data.address,
        metadata: data.metadata,
      })

      return {
        success: true,
        customer,
      }
    } catch (error) {
      console.error("Error creating Stripe customer:", error)
      throw error
    }
  }

  /**
   * Create a product
   */
  async createProduct(data: {
    name: string
    description?: string
    images?: string[]
    metadata?: Record<string, any>
    price?: {
      amount: number
      currency: string
      recurring?: {
        interval: "day" | "week" | "month" | "year"
        interval_count?: number
      }
    }
  }) {
    try {
      const product = await this.stripe.products.create({
        name: data.name,
        description: data.description,
        images: data.images,
        metadata: data.metadata,
      })

      let price

      if (data.price) {
        price = await this.stripe.prices.create({
          product: product.id,
          unit_amount: data.price.amount,
          currency: data.price.currency,
          recurring: data.price.recurring,
        })
      }

      return {
        success: true,
        product,
        price,
      }
    } catch (error) {
      console.error("Error creating Stripe product:", error)
      throw error
    }
  }

  /**
   *  {
      console.error('Error creating Stripe product:', error)
      throw error
    }
  }
  
  /**
   * Sync products from StockSync to Stripe
   */
  async syncProducts(products: any[]) {
    try {
      const results = []

      for (const product of products) {
        try {
          const stripeProduct = await this.createProduct({
            name: product.name,
            description: product.description,
            metadata: {
              stocksync_id: product.id,
              sku: product.sku,
            },
            price: {
              amount: Math.round(product.price * 100), // Convert to cents
              currency: "usd",
            },
          })

          results.push({
            success: true,
            stocksyncId: product.id,
            stripeId: stripeProduct.product.id,
            priceId: stripeProduct.price?.id,
          })
        } catch (error) {
          console.error(`Error syncing product ${product.id} to Stripe:`, error)
          results.push({
            success: false,
            stocksyncId: product.id,
            error: error instanceof Error ? error.message : "Unknown error",
          })
        }
      }

      return {
        success: true,
        results,
      }
    } catch (error) {
      console.error("Error syncing products to Stripe:", error)
      throw error
    }
  }

  /**
   * Sync customers from StockSync to Stripe
   */
  async syncCustomers(customers: any[]) {
    try {
      const results = []

      for (const customer of customers) {
        try {
          const stripeCustomer = await this.createCustomer({
            email: customer.email,
            name: `${customer.firstName} ${customer.lastName}`,
            phone: customer.phone,
            address: {
              line1: customer.address,
              city: customer.city,
              state: customer.state,
              postal_code: customer.postalCode,
              country: customer.country,
            },
            metadata: {
              stocksync_id: customer.id,
            },
          })

          results.push({
            success: true,
            stocksyncId: customer.id,
            stripeId: stripeCustomer.customer.id,
          })
        } catch (error) {
          console.error(`Error syncing customer ${customer.id} to Stripe:`, error)
          results.push({
            success: false,
            stocksyncId: customer.id,
            error: error instanceof Error ? error.message : "Unknown error",
          })
        }
      }

      return {
        success: true,
        results,
      }
    } catch (error) {
      console.error("Error syncing customers to Stripe:", error)
      throw error
    }
  }
}

