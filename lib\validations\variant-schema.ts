import { z } from "zod"

export const optionGroupSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Option name is required"),
  displayName: z.string().min(1, "Display name is required"),
  options: z.array(z.string()).min(1, "At least one option value is required"),
})

export const variantSchema = z.object({
  id: z.string().optional(),
  sku: z.string().min(1, "SKU is required"),
  barcode: z.string().optional().nullable(),
  price: z.coerce.number().min(0, "Price must be a positive number").optional().nullable(),
  cost: z.coerce.number().min(0, "Cost must be a positive number").optional().nullable(),
  stockQuantity: z.coerce
    .number()
    .int("Stock quantity must be an integer")
    .min(0, "Stock quantity must be a positive number"),
  isDefault: z.boolean().default(false),
  options: z
    .record(z.string(), z.string())
    .refine((options) => Object.keys(options).length > 0, "At least one option must be selected"),
})

export const productWithVariantsSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  sku: z.string().min(1, "SKU is required"),
  barcode: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  price: z.coerce.number().min(0, "Price must be a positive number"),
  cost: z.coerce.number().min(0, "Cost must be a positive number").optional().nullable(),
  taxRate: z.coerce.number().min(0, "Tax rate must be a positive number").optional().nullable(),
  stockQuantity: z.coerce
    .number()
    .int("Stock quantity must be an integer")
    .min(0, "Stock quantity must be a positive number"),
  reorderPoint: z.coerce
    .number()
    .int("Reorder point must be an integer")
    .min(0, "Reorder point must be a positive number")
    .optional()
    .nullable(),
  categoryId: z.string().min(1, "Category is required"),
  supplierId: z.string().optional().nullable(),
  image: z.string().optional().nullable(),
  hasVariants: z.boolean().default(false),
  optionGroups: z.array(optionGroupSchema).optional(),
  variants: z.array(variantSchema).optional(),
})

export type OptionGroupFormValues = z.infer<typeof optionGroupSchema>
export type VariantFormValues = z.infer<typeof variantSchema>
export type ProductWithVariantsFormValues = z.infer<typeof productWithVariantsSchema>

