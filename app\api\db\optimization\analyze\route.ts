import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { DbOptimizationService } from "@/lib/services/db-optimization"
import { ApiError } from "@/lib/api-error"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to analyze database
    if (session.user.role !== "ADMIN") {
      throw ApiError.forbidden("You do not have permission to analyze database performance")
    }

    // Analyze database performance
    const result = await DbOptimizationService.analyzePerformance()

    return NextResponse.json(result)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Database analysis error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

