"use client"

import { useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BiometricLogin } from "@/components/auth/biometric-login"
import { isNative, NativeHaptics } from "@/lib/native/native-features"
import { BiometricAuthService } from "@/lib/auth/biometric-auth"
import { useRouter } from "next/navigation"
import Link from "next/link"

export default function BiometricLoginPage() {
  const router = useRouter()

  useEffect(() => {
    async function checkBiometrics() {
      if (!isNative()) {
        // Redirect to regular login if not in native app
        router.push("/auth/login")
        return
      }

      const available = await BiometricAuthService.isAvailable()
      const enabled = available && (await BiometricAuthService.isEnabled())

      if (!available || !enabled) {
        // Redirect to regular login if biometrics not available or enabled
        router.push("/auth/login")
      } else {
        // Provide haptic feedback when page loads
        NativeHaptics.impact("light")
      }
    }

    checkBiometrics()
  }, [router])

  if (!isNative()) {
    return null
  }

  return (
    <div className="container max-w-md mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-screen">
      <div className="w-full space-y-6">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold">StockSync</h1>
          <p className="text-muted-foreground">Inventory Management System</p>
        </div>

        <BiometricLogin />

        <Card>
          <CardHeader>
            <CardTitle>Login Options</CardTitle>
            <CardDescription>Choose how you want to log in</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild variant="outline" className="w-full">
              <Link href="/auth/login">Use Email & Password</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

