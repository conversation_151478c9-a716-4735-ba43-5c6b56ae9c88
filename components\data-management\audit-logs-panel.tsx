"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Loader2, Search, RefreshCw, CalendarIcon, Info } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"
import { Pagination } from "@/components/ui/pagination"

export function AuditLogsPanel() {
  const [loading, setLoading] = useState(false)
  const [auditLogs, setAuditLogs] = useState<any[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
  })
  const [filters, setFilters] = useState({
    userId: "",
    entityType: "",
    entityId: "",
    action: "",
    startDate: null as Date | null,
    endDate: null as Date | null,
  })
  const [selectedLog, setSelectedLog] = useState<any>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)

  const loadAuditLogs = async (page = 1) => {
    setLoading(true)

    try {
      // Build query string
      const params = new URLSearchParams()
      params.append("page", page.toString())
      params.append("pageSize", pagination.pageSize.toString())

      if (filters.userId) {
        params.append("userId", filters.userId)
      }

      if (filters.entityType) {
        params.append("entityType", filters.entityType)
      }

      if (filters.entityId) {
        params.append("entityId", filters.entityId)
      }

      if (filters.action) {
        params.append("action", filters.action)
      }

      if (filters.startDate) {
        params.append("startDate", filters.startDate.toISOString())
      }

      if (filters.endDate) {
        params.append("endDate", filters.endDate.toISOString())
      }

      const response = await fetch(`/api/audit?${params.toString()}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to load audit logs")
      }

      setAuditLogs(result.auditLogs)
      setPagination(result.pagination)
    } catch (error) {
      console.error("Load audit logs error:", error)

      toast({
        title: "Failed to load audit logs",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    loadAuditLogs(1)
  }

  const handleReset = () => {
    setFilters({
      userId: "",
      entityType: "",
      entityId: "",
      action: "",
      startDate: null,
      endDate: null,
    })

    loadAuditLogs(1)
  }

  const handlePageChange = (page: number) => {
    loadAuditLogs(page)
  }

  const handleViewDetails = async (auditLogId: string) => {
    try {
      const response = await fetch(`/api/audit/${auditLogId}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to load audit log details")
      }

      setSelectedLog(result.auditLog)
      setDetailsOpen(true)
    } catch (error) {
      console.error("Load audit log details error:", error)

      toast({
        title: "Failed to load audit log details",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    }
  }

  // Load audit logs on initial render
  useEffect(() => {
    loadAuditLogs()
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Audit Logs</CardTitle>
        <CardDescription>View and search audit logs for system activities</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="entityType">Entity Type</Label>
              <Input
                id="entityType"
                value={filters.entityType}
                onChange={(e) => setFilters({ ...filters, entityType: e.target.value })}
                placeholder="e.g., Product, Order"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="entityId">Entity ID</Label>
              <Input
                id="entityId"
                value={filters.entityId}
                onChange={(e) => setFilters({ ...filters, entityId: e.target.value })}
                placeholder="Entity ID"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="action">Action</Label>
              <Select
                value={filters.action}
                onValueChange={(value) => setFilters({ ...filters, action: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Actions</SelectItem>
                  <SelectItem value="CREATE">Create</SelectItem>
                  <SelectItem value="UPDATE">Update</SelectItem>
                  <SelectItem value="DELETE">Delete</SelectItem>
                  <SelectItem value="LOGIN">Login</SelectItem>
                  <SelectItem value="LOGOUT">Logout</SelectItem>
                  <SelectItem value="EXPORT">Export</SelectItem>
                  <SelectItem value="IMPORT">Import</SelectItem>
                  <SelectItem value="ARCHIVE">Archive</SelectItem>
                  <SelectItem value="RESTORE">Restore</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="userId">User ID</Label>
              <Input
                id="userId"
                value={filters.userId}
                onChange={(e) => setFilters({ ...filters, userId: e.target.value })}
                placeholder="User ID"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.startDate ? (
                      format(filters.startDate, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.startDate || undefined}
                    onSelect={(date) => setFilters({ ...filters, startDate: date })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.endDate ? (
                      format(filters.endDate, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.endDate || undefined}
                    onSelect={(date) => setFilters({ ...filters, endDate: date })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          <div className="flex justify-between">
            <Button variant="outline" onClick={handleReset}>
              Reset Filters
            </Button>
            
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => loadAuditLogs(pagination.page)} disabled={loading}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </Button>
              
              <Button onClick={handleSearch} disabled={loading}>
                {loading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Search className="mr-2 h-4 w-4" />
                )}
                Search
              </Button>
            </div>
          </div>
          
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Action</TableHead>
                <TableHead>Entity Type</TableHead>
                <TableHead>Entity ID</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead>Details</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {auditLogs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center">
                    No audit logs found
                  </TableCell>
                </TableRow>
              ) : (
                auditLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>{log.action}</TableCell>
                    <TableCell>{log.entityType}</TableCell>
                    <TableCell>{log.entityId || "N/A"}</TableCell>
                    <TableCell>{log.user?.name || log.userId}</TableCell>
                    <TableCell>{format(new Date(log.createdAt), "PPP HH:mm:ss")}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetails(log.id)}
                      >
                        <Info className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          
          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      </CardContent>
      
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Audit Log Details</DialogTitle>
            <DialogDescription>
              Detailed information about the audit log
            </DialogDescription>
          </DialogHeader>
          
          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium">Action</h3>
                  <p>{selectedLog.action}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">Timestamp</h3>
                  <p>{format(new Date(selectedLog.createdAt), "PPP HH:mm:ss")}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">Entity Type</h3>
                  <p>{selectedLog.entityType}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">Entity ID</h3>
                  <p>{selectedLog.entityId || "N/A"}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">User</h3>
                  <p>{selectedLog.user?.name || selectedLog.userId}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">User Role</h3>
                  <p>{selectedLog.user?.role || "N/A"}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">IP Address</h3>
                  <p>{selectedLog.ipAddress || "N/A"}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium">User Agent</h3>
                  <p className="truncate">{selectedLog.userAgent || "N/A"}</p>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">Details</h3>
                <pre className="mt-2 rounded bg-muted p-4 overflow-auto">
                  {selectedLog.details ? JSON.stringify(JSON.parse(selectedLog.details), null, 2) : "No details available"}
                </pre>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </CardContent>
  </Card>
}

