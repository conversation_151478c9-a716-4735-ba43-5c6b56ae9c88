"use client"

import { useState, useEffect } from "react"
import { useMobile } from "@/hooks/use-mobile"

interface UseMobileDataOptions<T> {
  fetcher: () => Promise<T>
  initialData?: T
  revalidateOnFocus?: boolean
  revalidateOnReconnect?: boolean
  revalidateOnNetwork?: boolean
  dedupingInterval?: number
}

export function useMobileData<T>({
  fetcher,
  initialData,
  revalidateOnFocus = true,
  revalidateOnReconnect = true,
  revalidateOnNetwork = true,
  dedupingInterval = 5000, // 5 seconds
}: UseMobileDataOptions<T>) {
  const isMobile = useMobile()
  const [data, setData] = useState<T | undefined>(initialData)
  const [isLoading, setIsLoading] = useState<boolean>(!initialData)
  const [error, setError] = useState<Error | null>(null)
  const [lastFetchTime, setLastFetchTime] = useState<number>(0)

  const fetchData = async () => {
    const now = Date.now()

    // Dedupe requests within the deduping interval
    if (now - lastFetchTime < dedupingInterval) {
      return
    }

    setLastFetchTime(now)

    if (!data) {
      setIsLoading(true)
    }

    try {
      const result = await fetcher()
      setData(result)
      setError(null)

      // Store in localStorage for offline access if on mobile
      if (isMobile) {
        try {
          localStorage.setItem(`mobile-data-${fetcher.toString()}`, JSON.stringify(result))
        } catch (e) {
          console.warn("Failed to cache data in localStorage", e)
        }
      }
    } catch (err) {
      setError(err as Error)

      // Try to load from cache if on mobile and fetch failed
      if (isMobile) {
        try {
          const cached = localStorage.getItem(`mobile-data-${fetcher.toString()}`)
          if (cached) {
            setData(JSON.parse(cached))
          }
        } catch (e) {
          console.warn("Failed to load cached data", e)
        }
      }
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchData()

    // Set up event listeners for revalidation
    const onFocus = () => {
      if (revalidateOnFocus) fetchData()
    }

    const onReconnect = () => {
      if (revalidateOnReconnect) fetchData()
    }

    const onOnline = () => {
      if (revalidateOnNetwork) fetchData()
    }

    window.addEventListener("focus", onFocus)
    window.addEventListener("online", onOnline)

    // Custom event for when connection is re-established
    window.addEventListener("connection-restored", onReconnect)

    return () => {
      window.removeEventListener("focus", onFocus)
      window.removeEventListener("online", onOnline)
      window.removeEventListener("connection-restored", onReconnect)
    }
  }, [fetcher, revalidateOnFocus, revalidateOnReconnect, revalidateOnNetwork])

  return {
    data,
    isLoading,
    error,
    mutate: fetchData,
  }
}

