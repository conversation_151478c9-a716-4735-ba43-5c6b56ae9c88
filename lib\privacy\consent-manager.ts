import { db } from "../db"

export enum ConsentType {
  MARKETING_EMAIL = "marketing_email",
  MARKETING_SMS = "marketing_sms",
  DATA_ANALYTICS = "data_analytics",
  THIRD_PARTY_SHARING = "third_party_sharing",
  COOKIES_ESSENTIAL = "cookies_essential",
  COOKIES_ANALYTICS = "cookies_analytics",
  COOKIES_MARKETING = "cookies_marketing",
}

export interface ConsentRecord {
  id: string
  userId: string
  consentType: ConsentType
  granted: boolean
  timestamp: Date
  expiresAt?: Date
  ipAddress?: string
  userAgent?: string
}

export class ConsentManager {
  /**
   * Record a user's consent
   */
  static async recordConsent(
    userId: string,
    consentType: ConsentType,
    granted: boolean,
    ipAddress?: string,
    userAgent?: string,
    expiresAt?: Date,
  ): Promise<ConsentRecord> {
    return db.consents.create({
      data: {
        userId,
        consentType,
        granted,
        timestamp: new Date(),
        expiresAt,
        ipAddress,
        userAgent,
      },
    })
  }

  /**
   * Check if a user has granted a specific consent
   */
  static async hasConsent(userId: string, consentType: ConsentType): Promise<boolean> {
    const consent = await db.consents.findFirst({
      where: {
        userId,
        consentType,
        granted: true,
        OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
      },
      orderBy: {
        timestamp: "desc",
      },
    })

    return !!consent
  }

  /**
   * Get all consents for a user
   */
  static async getUserConsents(userId: string): Promise<{
    [key in ConsentType]?: boolean
  }> {
    const consents = await db.consents.findMany({
      where: {
        userId,
        OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
      },
      orderBy: {
        timestamp: "desc",
      },
      distinct: ["consentType"],
    })

    const result: { [key in ConsentType]?: boolean } = {}

    for (const consent of consents) {
      result[consent.consentType as ConsentType] = consent.granted
    }

    return result
  }

  /**
   * Revoke a specific consent
   */
  static async revokeConsent(userId: string, consentType: ConsentType): Promise<void> {
    await this.recordConsent(userId, consentType, false)
  }

  /**
   * Get consent history for a user
   */
  static async getConsentHistory(userId: string, consentType?: ConsentType): Promise<ConsentRecord[]> {
    return db.consents.findMany({
      where: {
        userId,
        ...(consentType ? { consentType } : {}),
      },
      orderBy: {
        timestamp: "desc",
      },
    })
  }
}

