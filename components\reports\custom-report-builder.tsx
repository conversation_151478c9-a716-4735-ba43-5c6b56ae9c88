"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ReportFieldSelector } from "@/components/reports/report-field-selector"
import { ReportFilterBuilder } from "@/components/reports/report-filter-builder"
import { ReportSortBuilder } from "@/components/reports/report-sort-builder"
import { ReportGroupBySelector } from "@/components/reports/report-group-by-selector"
import { ReportPreview } from "@/components/reports/report-preview"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"
import { Loader2, Save, Play, ArrowLeft } from "lucide-react"

interface CustomReportBuilderProps {
  reportId?: string
}

export function CustomReportBuilder({ reportId }: CustomReportBuilderProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("data-source")
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [availableFields, setAvailableFields] = useState<any[]>([])
  const [reportData, setReportData] = useState<any>(null)

  // Report state
  const [report, setReport] = useState({
    id: "",
    name: "",
    description: "",
    entity: "products",
    fields: [] as string[],
    filters: [] as any[],
    sortFields: [] as any[],
    groupByFields: [] as string[],
    isPublic: false,
  })

  // Load report data if editing
  useEffect(() => {
    if (reportId) {
      loadReport(reportId)
    }
  }, [reportId])

  // Load available fields when entity changes
  useEffect(() => {
    loadAvailableFields(report.entity)
  }, [report.entity])

  // Load report data
  const loadReport = async (id: string) => {
    setIsLoading(true)
    try {
      const data = await fetchApi(`/api/reports/custom/${id}`)
      setReport({
        ...data,
        fields: data.fields || [],
        filters: data.filters || [],
        sortFields: data.sortFields || [],
        groupByFields: data.groupByFields || [],
      })
    } catch (error) {
      console.error("Error loading report:", error)
      toast({
        title: "Error",
        description: "Failed to load report",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Load available fields for the selected entity
  const loadAvailableFields = async (entity: string) => {
    setIsLoading(true)
    try {
      const data = await fetchApi(`/api/reports/fields?entity=${entity}`)
      setAvailableFields(data)
    } catch (error) {
      console.error("Error loading fields:", error)
      toast({
        title: "Error",
        description: "Failed to load available fields",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Save report
  const saveReport = async () => {
    // Validate report
    if (!report.name) {
      toast({
        title: "Validation Error",
        description: "Report name is required",
        variant: "destructive",
      })
      return
    }

    if (report.fields.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one field",
        variant: "destructive",
      })
      setActiveTab("fields")
      return
    }

    setIsSaving(true)

    try {
      const method = report.id ? "PUT" : "POST"
      const url = report.id ? `/api/reports/custom/${report.id}` : "/api/reports/custom"

      const savedReport = await fetchApi(url, {
        method,
        body: JSON.stringify(report),
      })

      toast({
        title: "Success",
        description: "Report saved successfully",
      })

      if (!report.id) {
        // If it's a new report, update the ID
        setReport((prev) => ({ ...prev, id: savedReport.id }))
      }

      // Navigate back to reports list
      router.push("/dashboard/reports/custom")
    } catch (error) {
      console.error("Error saving report:", error)
      toast({
        title: "Error",
        description: "Failed to save report",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Run report
  const runReport = async () => {
    // Validate report
    if (report.fields.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one field",
        variant: "destructive",
      })
      setActiveTab("fields")
      return
    }

    setIsRunning(true)

    try {
      const data = await fetchApi("/api/reports/custom/execute", {
        method: "POST",
        body: JSON.stringify({
          entity: report.entity,
          fields: report.fields,
          filters: report.filters,
          sortFields: report.sortFields,
          groupByFields: report.groupByFields,
        }),
      })

      setReportData({
        ...data,
        report: {
          name: report.name || "Untitled Report",
          description: report.description,
        },
      })

      setActiveTab("preview")
    } catch (error) {
      console.error("Error running report:", error)
      toast({
        title: "Error",
        description: "Failed to run report",
        variant: "destructive",
      })
    } finally {
      setIsRunning(false)
    }
  }

  // Export report
  const exportReport = async (format: "csv" | "excel" | "pdf") => {
    if (!reportData) return

    try {
      const response = await fetchApi("/api/reports/custom/export", {
        method: "POST",
        body: JSON.stringify({
          data: reportData.data,
          format,
          reportName: report.name,
        }),
      })

      // Create a download link
      const downloadUrl = response.downloadUrl
      if (downloadUrl) {
        const link = document.createElement("a")
        link.href = downloadUrl
        link.download = `${report.name}.${format}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    } catch (error) {
      console.error("Error exporting report:", error)
      toast({
        title: "Error",
        description: "Failed to export report",
        variant: "destructive",
      })
    }
  }

  // Handle entity change
  const handleEntityChange = (entity: string) => {
    // Reset fields when entity changes
    setReport((prev) => ({
      ...prev,
      entity,
      fields: [],
      filters: [],
      sortFields: [],
      groupByFields: [],
    }))

    // Reset report data
    setReportData(null)
  }

  // Get available entities
  const entities = [
    { value: "products", label: "Products" },
    { value: "orders", label: "Orders" },
    { value: "customers", label: "Customers" },
    { value: "inventory", label: "Inventory" },
    { value: "sales", label: "Sales" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => router.push("/dashboard/reports/custom")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Reports
        </Button>

        <div className="flex space-x-2">
          <Button variant="outline" onClick={runReport} disabled={isRunning || report.fields.length === 0}>
            {isRunning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Run Report
              </>
            )}
          </Button>

          <Button onClick={saveReport} disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Report
              </>
            )}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Report Details</CardTitle>
          <CardDescription>Configure your custom report</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="report-name">Report Name</Label>
              <Input
                id="report-name"
                value={report.name}
                onChange={(e) => setReport((prev) => ({ ...prev, name: e.target.value }))}
                placeholder="Enter report name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="report-entity">Data Source</Label>
              <Select
                value={report.entity}
                onValueChange={handleEntityChange}
                disabled={!!reportId} // Can't change entity when editing
              >
                <SelectTrigger id="report-entity">
                  <SelectValue placeholder="Select data source" />
                </SelectTrigger>
                <SelectContent>
                  {entities.map((entity) => (
                    <SelectItem key={entity.value} value={entity.value}>
                      {entity.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <Label htmlFor="report-description">Description (Optional)</Label>
            <Textarea
              id="report-description"
              value={report.description}
              onChange={(e) => setReport((prev) => ({ ...prev, description: e.target.value }))}
              placeholder="Enter report description"
              rows={2}
            />
          </div>

          <div className="mt-4 flex items-center space-x-2">
            <Switch
              id="report-public"
              checked={report.isPublic}
              onCheckedChange={(checked) => setReport((prev) => ({ ...prev, isPublic: checked }))}
            />
            <Label htmlFor="report-public">Make this report available to all users</Label>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="data-source">Data Source</TabsTrigger>
          <TabsTrigger value="fields">Fields</TabsTrigger>
          <TabsTrigger value="filters">Filters</TabsTrigger>
          <TabsTrigger value="sorting">Sorting & Grouping</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <TabsContent value="data-source">
            <Card>
              <CardHeader>
                <CardTitle>Data Source</CardTitle>
                <CardDescription>Select the data source for your report</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p>
                    You've selected <strong>{entities.find((e) => e.value === report.entity)?.label}</strong> as your
                    data source.
                  </p>

                  <p className="text-muted-foreground">
                    This data source provides information about{" "}
                    {report.entity === "products" &&
                      "your product catalog, including inventory levels, prices, and categories."}
                    {report.entity === "orders" &&
                      "customer orders, including order details, status, and payment information."}
                    {report.entity === "customers" &&
                      "your customers, including contact information, purchase history, and account status."}
                    {report.entity === "inventory" &&
                      "your inventory, including stock levels, movements, and valuations."}
                    {report.entity === "sales" &&
                      "your sales performance, including revenue, profit margins, and sales trends."}
                  </p>

                  <div className="bg-muted p-4 rounded-md">
                    <h4 className="font-medium mb-2">Next Steps</h4>
                    <p className="text-sm text-muted-foreground">
                      Click on the "Fields" tab to select which data fields to include in your report.
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={() => setActiveTab("fields")}>Continue to Fields</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="fields">
            <Card>
              <CardHeader>
                <CardTitle>Select Fields</CardTitle>
                <CardDescription>Choose which fields to include in your report</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <ReportFieldSelector
                    availableFields={availableFields}
                    selectedFields={report.fields}
                    onFieldsChange={(fields) => setReport((prev) => ({ ...prev, fields }))}
                  />
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveTab("data-source")}>
                  Back
                </Button>
                <Button onClick={() => setActiveTab("filters")}>Continue to Filters</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="filters">
            <Card>
              <CardHeader>
                <CardTitle>Configure Filters</CardTitle>
                <CardDescription>Define conditions to filter your report data</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <ReportFilterBuilder
                    availableFields={availableFields}
                    filters={report.filters}
                    onFiltersChange={(filters) => setReport((prev) => ({ ...prev, filters }))}
                  />
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveTab("fields")}>
                  Back
                </Button>
                <Button onClick={() => setActiveTab("sorting")}>Continue to Sorting & Grouping</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="sorting">
            <Card>
              <CardHeader>
                <CardTitle>Sorting & Grouping</CardTitle>
                <CardDescription>Configure how your report data is organized</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {isLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <>
                    <div>
                      <h3 className="text-lg font-medium mb-4">Sort Order</h3>
                      <ReportSortBuilder
                        availableFields={availableFields}
                        sortFields={report.sortFields}
                        onSortFieldsChange={(sortFields) => setReport((prev) => ({ ...prev, sortFields }))}
                      />
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium mb-4">Group By</h3>
                      <ReportGroupBySelector
                        availableFields={availableFields}
                        selectedFields={report.groupByFields}
                        onFieldsChange={(groupByFields) => setReport((prev) => ({ ...prev, groupByFields }))}
                      />
                    </div>
                  </>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveTab("filters")}>
                  Back
                </Button>
                <Button onClick={() => setActiveTab("preview")}>Continue to Preview</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="preview">
            <Card>
              <CardHeader>
                <CardTitle>Report Preview</CardTitle>
                <CardDescription>Preview and run your report</CardDescription>
              </CardHeader>
              <CardContent>
                {!reportData && !isRunning ? (
                  <div className="text-center py-12 border rounded-md">
                    <h3 className="text-lg font-medium mb-2">No Preview Available</h3>
                    <p className="text-muted-foreground mb-4">Click "Run Report" to see a preview of your report</p>
                    <Button onClick={runReport}>
                      <Play className="mr-2 h-4 w-4" />
                      Run Report
                    </Button>
                  </div>
                ) : (
                  <ReportPreview reportData={reportData} isLoading={isRunning} onExport={exportReport} />
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveTab("sorting")}>
                  Back
                </Button>
                <Button onClick={saveReport} disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Report
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
}

