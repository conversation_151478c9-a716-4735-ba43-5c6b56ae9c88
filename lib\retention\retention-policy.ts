import { db } from "../db"

export enum DataCategory {
  CUSTOMER = "customer",
  ORDER = "order",
  PRODUCT = "product",
  INVENTORY = "inventory",
  ANALYTICS = "analytics",
  AUDIT_LOGS = "audit_logs",
  USER_ACTIVITY = "user_activity",
}

export interface RetentionPolicy {
  id: string
  dataCategory: DataCategory
  retentionPeriodDays: number
  archiveBeforeDelete: boolean
  createdAt: Date
  updatedAt: Date
}

export class RetentionPolicyService {
  /**
   * Initialize default retention policies
   */
  static async initialize(): Promise<void> {
    const defaultPolicies = [
      {
        dataCategory: DataCategory.CUSTOMER,
        retentionPeriodDays: 365 * 7, // 7 years
        archiveBeforeDelete: true,
      },
      {
        dataCategory: DataCategory.ORDER,
        retentionPeriodDays: 365 * 7, // 7 years
        archiveBeforeDelete: true,
      },
      {
        dataCategory: DataCategory.PRODUCT,
        retentionPeriodDays: 365 * 3, // 3 years after discontinuation
        archiveBeforeDelete: true,
      },
      {
        dataCategory: DataCategory.INVENTORY,
        retentionPeriodDays: 365 * 2, // 2 years
        archiveBeforeDelete: true,
      },
      {
        dataCategory: DataCategory.ANALYTICS,
        retentionPeriodDays: 365, // 1 year
        archiveBeforeDelete: true,
      },
      {
        dataCategory: DataCategory.AUDIT_LOGS,
        retentionPeriodDays: 365 * 2, // 2 years
        archiveBeforeDelete: true,
      },
      {
        dataCategory: DataCategory.USER_ACTIVITY,
        retentionPeriodDays: 180, // 6 months
        archiveBeforeDelete: false,
      },
    ]

    for (const policy of defaultPolicies) {
      const existingPolicy = await db.retentionPolicies.findFirst({
        where: {
          dataCategory: policy.dataCategory,
        },
      })

      if (!existingPolicy) {
        await db.retentionPolicies.create({
          data: {
            ...policy,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        })
      }
    }
  }

  /**
   * Get all retention policies
   */
  static async getAllPolicies(): Promise<RetentionPolicy[]> {
    return db.retentionPolicies.findMany({
      orderBy: { dataCategory: "asc" },
    })
  }

  /**
   * Get a retention policy by data category
   */
  static async getPolicyByCategory(dataCategory: DataCategory): Promise<RetentionPolicy | null> {
    return db.retentionPolicies.findFirst({
      where: { dataCategory },
    })
  }

  /**
   * Update a retention policy
   */
  static async updatePolicy(
    dataCategory: DataCategory,
    retentionPeriodDays: number,
    archiveBeforeDelete: boolean,
  ): Promise<RetentionPolicy> {
    const existingPolicy = await db.retentionPolicies.findFirst({
      where: { dataCategory },
    })

    if (existingPolicy) {
      return db.retentionPolicies.update({
        where: { id: existingPolicy.id },
        data: {
          retentionPeriodDays,
          archiveBeforeDelete,
          updatedAt: new Date(),
        },
      })
    } else {
      return db.retentionPolicies.create({
        data: {
          dataCategory,
          retentionPeriodDays,
          archiveBeforeDelete,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      })
    }
  }

  /**
   * Calculate the expiration date for a data category
   */
  static async calculateExpirationDate(
    dataCategory: DataCategory,
    referenceDate: Date = new Date(),
  ): Promise<Date | null> {
    const policy = await this.getPolicyByCategory(dataCategory)

    if (!policy) {
      return null
    }

    const expirationDate = new Date(referenceDate)
    expirationDate.setDate(expirationDate.getDate() + policy.retentionPeriodDays)

    return expirationDate
  }
}

