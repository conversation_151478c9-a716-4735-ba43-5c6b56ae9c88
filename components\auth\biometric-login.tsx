"use client"

import { useState, useEffect } from "react"
import { Fingerprint, ScanFaceIcon as Face, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { BiometricAuthService } from "@/lib/auth/biometric-auth"
import { NativeHaptics, isNative } from "@/lib/native/native-features"
import { signIn } from "next-auth/react"
import { useRouter } from "next/navigation"

export function BiometricLogin() {
  const router = useRouter()
  const [isAvailable, setIsAvailable] = useState(false)
  const [isEnabled, setIsEnabled] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function checkBiometrics() {
      if (!isNative()) return

      const available = await BiometricAuthService.isAvailable()
      setIsAvailable(available)

      if (available) {
        const enabled = await BiometricAuthService.isEnabled()
        setIsEnabled(enabled)
      }
    }

    checkBiometrics()
  }, [])

  // If biometrics are not available or not enabled, don't render anything
  if (!isAvailable || !isEnabled) {
    return null
  }

  const handleBiometricLogin = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await BiometricAuthService.authenticate()

      if (result.success && result.token) {
        // Provide haptic feedback for successful authentication
        await NativeHaptics.notification("success")

        // Use the token to sign in
        const signInResult = await signIn("credentials", {
          biometricToken: result.token,
          redirect: false,
        })

        if (signInResult?.error) {
          setError("Authentication failed. Please try again or use password.")
          await NativeHaptics.notification("error")
        } else {
          // Redirect to dashboard on success
          router.push("/dashboard")
        }
      } else {
        setError(result.error || "Authentication failed")
        await NativeHaptics.notification("error")
      }
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred")
      await NativeHaptics.notification("error")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="flex flex-col items-center">
          <div className="mb-4">
            {isNative() && isNative() ? (
              <Face className="h-12 w-12 text-primary" />
            ) : (
              <Fingerprint className="h-12 w-12 text-primary" />
            )}
          </div>

          <h3 className="text-lg font-medium mb-2">Biometric Login</h3>
          <p className="text-sm text-muted-foreground mb-4 text-center">
            Log in quickly and securely using your biometrics
          </p>

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button onClick={handleBiometricLogin} className="w-full" disabled={isLoading}>
            {isLoading ? "Authenticating..." : "Login with Biometrics"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

