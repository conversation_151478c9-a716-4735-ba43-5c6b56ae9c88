import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { AuditService } from "@/lib/services/audit-service"
import { ApiError } from "@/lib/api-error"

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to view audit logs
    if (session.user.role !== "ADMIN" && session.user.role !== "MANAGER") {
      throw ApiError.forbidden("You do not have permission to view audit logs")
    }

    const auditLogId = params.id

    if (!auditLogId) {
      throw ApiError.badRequest("No audit log ID provided")
    }

    // Get audit log details
    const result = await AuditService.getAuditLogDetails(auditLogId)

    return NextResponse.json(result)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Get audit log details error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

