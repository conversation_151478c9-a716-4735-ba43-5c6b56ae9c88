import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { MigrationService } from "@/lib/services/migration-service"
import { ApiError } from "@/lib/api-error"
import { AuditService, AuditAction } from "@/lib/services/audit-service"

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to import data
    if (session.user.role !== "ADMIN" && session.user.role !== "MANAGER") {
      throw ApiError.forbidden("You do not have permission to import data")
    }

    const formData = await req.formData()
    const file = formData.get("file") as File
    const entityType = formData.get("entityType") as string
    const updateExisting = formData.get("updateExisting") === "true"
    const skipErrors = formData.get("skipErrors") === "true"
    const validateOnly = formData.get("validateOnly") === "true"

    if (!file) {
      throw ApiError.badRequest("No file provided")
    }

    if (!entityType) {
      throw ApiError.badRequest("No entity type provided")
    }

    // Import data
    const result = await MigrationService.importData({
      entityType: entityType as any,
      file,
      userId: session.user.id,
      options: {
        updateExisting,
        skipErrors,
        validateOnly,
      },
    })

    // Log audit event
    await AuditService.log({
      action: AuditAction.IMPORT,
      entityType,
      userId: session.user.id,
      details: {
        fileName: file.name,
        entityType,
        recordCount: result.successCount + result.errorCount,
        successCount: result.successCount,
        errorCount: result.errorCount,
      },
      ipAddress: req.headers.get("x-forwarded-for") || req.ip,
      userAgent: req.headers.get("user-agent"),
    })

    return NextResponse.json(result)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Import error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

