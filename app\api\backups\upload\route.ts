import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { writeFile } from "fs/promises"
import { v4 as uuidv4 } from "uuid"
import { format } from "date-fns"
import path from "path"
import { mkdir } from "fs/promises"
import { createGunzip } from "zlib"
import { pipeline } from "stream/promises"
import { Readable } from "stream"
import { prisma } from "@/lib/prisma"

// Define backup directory
const BACKUP_DIR = process.env.BACKUP_DIR || path.join(process.cwd(), "backups")

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Ensure backup directory exists
    await mkdir(BACKUP_DIR, { recursive: true })

    // Get the uploaded file
    const formData = await req.formData()
    const file = formData.get("file") as File

    if (!file) {
      return NextResponse.json({ error: "No file uploaded" }, { status: 400 })
    }

    // Check file type
    const fileName = file.name.toLowerCase()
    if (!fileName.endsWith(".json") && !fileName.endsWith(".gz")) {
      return NextResponse.json({ error: "Invalid file type. Only .json and .gz files are supported" }, { status: 400 })
    }

    // Generate backup ID and path
    const backupId = uuidv4()
    const timestamp = new Date()
    const formattedDate = format(timestamp, "yyyy-MM-dd-HH-mm-ss")
    const backupName = `uploaded-${formattedDate}-${backupId.slice(0, 8)}`
    const backupFilePath = path.join(BACKUP_DIR, `${backupName}.json`)

    // Read file content
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // If it's a gzip file, decompress it
    let jsonData: string
    if (fileName.endsWith(".gz")) {
      // Decompress the gzip file
      const gunzip = createGunzip()
      const source = Readable.from(buffer)

      const chunks: Buffer[] = []
      gunzip.on("data", (chunk) => {
        chunks.push(chunk)
      })

      await pipeline(source, gunzip)

      jsonData = Buffer.concat(chunks).toString("utf-8")
    } else {
      // It's already a JSON file
      jsonData = buffer.toString("utf-8")
    }

    // Parse the JSON data
    const backupData = JSON.parse(jsonData)

    // Validate backup data structure
    if (!backupData || typeof backupData !== "object") {
      return NextResponse.json({ error: "Invalid backup file format" }, { status: 400 })
    }

    // Write the backup file
    await writeFile(backupFilePath, JSON.stringify(backupData, null, 2))

    // Create backup metadata
    const fileStats = { size: buffer.length }

    // Count tables and records
    const tables = Object.keys(backupData).map((tableName) => ({
      name: tableName,
      count: Array.isArray(backupData[tableName]) ? backupData[tableName].length : 0,
    }))

    // Save backup metadata to database
    const backup = await prisma.backup.create({
      data: {
        id: backupId,
        name: backupName,
        description: `Uploaded backup from ${file.name}`,
        filePath: backupFilePath,
        size: fileStats.size,
        tables: JSON.stringify(tables),
        createdAt: timestamp,
      },
    })

    // Start a transaction for the restore process
    await prisma.$transaction(async (tx) => {
      // For each table in the backup, restore the data
      for (const [tableName, records] of Object.entries(backupData)) {
        // Skip if no records to restore
        if (!records || !Array.isArray(records) || records.length === 0) continue

        // Clear existing data from the table
        // @ts-ignore - Prisma client has dynamic properties
        await tx[tableName.toLowerCase()].deleteMany({})

        // Insert the backup records
        // We need to handle records in batches to avoid hitting limits
        const batchSize = 100
        const recordsArray = records as any[]

        for (let i = 0; i < recordsArray.length; i += batchSize) {
          const batch = recordsArray.slice(i, i + batchSize)

          // Use createMany for efficient bulk insert
          // @ts-ignore - Prisma client has dynamic properties
          await tx[tableName.toLowerCase()].createMany({
            data: batch,
            skipDuplicates: true,
          })
        }
      }

      // Log the restore operation
      await tx.backup.update({
        where: { id: backupId },
        data: {
          lastRestoredAt: new Date(),
        },
      })
    })

    return NextResponse.json({
      success: true,
      message: "Backup uploaded and restored successfully",
      backupId,
    })
  } catch (error) {
    console.error("Error in upload backup API:", error)
    return NextResponse.json({ error: "Failed to upload and restore backup: " + error.message }, { status: 500 })
  }
}

