"use server"

import { revalidatePath } from "next/cache"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { invalidateCachePattern } from "@/lib/cache"

export async function createProduct(formData: FormData) {
  try {
    const session = await auth()
    if (!session?.user) {
      return { error: "Unauthorized" }
    }

    // Process form data and create product
    const name = formData.get("name") as string
    const sku = formData.get("sku") as string
    const description = formData.get("description") as string
    const categoryId = formData.get("categoryId") as string
    const supplierId = formData.get("supplierId") as string
    const costPrice = Number.parseFloat(formData.get("costPrice") as string)
    const sellingPrice = Number.parseFloat(formData.get("sellingPrice") as string)
    const quantity = Number.parseInt(formData.get("quantity") as string)
    const reorderLevel = Number.parseInt(formData.get("reorderLevel") as string)
    const imageUrl = formData.get("imageUrl") as string

    const product = await db.product.create({
      data: {
        name,
        sku,
        description,
        categoryId,
        supplierId,
        costPrice,
        sellingPrice,
        quantity,
        reorderLevel,
        imageUrl,
        userId: session.user.id,
      },
    })

    // Invalidate relevant caches
    await invalidateCachePattern("products:*")
    await invalidateCachePattern("dashboard:*")

    // Revalidate paths
    revalidatePath("/products")
    revalidatePath("/dashboard")

    return { success: true, product }
  } catch (error) {
    console.error("Error creating product:", error)
    return { error: "Failed to create product" }
  }
}

// Similar pattern for update and delete actions

