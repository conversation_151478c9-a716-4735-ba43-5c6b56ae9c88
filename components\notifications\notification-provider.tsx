"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { useSocket } from "@/components/socket-provider"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"

export type NotificationType = "info" | "success" | "warning" | "error"

export interface Notification {
  id: string
  title: string
  message: string
  type: NotificationType
  read: boolean
  actionUrl?: string
  createdAt: string
  expiresAt?: string
}

interface NotificationContextType {
  notifications: Notification[]
  unreadCount: number
  isLoading: boolean
  markAsRead: (id: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  deleteNotification: (id: string) => Promise<void>
  deleteAllNotifications: () => Promise<void>
  refreshNotifications: () => Promise<void>
}

const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  markAsRead: async () => {},
  markAllAsRead: async () => {},
  deleteNotification: async () => {},
  deleteAllNotifications: async () => {},
  refreshNotifications: async () => {},
})

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession()
  const { socket, status } = useSocket()
  const { toast } = useToast()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Calculate unread count
  const unreadCount = notifications.filter((n) => !n.read).length

  // Fetch notifications on session change or socket reconnection
  useEffect(() => {
    if (session?.user) {
      refreshNotifications()
    }
  }, [session, status])

  // Listen for real-time notifications
  useEffect(() => {
    if (!socket || !session?.user) return

    const handleNewNotification = (data: any) => {
      // Only process notifications for the current user
      if (data.userId === session.user.id) {
        const newNotification: Notification = {
          id: data.id,
          title: data.title,
          message: data.message,
          type: data.type,
          read: false,
          actionUrl: data.actionUrl,
          createdAt: new Date().toISOString(),
          expiresAt: data.expiresAt,
        }

        // Add to notifications list
        setNotifications((prev) => [newNotification, ...prev])

        // Show toast for important notifications
        if (data.type === "warning" || data.type === "error") {
          toast({
            title: data.title,
            description: data.message,
            variant: data.type === "error" ? "destructive" : "default",
          })
        }
      }
    }

    // Subscribe to notification events
    socket.on("notification", handleNewNotification)

    return () => {
      socket.off("notification", handleNewNotification)
    }
  }, [socket, session, toast])

  // Fetch all notifications
  const refreshNotifications = async () => {
    if (!session?.user) return

    setIsLoading(true)
    try {
      const data = await fetchApi<Notification[]>("/api/notifications")
      setNotifications(data)
    } catch (error) {
      console.error("Failed to fetch notifications:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    try {
      await fetchApi(`/api/notifications/${id}/read`, { method: "POST" })
      setNotifications((prev) =>
        prev.map((notification) => (notification.id === id ? { ...notification, read: true } : notification)),
      )
    } catch (error) {
      console.error("Failed to mark notification as read:", error)
    }
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      await fetchApi("/api/notifications/read-all", { method: "POST" })
      setNotifications((prev) => prev.map((notification) => ({ ...notification, read: true })))
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error)
    }
  }

  // Delete a notification
  const deleteNotification = async (id: string) => {
    try {
      await fetchApi(`/api/notifications/${id}`, { method: "DELETE" })
      setNotifications((prev) => prev.filter((notification) => notification.id !== id))
    } catch (error) {
      console.error("Failed to delete notification:", error)
    }
  }

  // Delete all notifications
  const deleteAllNotifications = async () => {
    try {
      await fetchApi("/api/notifications/delete-all", { method: "DELETE" })
      setNotifications([])
    } catch (error) {
      console.error("Failed to delete all notifications:", error)
    }
  }

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        isLoading,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        deleteAllNotifications,
        refreshNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  )
}

export const useNotifications = () => useContext(NotificationContext)

