import { parse, unparse } from "papaparse"
import { z } from "zod"

export type CsvProcessorOptions<T> = {
  schema: z.ZodType<T>
  onProgress?: (progress: number) => void
  chunkSize?: number
}

export class CsvProcessor<T> {
  private schema: z.ZodType<T>
  private onProgress?: (progress: number) => void
  private chunkSize: number

  constructor(options: CsvProcessorOptions<T>) {
    this.schema = options.schema
    this.onProgress = options.onProgress
    this.chunkSize = options.chunkSize || 100
  }

  async parseFile(file: File): Promise<{
    data: T[]
    errors: { row: number; errors: z.ZodError }[]
    totalRows: number
  }> {
    return new Promise((resolve, reject) => {
      const results: T[] = []
      const errors: { row: number; errors: z.ZodError }[] = []
      let totalRows = 0
      let processedRows = 0

      parse(file, {
        header: true,
        skipEmptyLines: true,
        dynamicTyping: true,
        chunk: (chunk) => {
          totalRows += chunk.data.length
          const validatedChunk = this.validateChunk(chunk.data, processedRows)
          results.push(...validatedChunk.data)
          errors.push(...validatedChunk.errors)
          processedRows += chunk.data.length

          if (this.onProgress) {
            this.onProgress(processedRows / totalRows)
          }
        },
        complete: () => {
          resolve({
            data: results,
            errors,
            totalRows,
          })
        },
        error: (error) => {
          reject(error)
        },
      })
    })
  }

  private validateChunk(
    chunk: any[],
    startRow: number,
  ): {
    data: T[]
    errors: { row: number; errors: z.ZodError }[]
  } {
    const data: T[] = []
    const errors: { row: number; errors: z.ZodError }[] = []

    chunk.forEach((row, index) => {
      try {
        const validatedRow = this.schema.parse(row)
        data.push(validatedRow)
      } catch (error) {
        if (error instanceof z.ZodError) {
          errors.push({
            row: startRow + index,
            errors: error,
          })
        }
      }
    })

    return { data, errors }
  }

  static generateTemplate<T>(headers: Record<keyof T, string>): string {
    const csvData = [headers]
    return unparse(csvData)
  }

  static exportData<T>(data: T[], headers: Record<keyof T, string>): string {
    const csvData = data.map((item) => {
      const row: Record<string, any> = {}
      Object.entries(headers).forEach(([key, label]) => {
        row[label] = item[key as keyof T]
      })
      return row
    })

    return unparse([headers, ...csvData])
  }
}

