"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { BackupList } from "@/components/backup/backup-list"
import { BackupSchedule } from "@/components/backup/backup-schedule"
import { BackupUpload } from "@/components/backup/backup-upload"

export default function BackupPage() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Backup & Restore</h3>
        <p className="text-sm text-muted-foreground">Manage your database backups and restore options</p>
      </div>

      <Tabs defaultValue="backups" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="backups">Backups</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="restore">Restore</TabsTrigger>
        </TabsList>
        <TabsContent value="backups">
          <BackupList />
        </Ta<PERSON>Content>
        <TabsContent value="schedule">
          <BackupSchedule />
        </TabsContent>
        <TabsContent value="restore">
          <BackupUpload />
        </TabsContent>
      </Tabs>
    </div>
  )
}

