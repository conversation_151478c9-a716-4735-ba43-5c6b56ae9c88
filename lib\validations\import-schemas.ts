import { z } from "zod"

// Product import schema
export const productImportSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  sku: z.string().min(1, "SKU is required"),
  barcode: z.string().optional(),
  description: z.string().optional(),
  price: z.number().min(0, "Price must be a positive number"),
  cost: z.number().min(0, "Cost must be a positive number").optional(),
  taxRate: z.number().min(0, "Tax rate must be a positive number").optional(),
  stockQuantity: z.number().int().min(0, "Stock quantity must be a positive integer"),
  reorderPoint: z.number().int().min(0, "Reorder point must be a positive integer").optional(),
  categoryName: z.string().min(1, "Category name is required"),
  supplierName: z.string().optional(),
  taxable: z.boolean().optional(),
  userId: z.string().optional(),
})

// Customer import schema
export const customerImportSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  notes: z.string().optional(),
  userId: z.string().optional(),
})

// Supplier import schema
export const supplierImportSchema = z.object({
  name: z.string().min(1, "Supplier name is required"),
  contactName: z.string().optional(),
  email: z.string().email("Invalid email address").optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  notes: z.string().optional(),
  userId: z.string().optional(),
})

// Category import schema
export const categoryImportSchema = z.object({
  name: z.string().min(1, "Category name is required"),
  description: z.string().optional(),
  parentCategory: z.string().optional(),
  userId: z.string().optional(),
})

// Order import schema
export const orderImportSchema = z.object({
  orderNumber: z.string().min(1, "Order number is required"),
  customerEmail: z.string().email("Invalid customer email"),
  status: z.string().min(1, "Order status is required"),
  paymentStatus: z.string().min(1, "Payment status is required"),
  paymentMethod: z.string().optional(),
  subtotal: z.number().min(0, "Subtotal must be a positive number"),
  tax: z.number().min(0, "Tax must be a positive number"),
  shipping: z.number().min(0, "Shipping must be a positive number").optional(),
  discount: z.number().min(0, "Discount must be a positive number").optional(),
  total: z.number().min(0, "Total must be a positive number"),
  notes: z.string().optional(),
  shippingAddress: z.string().optional(),
  billingAddress: z.string().optional(),
  userId: z.string().optional(),
})

// Export headers for each entity type
export const productExportHeaders = {
  name: "Product Name",
  sku: "SKU",
  barcode: "Barcode",
  description: "Description",
  price: "Price",
  cost: "Cost",
  taxRate: "Tax Rate",
  stockQuantity: "Stock Quantity",
  reorderPoint: "Reorder Point",
  categoryName: "Category",
  supplierName: "Supplier",
}

export const customerExportHeaders = {
  firstName: "First Name",
  lastName: "Last Name",
  email: "Email",
  phone: "Phone",
  address: "Address",
  city: "City",
  state: "State",
  postalCode: "Postal Code",
  country: "Country",
  notes: "Notes",
}

export const supplierExportHeaders = {
  name: "Supplier Name",
  contactName: "Contact Name",
  email: "Email",
  phone: "Phone",
  address: "Address",
  notes: "Notes",
}

export const categoryExportHeaders = {
  name: "Category Name",
  description: "Description",
  parentCategory: "Parent Category",
}

export const orderExportHeaders = {
  orderNumber: "Order Number",
  customerEmail: "Customer Email",
  status: "Status",
  paymentStatus: "Payment Status",
  paymentMethod: "Payment Method",
  subtotal: "Subtotal",
  tax: "Tax",
  shipping: "Shipping",
  discount: "Discount",
  total: "Total",
  notes: "Notes",
}

