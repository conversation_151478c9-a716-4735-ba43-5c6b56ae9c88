import { Server } from "socket.io"
import { prisma } from "./lib/prisma"

export function createSocketServer(server: any) {
  const io = new Server(server, {
    cors: {
      origin:
        process.env.NODE_ENV === "production"
          ? process.env.NEXTAUTH_URL || "https://stocksync.vercel.app"
          : "http://localhost:3000",
      methods: ["GET", "POST"],
      credentials: true,
    },
  })

  // Store connected clients
  const connectedClients = new Map()

  // Middleware for authentication
  io.use(async (socket, next) => {
    const token = socket.handshake.auth.token

    // In a real app, verify the token against your auth system
    // For now, we'll just check if it exists
    if (!token) {
      return next(new Error("Authentication error"))
    }

    // Store user info in socket
    socket.data.userId = "user_" + Math.floor(Math.random() * 1000)
    socket.data.deviceId = socket.handshake.auth.deviceId || socket.id

    next()
  })

  io.on("connection", (socket) => {
    console.log(`Client connected: ${socket.id}, Device ID: ${socket.data.deviceId}`)

    // Store client connection
    connectedClients.set(socket.data.deviceId, {
      socketId: socket.id,
      userId: socket.data.userId,
      lastSeen: new Date(),
    })

    // Handle inventory updates
    socket.on("inventory_update", async (data) => {
      try {
        console.log("Inventory update received:", data)

        // Update the database
        const product = await prisma.product.findUnique({
          where: { id: data.data.id },
        })

        if (product) {
          let newQuantity = product.stockQuantity

          if (data.data.action === "set") {
            newQuantity = data.data.stockQuantity
          } else if (data.data.action === "increment") {
            newQuantity += data.data.stockQuantity
          } else if (data.data.action === "decrement") {
            newQuantity -= Math.abs(data.data.stockQuantity)
          }

          // Ensure stock doesn't go below zero
          if (newQuantity < 0) newQuantity = 0

          // Update the product
          await prisma.product.update({
            where: { id: data.data.id },
            data: { stockQuantity: newQuantity },
          })

          // Create inventory log
          await prisma.inventoryLog.create({
            data: {
              productId: data.data.id,
              userId: socket.data.userId,
              quantity:
                data.data.action === "set"
                  ? data.data.stockQuantity - product.stockQuantity
                  : data.data.action === "increment"
                    ? data.data.stockQuantity
                    : -Math.abs(data.data.stockQuantity),
              type: "ADJUSTMENT",
              reason: data.data.reason || "WebSocket update",
            },
          })

          // Broadcast the update to all clients except sender
          socket.broadcast.emit("inventory_update", {
            id: data.id,
            timestamp: Date.now(),
            data: {
              id: data.data.id,
              name: data.data.name,
              stockQuantity: newQuantity,
              action: "set",
            },
          })

          // Check for low stock
          if (newQuantity <= product.reorderPoint) {
            io.emit("low_stock_alert", {
              id: `low_stock_${data.data.id}_${Date.now()}`,
              timestamp: Date.now(),
              data: {
                productId: data.data.id,
                productName: data.data.name,
                currentStock: newQuantity,
                reorderPoint: product.reorderPoint,
              },
            })
          }
        }
      } catch (error) {
        console.error("Error processing inventory update:", error)
      }
    })

    // Handle order creation
    socket.on("order_created", async (data) => {
      try {
        console.log("Order created:", data)

        // In a real app, you would save the order to the database here
        // For now, we'll just broadcast it to all clients
        socket.broadcast.emit("order_created", {
          id: data.id,
          timestamp: Date.now(),
          data: data.data,
        })
      } catch (error) {
        console.error("Error processing order creation:", error)
      }
    })

    // Handle order updates
    socket.on("order_updated", (data) => {
      console.log("Order updated:", data)

      // Broadcast to all clients except sender
      socket.broadcast.emit("order_updated", {
        id: data.id,
        timestamp: Date.now(),
        data: data.data,
      })
    })

    // Handle order completion
    socket.on("order_completed", (data) => {
      console.log("Order completed:", data)

      // Broadcast to all clients
      io.emit("order_completed", {
        id: data.id,
        timestamp: Date.now(),
        data: data.data,
      })
    })

    // Handle sync requests
    socket.on("sync_request", async (data) => {
      try {
        console.log("Sync request received:", data)

        let responseData = {}

        // Handle different sync types
        if (data.type === "inventory") {
          const products = await prisma.product.findMany({
            include: {
              category: true,
            },
            take: data.limit || 100,
          })

          responseData = {
            products,
          }
        } else if (data.type === "orders") {
          const orders = await prisma.order.findMany({
            include: {
              customer: true,
              items: {
                include: {
                  product: true,
                },
              },
            },
            take: data.limit || 50,
            orderBy: {
              createdAt: "desc",
            },
          })

          responseData = {
            orders,
          }
        } else if (data.type === "customers") {
          const customers = await prisma.customer.findMany({
            take: data.limit || 50,
          })

          responseData = {
            customers,
          }
        } else if (data.type === "analytics") {
          // In a real app, you would fetch this data from the database
          // For now, we'll return mock data
          responseData = {
            totalSales: 12589.99,
            totalOrders: 156,
            totalProducts: 48,
            totalCustomers: 89,
            lowStockItems: 5,
            averageOrderValue: 80.71,
          }
        } else if (data.type === "sales_chart") {
          // In a real app, you would fetch this data from the database
          // For now, we'll return mock data
          responseData = {
            daily: [
              { date: "Mon", sales: 420.65 },
              { date: "Tue", sales: 380.12 },
              { date: "Wed", sales: 450.3 },
              { date: "Thu", sales: 520.8 },
              { date: "Fri", sales: 580.25 },
              { date: "Sat", sales: 620.5 },
              { date: "Sun", sales: 350.9 },
            ],
            weekly: [
              { date: "Week 1", sales: 2800.65 },
              { date: "Week 2", sales: 3200.12 },
              { date: "Week 3", sales: 2950.3 },
              { date: "Week 4", sales: 3400.8 },
            ],
            monthly: [
              { date: "Jan", sales: 12500.65 },
              { date: "Feb", sales: 11800.12 },
              { date: "Mar", sales: 13200.3 },
              { date: "Apr", sales: 12800.8 },
              { date: "May", sales: 14500.25 },
              { date: "Jun", sales: 13900.5 },
            ],
          }
        } else if (data.type === "recent_orders") {
          const orders = await prisma.order.findMany({
            include: {
              customer: true,
            },
            take: data.limit || 5,
            orderBy: {
              createdAt: "desc",
            },
          })

          responseData = {
            orders,
          }
        } else if (data.type === "low_stock") {
          const items = await prisma.product.findMany({
            where: {
              stockQuantity: {
                lte: prisma.product.fields.reorderPoint,
              },
            },
            include: {
              category: true,
            },
            orderBy: {
              stockQuantity: "asc",
            },
          })

          responseData = {
            items,
          }
        }

        // Send response back to the requesting client
        socket.emit("sync_response", {
          id: data.id,
          timestamp: Date.now(),
          type: data.type,
          data: responseData,
        })
      } catch (error) {
        console.error("Error processing sync request:", error)

        // Send error response
        socket.emit("sync_response", {
          id: data.id,
          timestamp: Date.now(),
          type: data.type,
          error: "Failed to process sync request",
        })
      }
    })

    // Handle disconnection
    socket.on("disconnect", () => {
      console.log(`Client disconnected: ${socket.id}`)

      // Remove client from connected clients
      connectedClients.delete(socket.data.deviceId)
    })
  })

  return io
}

