import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { ApiError } from "@/lib/api-error"
import { CsvProcessor } from "@/lib/utils/csv-processor"
import { productImportSchema } from "@/lib/validations/product-import-schema"

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to import products
    if (session.user.role !== "ADMIN" && session.user.role !== "MANAGER") {
      throw ApiError.forbidden("You do not have permission to import products")
    }

    const formData = await req.formData()
    const file = formData.get("file") as File

    if (!file) {
      throw ApiError.badRequest("No file provided")
    }

    // Validate file type
    if (!file.name.endsWith(".csv")) {
      throw ApiError.badRequest("Only CSV files are supported")
    }

    // Create a batch operation record
    const batchOperation = await prisma.batchOperation.create({
      data: {
        type: "import",
        status: "pending",
        fileName: file.name,
        userId: session.user.id,
        metadata: {
          fileSize: file.size,
          contentType: file.type,
        },
      },
    })

    // Process the file in the background
    // In a real implementation, this would be handled by a background job
    // For simplicity, we're doing it in the request handler
    processImportFile(file, batchOperation.id, session.user.id)

    return NextResponse.json({
      success: true,
      batchOperationId: batchOperation.id,
    })
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Batch import error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

async function processImportFile(file: File, batchOperationId: string, userId: string) {
  try {
    // Update batch operation status
    await prisma.batchOperation.update({
      where: { id: batchOperationId },
      data: { status: "processing" },
    })

    const csvProcessor = new CsvProcessor({
      schema: productImportSchema,
      onProgress: async (progress) => {
        // Update progress
        await prisma.batchOperation.update({
          where: { id: batchOperationId },
          data: {
            processedItems: Math.floor(progress * 100),
          },
        })
      },
    })

    const result = await csvProcessor.parseFile(file)

    // Process valid products
    let successCount = 0
    let errorCount = 0

    for (const productData of result.data) {
      try {
        // Find or create category
        const category = await prisma.category.upsert({
          where: { name: productData.categoryName },
          update: {},
          create: {
            name: productData.categoryName,
            slug: productData.categoryName.toLowerCase().replace(/\s+/g, "-"),
          },
        })

        // Find or create supplier if provided
        let supplierId = null
        if (productData.supplierName) {
          const supplier = await prisma.supplier.upsert({
            where: { name: productData.supplierName },
            update: {},
            create: {
              name: productData.supplierName,
            },
          })
          supplierId = supplier.id
        }

        // Find store ID (using first available store for simplicity)
        const store = await prisma.store.findFirst()
        if (!store) {
          throw new Error("No store found")
        }

        // Create or update product
        await prisma.product.upsert({
          where: { sku: productData.sku },
          update: {
            name: productData.name,
            barcode: productData.barcode || null,
            description: productData.description || null,
            price: productData.price,
            cost: productData.cost || null,
            taxRate: productData.taxRate || 0,
            stockQuantity: productData.stockQuantity,
            reorderPoint: productData.reorderPoint || 10,
            categoryId: category.id,
            supplierId: supplierId,
          },
          create: {
            name: productData.name,
            sku: productData.sku,
            barcode: productData.barcode || null,
            description: productData.description || null,
            price: productData.price,
            cost: productData.cost || null,
            taxRate: productData.taxRate || 0,
            stockQuantity: productData.stockQuantity,
            reorderPoint: productData.reorderPoint || 10,
            categoryId: category.id,
            supplierId: supplierId,
            storeId: store.id,
          },
        })

        successCount++
      } catch (error) {
        console.error("Error processing product:", error)
        errorCount++
      }
    }

    // Update batch operation with results
    await prisma.batchOperation.update({
      where: { id: batchOperationId },
      data: {
        status: "completed",
        totalItems: result.totalRows,
        processedItems: result.totalRows,
        successItems: successCount,
        errorItems: errorCount + result.errors.length,
        errors: result.errors.length > 0 ? result.errors : null,
        completedAt: new Date(),
      },
    })

    // Create notification for the user
    await prisma.notification.create({
      data: {
        userId: userId,
        title: "Product Import Completed",
        message: `Imported ${successCount} products successfully. ${errorCount + result.errors.length} errors.`,
        type: errorCount > 0 ? "warning" : "success",
        actionUrl: `/dashboard/products/batch/${batchOperationId}`,
      },
    })
  } catch (error) {
    console.error("Error processing import file:", error)

    // Update batch operation with error
    await prisma.batchOperation.update({
      where: { id: batchOperationId },
      data: {
        status: "failed",
        errors: [{ message: error instanceof Error ? error.message : "Unknown error" }],
        completedAt: new Date(),
      },
    })

    // Create notification for the user
    await prisma.notification.create({
      data: {
        userId: userId,
        title: "Product Import Failed",
        message: "There was an error processing your product import.",
        type: "error",
        actionUrl: `/dashboard/products/batch/${batchOperationId}`,
      },
    })
  }
}

