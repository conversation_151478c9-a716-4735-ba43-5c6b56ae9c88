"use client"

import { useEffect, useState } from "react"
import { useStore } from "@/lib/store"

interface StoreProviderProps {
  children: React.ReactNode
}

export function StoreProvider({ children }: StoreProviderProps) {
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // Manually trigger hydration on the client side
    useStore.persist.rehydrate()
    setIsHydrated(true)
  }, [])

  return (
    <div suppressHydrationWarning>
      {children}
    </div>
  )
}
