import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// Define cacheable paths and their max-age in seconds
const CACHE_RULES = [
  { path: "/api/products", maxAge: 60 }, // 1 minute
  { path: "/api/categories", maxAge: 300 }, // 5 minutes
  { path: "/api/suppliers", maxAge: 300 }, // 5 minutes
  { path: "/api/dashboard/stats", maxAge: 60 }, // 1 minute
  { path: "/api/reports", maxAge: 300 }, // 5 minutes
  // Static assets get longer cache times
  { path: "/_next/image", maxAge: 86400 }, // 1 day
  { path: "/images", maxAge: 86400 }, // 1 day
]

export function middleware(request: NextRequest) {
  const response = NextResponse.next()

  // Skip for authenticated API routes that shouldn't be cached by CDN/browsers
  if (request.cookies.has("session-token") && request.nextUrl.pathname.startsWith("/api/")) {
    response.headers.set("Cache-Control", "no-store, max-age=0")
    return response
  }

  // Apply cache rules for public assets and API responses
  for (const rule of CACHE_RULES) {
    if (request.nextUrl.pathname.startsWith(rule.path)) {
      // Set appropriate cache headers
      response.headers.set(
        "Cache-Control",
        `public, max-age=${rule.maxAge}, s-maxage=${rule.maxAge * 2}, stale-while-revalidate=${rule.maxAge * 5}`,
      )
      return response
    }
  }

  return response
}

export const config = {
  matcher: ["/api/:path*", "/_next/image:path*", "/images/:path*"],
}

