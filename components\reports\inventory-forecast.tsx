"use client"

import { useState, useEffect } from "react"
import { useAnalyticsWorker } from "@/hooks/use-worker"
import { useOptimizedQuery } from "@/hooks/use-optimized-query"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"

export function InventoryForecast() {
  const [forecastData, setForecastData] = useState<any[]>([])

  // Fetch products data
  const { data: products, isLoading: isLoadingProducts } = useOptimizedQuery({
    queryKey: ["products"],
    queryFn: async () => {
      const res = await fetch("/api/products?limit=1000")
      const data = await res.json()
      return data.products
    },
  })

  // Fetch sales history
  const { data: salesHistory, isLoading: isLoadingSales } = useOptimizedQuery({
    queryKey: ["sales-history"],
    queryFn: async () => {
      const res = await fetch("/api/reports/sales-history")
      const data = await res.json()
      return data.salesHistory
    },
  })

  // Initialize worker
  const {
    runTask,
    isLoading: isProcessing,
    result,
  } = useAnalyticsWorker({
    onMessage: (result) => {
      setForecastData(result)
    },
  })

  // Run forecast when data is available
  useEffect(() => {
    if (products && salesHistory && !isProcessing) {
      runTask("forecastInventory", { products, salesHistory })
    }
  }, [products, salesHistory, runTask, isProcessing])

  const isLoading = isLoadingProducts || isLoadingSales || isProcessing

  return (
    <Card>
      <CardHeader>
        <CardTitle>Inventory Forecast</CardTitle>
        <CardDescription>Projected inventory levels and reorder recommendations</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead className="text-right">Current Stock</TableHead>
                <TableHead className="text-right">Avg. Daily Sales</TableHead>
                <TableHead className="text-right">Days Until Stockout</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {forecastData?.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.name}</TableCell>
                  <TableCell className="text-right">{item.currentStock}</TableCell>
                  <TableCell className="text-right">{item.avgDailySales.toFixed(2)}</TableCell>
                  <TableCell className="text-right">{item.daysUntilStockout}</TableCell>
                  <TableCell>
                    <Badge variant={item.reorderNeeded ? "destructive" : "success"}>
                      {item.reorderNeeded ? "Reorder Needed" : "Adequate Stock"}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

