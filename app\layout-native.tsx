"use client"

import type React from "react"

import { useEffect } from "react"
import { AnalyticsProvider } from "@/components/analytics/analytics-provider"
import { PWASetup } from "@/app/layout-pwa"
import { isNative, NativeStatusBar } from "@/lib/native/native-features"

export function NativeFeatures({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Set up native features when running in a native app
    if (isNative()) {
      // Set status bar color
      NativeStatusBar.setColor("#ffffff", true)

      // Add native app specific styles
      document.documentElement.classList.add("native-app")
    }
  }, [])

  return (
    <AnalyticsProvider>
      <PWASetup />
      {children}
    </AnalyticsProvider>
  )
}

