import { io, type Socket } from "socket.io-client"
import { create } from "zustand"

// Define the types of events we'll handle
export type SocketEvent =
  | "inventory_update"
  | "order_created"
  | "order_updated"
  | "order_completed"
  | "low_stock_alert"
  | "payment_received"
  | "user_activity"
  | "sync_request"
  | "sync_response"

// Define the payload structure for different event types
export interface SocketPayload {
  id: string
  timestamp: number
  data: any
  source?: string
}

// Socket connection state
export type ConnectionStatus = "connected" | "disconnected" | "connecting" | "reconnecting"

// Socket store interface
interface SocketStore {
  socket: Socket | null
  status: ConnectionStatus
  isInitialized: boolean
  lastActivity: number
  pendingEvents: Array<{ event: SocketEvent; payload: SocketPayload }>

  // Methods
  initialize: () => void
  disconnect: () => void
  reconnect: () => void
  emit: (event: SocketEvent, payload: SocketPayload) => void
  addPendingEvent: (event: SocketEvent, payload: SocketPayload) => void
  processPendingEvents: () => void
}

// Create the socket store
export const useSocketStore = create<SocketStore>((set, get) => ({
  socket: null,
  status: "disconnected",
  isInitialized: false,
  lastActivity: Date.now(),
  pendingEvents: [],

  initialize: () => {
    // Don't initialize if already initialized
    if (get().isInitialized) return

    const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin

    try {
      const socket = io(socketUrl, {
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 10000,
        autoConnect: true,
        transports: ["websocket", "polling"],
      })

      // Set up event listeners
      socket.on("connect", () => {
        set({
          status: "connected",
          lastActivity: Date.now(),
        })

        // Process any pending events once connected
        get().processPendingEvents()
      })

      socket.on("disconnect", () => {
        set({ status: "disconnected" })
      })

      socket.on("reconnect_attempt", () => {
        set({ status: "reconnecting" })
      })

      socket.on("error", (error) => {
        console.error("Socket error:", error)
      })

      set({
        socket,
        isInitialized: true,
        status: "connecting",
      })
    } catch (error) {
      console.error("Failed to initialize socket:", error)
    }
  },

  disconnect: () => {
    const { socket } = get()
    if (socket) {
      socket.disconnect()
      set({
        socket: null,
        status: "disconnected",
        isInitialized: false,
      })
    }
  },

  reconnect: () => {
    const { socket } = get()
    if (socket) {
      socket.connect()
      set({ status: "connecting" })
    } else {
      // If socket doesn't exist, initialize a new one
      get().initialize()
    }
  },

  emit: (event: SocketEvent, payload: SocketPayload) => {
    const { socket, status, addPendingEvent } = get()

    // Add device identifier to payload
    const enhancedPayload = {
      ...payload,
      source: socket?.id || "unknown",
      timestamp: Date.now(),
    }

    // If connected, emit the event
    if (socket && status === "connected") {
      socket.emit(event, enhancedPayload)
      set({ lastActivity: Date.now() })
    } else {
      // Otherwise, add to pending events
      addPendingEvent(event, enhancedPayload)
    }
  },

  addPendingEvent: (event: SocketEvent, payload: SocketPayload) => {
    set((state) => ({
      pendingEvents: [...state.pendingEvents, { event, payload }],
    }))
  },

  processPendingEvents: () => {
    const { socket, pendingEvents } = get()

    if (socket && socket.connected && pendingEvents.length > 0) {
      // Process all pending events
      pendingEvents.forEach(({ event, payload }) => {
        socket.emit(event, payload)
      })

      // Clear pending events
      set({
        pendingEvents: [],
        lastActivity: Date.now(),
      })
    }
  },
}))

