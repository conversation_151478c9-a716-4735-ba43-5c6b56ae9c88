import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const settings = await prisma.loyaltySettings.findUnique({
      where: {
        userId: session.user.id,
      },
      include: {
        tiers: true,
      },
    })

    if (!settings) {
      // Create default settings if none exist
      const defaultSettings = await prisma.loyaltySettings.create({
        data: {
          enabled: false,
          pointsPerDollar: 1,
          minimumPointsToRedeem: 100,
          pointsValueInCents: 1,
          userId: session.user.id,
          tiers: {
            create: [
              {
                name: "Bronze",
                minimumPoints: 0,
                pointsMultiplier: 1,
                benefits: "Standard earning rate",
              },
              {
                name: "<PERSON>",
                minimumPoints: 1000,
                pointsMultiplier: 1.25,
                benefits: "25% bonus on points earned",
              },
              {
                name: "Gold",
                minimumPoints: 5000,
                pointsMultiplier: 1.5,
                benefits: "50% bonus on points earned, Free shipping",
              },
            ],
          },
        },
        include: {
          tiers: true,
        },
      })

      return NextResponse.json(defaultSettings)
    }

    return NextResponse.json(settings)
  } catch (error) {
    console.error("[LOYALTY_SETTINGS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await req.json()
    const { enabled, pointsPerDollar, minimumPointsToRedeem, pointsValueInCents, expiryMonths, tiers } = body

    const settings = await prisma.loyaltySettings.upsert({
      where: {
        userId: session.user.id,
      },
      update: {
        enabled,
        pointsPerDollar,
        minimumPointsToRedeem,
        pointsValueInCents,
        expiryMonths,
      },
      create: {
        enabled,
        pointsPerDollar,
        minimumPointsToRedeem,
        pointsValueInCents,
        expiryMonths,
        userId: session.user.id,
      },
    })

    // Update tiers if provided
    if (tiers && Array.isArray(tiers)) {
      // Delete existing tiers
      await prisma.loyaltyTier.deleteMany({
        where: {
          loyaltySettingsId: settings.id,
        },
      })

      // Create new tiers
      for (const tier of tiers) {
        await prisma.loyaltyTier.create({
          data: {
            name: tier.name,
            minimumPoints: tier.minimumPoints,
            pointsMultiplier: tier.pointsMultiplier,
            benefits: tier.benefits,
            loyaltySettingsId: settings.id,
          },
        })
      }
    }

    const updatedSettings = await prisma.loyaltySettings.findUnique({
      where: {
        id: settings.id,
      },
      include: {
        tiers: true,
      },
    })

    return NextResponse.json(updatedSettings)
  } catch (error) {
    console.error("[LOYALTY_SETTINGS_PUT]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

