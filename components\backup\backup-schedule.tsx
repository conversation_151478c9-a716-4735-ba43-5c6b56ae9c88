"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

type BackupScheduleSettings = {
  enabled: boolean
  frequency: "daily" | "weekly" | "monthly"
  time: string
  retentionCount: number
}

export function BackupSchedule() {
  const { toast } = useToast()
  const [settings, setSettings] = useState<BackupScheduleSettings>({
    enabled: false,
    frequency: "daily",
    time: "00:00",
    retentionCount: 7,
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Fetch backup schedule settings
  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/backups/schedule")
      const data = await response.json()

      if (data.success) {
        setSettings(data.settings)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch backup schedule settings",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch backup schedule settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Save backup schedule settings
  const saveSettings = async () => {
    try {
      setSaving(true)
      const response = await fetch("/api/backups/schedule", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: data.message || "Backup schedule updated successfully",
        })
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update backup schedule",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update backup schedule",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  // Handle form changes
  const handleChange = (field: keyof BackupScheduleSettings, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // Load settings on component mount
  useEffect(() => {
    fetchSettings()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Backup Schedule</CardTitle>
          <CardDescription>Configure automatic backup schedule</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Backup Schedule</CardTitle>
        <CardDescription>Configure automatic backup schedule</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="auto-backup">Automatic Backups</Label>
            <p className="text-sm text-muted-foreground">Enable or disable automatic backups</p>
          </div>
          <Switch
            id="auto-backup"
            checked={settings.enabled}
            onCheckedChange={(checked) => handleChange("enabled", checked)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="backup-frequency">Backup Frequency</Label>
          <Select
            value={settings.frequency}
            onValueChange={(value) => handleChange("frequency", value)}
            disabled={!settings.enabled}
          >
            <SelectTrigger id="backup-frequency">
              <SelectValue placeholder="Select frequency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">How often backups should be created</p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="backup-time">Backup Time</Label>
          <Input
            id="backup-time"
            type="time"
            value={settings.time}
            onChange={(e) => handleChange("time", e.target.value)}
            disabled={!settings.enabled}
          />
          <p className="text-sm text-muted-foreground">The time of day when backups should be created</p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="retention-count">Retention Policy</Label>
          <Input
            id="retention-count"
            type="number"
            min="1"
            max="100"
            value={settings.retentionCount}
            onChange={(e) => handleChange("retentionCount", Number.parseInt(e.target.value))}
            disabled={!settings.enabled}
          />
          <p className="text-sm text-muted-foreground">Number of backups to keep before deleting old ones</p>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={saveSettings} disabled={saving}>
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Settings"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

