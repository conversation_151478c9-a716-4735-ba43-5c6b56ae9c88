import { z } from "zod"

export const productImportSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  sku: z.string().min(1, "SKU is required"),
  barcode: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  price: z.coerce.number().min(0, "Price must be a positive number"),
  cost: z.coerce.number().min(0, "Cost must be a positive number").optional().nullable(),
  taxRate: z.coerce.number().min(0, "Tax rate must be a positive number").optional().nullable(),
  stockQuantity: z.coerce
    .number()
    .int("Stock quantity must be an integer")
    .min(0, "Stock quantity must be a positive number"),
  reorderPoint: z.coerce
    .number()
    .int("Reorder point must be an integer")
    .min(0, "Reorder point must be a positive number")
    .optional()
    .nullable(),
  categoryName: z.string().min(1, "Category is required"),
  supplierName: z.string().optional().nullable(),
})

export type ProductImportData = z.infer<typeof productImportSchema>

export const productExportHeaders: Record<keyof ProductImportData, string> = {
  name: "Product Name",
  sku: "SKU",
  barcode: "Barcode",
  description: "Description",
  price: "Price",
  cost: "Cost",
  taxRate: "Tax Rate",
  stockQuantity: "Stock Quantity",
  reorderPoint: "Reorder Point",
  categoryName: "Category",
  supplierName: "Supplier",
}

