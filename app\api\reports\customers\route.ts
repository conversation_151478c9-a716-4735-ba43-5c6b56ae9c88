import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { parseISO, startOfDay, endOfDay, format } from "date-fns"

export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const fromDate = searchParams.get("from")
    const toDate = searchParams.get("to")
    const store = searchParams.get("store")
    const groupBy = searchParams.get("groupBy") || "customer"

    if (!fromDate || !toDate) {
      return NextResponse.json({ error: "Missing date range parameters" }, { status: 400 })
    }

    // Parse dates
    const from = startOfDay(parseISO(fromDate))
    const to = endOfDay(parseISO(toDate))

    // Build query filters for orders
    const orderFilters: any = {
      createdAt: {
        gte: from,
        lte: to,
      },
    }

    if (store) {
      orderFilters.storeId = store
    }

    // Fetch customers with their orders
    const customers = await prisma.customer.findMany({
      include: {
        orders: {
          where: orderFilters,
          include: {
            items: true,
          },
        },
      },
    })

    // Calculate customer metrics
    const totalCustomers = customers.length
    const activeCustomers = customers.filter((c) => c.orders.length > 0).length
    const newCustomers = customers.filter((c) => c.createdAt >= from && c.createdAt <= to).length

    // Calculate total spend and average spend
    const totalSpend = customers.reduce(
      (sum, c) => sum + c.orders.reduce((orderSum, o) => orderSum + Number(o.total), 0),
      0,
    )
    const averageSpend = activeCustomers > 0 ? totalSpend / activeCustomers : 0

    // Calculate repeat purchase rate
    const customersWithMultipleOrders = customers.filter((c) => c.orders.length > 1).length
    const repeatPurchaseRate = activeCustomers > 0 ? (customersWithMultipleOrders / activeCustomers) * 100 : 0

    // Calculate customer retention (simplified)
    // In a real app, you'd compare to previous periods
    const customerRetention = 85 // Placeholder value

    // Generate chart data based on groupBy parameter
    let chartData: any[] = []

    if (groupBy === "customer") {
      // Top customers by spend
      chartData = customers
        .filter((c) => c.orders.length > 0)
        .map((c) => ({
          name: c.name,
          orders: c.orders.length,
          spend: c.orders.reduce((sum, o) => sum + Number(o.total), 0),
          items: c.orders.reduce((sum, o) => sum + o.items.reduce((itemSum, i) => itemSum + i.quantity, 0), 0),
        }))
        .sort((a, b) => b.spend - a.spend)
        .slice(0, 10) // Top 10 customers
    } else {
      // Time series data
      const dailyData = new Map()

      // Initialize with all days in the range
      const currentDate = new Date(from)
      while (currentDate <= to) {
        const day = format(currentDate, "yyyy-MM-dd")
        dailyData.set(day, {
          date: day,
          newCustomers: 0,
          activeCustomers: 0,
          orders: 0,
          spend: 0,
        })
        currentDate.setDate(currentDate.getDate() + 1)
      }

      // Fill in customer data
      customers.forEach((customer) => {
        if (customer.createdAt >= from && customer.createdAt <= to) {
          const day = format(customer.createdAt, "yyyy-MM-dd")
          const existing = dailyData.get(day)
          if (existing) {
            existing.newCustomers += 1
          }
        }

        customer.orders.forEach((order) => {
          const day = format(order.createdAt, "yyyy-MM-dd")
          const existing = dailyData.get(day)
          if (existing) {
            existing.activeCustomers += 1
            existing.orders += 1
            existing.spend += Number(order.total)
          }
        })
      })

      chartData = Array.from(dailyData.values())
    }

    // Generate pie data
    const pieData = [
      { name: "New Customers", value: newCustomers },
      { name: "Returning Customers", value: activeCustomers - newCustomers },
    ]

    // Generate table data
    const tableData = customers
      .filter((c) => c.orders.length > 0)
      .map((c) => ({
        id: c.id,
        name: c.name,
        email: c.email || "N/A",
        phone: c.phone || "N/A",
        orders: c.orders.length,
        totalSpend: c.orders.reduce((sum, o) => sum + Number(o.total), 0),
        averageOrderValue:
          c.orders.length > 0 ? c.orders.reduce((sum, o) => sum + Number(o.total), 0) / c.orders.length : 0,
        lastOrderDate:
          c.orders.length > 0
            ? c.orders.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0].createdAt.toISOString()
            : null,
      }))
      .sort((a, b) => b.totalSpend - a.totalSpend)

    // Calculate previous period for comparison
    // This is simplified - in a real app you'd need to fetch historical data
    const averageSpendChange = 5.2 // Placeholder
    const retentionChange = 2.1 // Placeholder

    return NextResponse.json({
      metrics: {
        totalCustomers,
        activeCustomers,
        newCustomers,
        averageSpend,
        averageSpendChange,
        repeatPurchaseRate,
        customerRetention,
        retentionChange,
      },
      chartData,
      timeSeriesData: groupBy !== "customer" ? chartData : undefined,
      pieData,
      tableData,
    })
  } catch (error) {
    console.error("Error generating customer report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

