"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Smartphone, Bell, Wifi, Mic, CuboidIcon as Cube } from "lucide-react"
import NotificationPreferences from "@/components/notifications/notification-preferences"
import OfflineStatus from "@/components/offline/offline-status"
import VoiceCommandListener from "@/components/voice/voice-command-listener"
import ARInventoryScanner from "@/components/ar/ar-inventory-scanner"
import { useToast } from "@/hooks/use-toast"
import { useNativeFeatures } from "@/hooks/use-native-features"

export default function MobileFeaturesPage() {
  const [scannedBarcode, setScannedBarcode] = useState<string | null>(null)
  const { toast } = useToast()
  const { isNative } = useNativeFeatures()

  const handleBarcodeScan = (barcode: string) => {
    setScannedBarcode(barcode)
    toast({
      title: "Barcode Scanned",
      description: `Scanned barcode: ${barcode}`,
      variant: "default",
    })
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Mobile Features</h1>
        <div className="flex items-center space-x-2">
          <Smartphone className="h-5 w-5 text-muted-foreground" />
          {isNative ? (
            <span className="text-sm text-green-600">Native App</span>
          ) : (
            <span className="text-sm text-muted-foreground">Web App</span>
          )}
        </div>
      </div>

      <Tabs defaultValue="notifications">
        <TabsList className="grid grid-cols-4 md:w-[600px]">
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="offline">
            <Wifi className="h-4 w-4 mr-2" />
            Offline Mode
          </TabsTrigger>
          <TabsTrigger value="voice">
            <Mic className="h-4 w-4 mr-2" />
            Voice Commands
          </TabsTrigger>
          <TabsTrigger value="ar">
            <Cube className="h-4 w-4 mr-2" />
            AR Features
          </TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-4 mt-4">
          <NotificationPreferences />
        </TabsContent>

        <TabsContent value="offline" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Offline Mode</CardTitle>
              <CardDescription>StockSync works offline and syncs when you're back online</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Current Status</span>
                <OfflineStatus />
              </div>

              <p className="text-sm text-muted-foreground">
                When you're offline, StockSync will store your changes locally and sync them when you're back online.
                Try turning off your internet connection and making changes to see it in action.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="voice" className="space-y-4 mt-4">
          <VoiceCommandListener />
        </TabsContent>

        <TabsContent value="ar" className="space-y-4 mt-4">
          <ARInventoryScanner onScan={handleBarcodeScan} />

          {scannedBarcode && (
            <Card>
              <CardHeader>
                <CardTitle>Scanned Product</CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  Barcode: <strong>{scannedBarcode}</strong>
                </p>
                <Button className="mt-4" onClick={() => (window.location.href = `/products?barcode=${scannedBarcode}`)}>
                  View Product
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

