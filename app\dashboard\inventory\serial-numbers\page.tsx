import { Suspense } from "react"
import { prisma } from "@/lib/prisma"
import { Skeleton } from "@/components/ui/skeleton"
import { SerialNumbersList } from "@/components/inventory/serial-numbers-list"

export default async function SerialNumbersPage() {
  // Get products that track serial numbers
  const products = await prisma.product.findMany({
    where: {
      trackSerialNumbers: true,
    },
    select: {
      id: true,
      name: true,
      sku: true,
      _count: {
        select: {
          serialNumbers: true,
        },
      },
    },
    orderBy: {
      name: "asc",
    },
  })

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Serial Numbers</h2>
        <p className="text-muted-foreground">Manage and track serialized inventory items</p>
      </div>

      <Suspense fallback={<Skeleton className="h-[600px] w-full" />}>
        <SerialNumbersList products={products} />
      </Suspense>
    </div>
  )
}

