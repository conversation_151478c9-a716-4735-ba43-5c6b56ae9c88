import { LanguageSelector } from "@/components/language-selector"
import { SyncIndicator } from "@/components/sync-indicator"
import { NotificationBell } from "@/components/notification-bell"
import { ConnectionStatus } from "@/components/connection-status"
import { UserMenu } from "@/components/user-menu"
import Link from "next/link"
import { useUser } from "@/lib/user"

export const DashboardHeader = () => {
  const { user } = useUser()

  return (
    <header className="sticky top-0 z-40 flex h-16 bg-white shadow-sm">
      <div className="flex w-full items-center justify-between px-6">
        <Link href="/" className="flex items-center gap-2 text-xl font-bold">
          <span>Your Logo</span>
        </Link>
        <div className="ml-auto flex items-center gap-4">
          <ConnectionStatus className="mr-2" />
          <SyncIndicator />
          <NotificationBell />
          <LanguageSelector />
          {user && <UserMenu user={user} />}
        </div>
      </div>
    </header>
  )
}

