"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"
import { SalesAnalytics } from "@/components/analytics/sales-analytics"
import { InventoryAnalytics } from "@/components/analytics/inventory-analytics"
import { CustomerAnalytics } from "@/components/analytics/customer-analytics"
import { PredictiveAnalytics } from "@/components/analytics/predictive-analytics"
import { Loader2, RefreshCw } from "lucide-react"

export default function AdvancedAnalyticsPage() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("sales")
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [analyticsData, setAnalyticsData] = useState<any>({
    sales: null,
    inventory: null,
    customers: null,
    predictive: null,
  })

  // Load analytics data
  useEffect(() => {
    async function loadAnalyticsData() {
      if (!analyticsData[activeTab]) {
        setIsLoading(true)
        try {
          const data = await fetchApi(`/api/analytics/${activeTab}`)
          setAnalyticsData((prev) => ({
            ...prev,
            [activeTab]: data,
          }))
        } catch (error) {
          console.error(`Error loading ${activeTab} analytics:`, error)
          toast({
            title: "Error",
            description: `Failed to load ${activeTab} analytics data`,
            variant: "destructive",
          })
        } finally {
          setIsLoading(false)
        }
      }
    }

    loadAnalyticsData()
  }, [activeTab, analyticsData, toast])

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  // Refresh data
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      const data = await fetchApi(`/api/analytics/${activeTab}?refresh=true`)
      setAnalyticsData((prev) => ({
        ...prev,
        [activeTab]: data,
      }))

      toast({
        title: "Data Refreshed",
        description: `${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} analytics data has been updated`,
      })
    } catch (error) {
      console.error(`Error refreshing ${activeTab} analytics:`, error)
      toast({
        title: "Error",
        description: `Failed to refresh ${activeTab} analytics data`,
        variant: "destructive",
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Advanced Analytics</h2>
          <p className="text-muted-foreground">Gain deeper insights with predictive analytics and trend analysis</p>
        </div>
        <Button onClick={handleRefresh} disabled={isRefreshing}>
          {isRefreshing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Data
            </>
          )}
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="sales">Sales Analytics</TabsTrigger>
          <TabsTrigger value="inventory">Inventory Analytics</TabsTrigger>
          <TabsTrigger value="customers">Customer Analytics</TabsTrigger>
          <TabsTrigger value="predictive">Predictive Analytics</TabsTrigger>
        </TabsList>

        <div className="mt-6">
          {isLoading ? (
            <Card>
              <CardContent className="flex justify-center items-center py-24">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2 text-muted-foreground">Loading analytics data...</span>
              </CardContent>
            </Card>
          ) : (
            <>
              <TabsContent value="sales">
                <SalesAnalytics data={analyticsData.sales} />
              </TabsContent>

              <TabsContent value="inventory">
                <InventoryAnalytics data={analyticsData.inventory} />
              </TabsContent>

              <TabsContent value="customers">
                <CustomerAnalytics data={analyticsData.customers} />
              </TabsContent>

              <TabsContent value="predictive">
                <PredictiveAnalytics data={analyticsData.predictive} />
              </TabsContent>
            </>
          )}
        </div>
      </Tabs>
    </div>
  )
}

