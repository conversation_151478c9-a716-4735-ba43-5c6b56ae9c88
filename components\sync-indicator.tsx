"use client"

import { useEffect, useState } from "react"
import { useSocket } from "@/components/socket-provider"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Wifi, WifiOff, RefreshCw } from "lucide-react"

export function SyncIndicator() {
  const { status, isOnline } = useSocket()
  const [syncing, setSyncing] = useState(false)
  const [lastSynced, setLastSynced] = useState<Date | null>(null)

  // Simulate sync activity
  useEffect(() => {
    if (status === "connected" && isOnline) {
      // Set last synced time when connected
      setLastSynced(new Date())

      // Simulate periodic sync
      const syncInterval = setInterval(() => {
        setSyncing(true)

        // Simulate sync completion after 1-2 seconds
        setTimeout(
          () => {
            setSyncing(false)
            setLastSynced(new Date())
          },
          Math.random() * 1000 + 1000,
        )
      }, 30000) // Sync every 30 seconds

      return () => clearInterval(syncInterval)
    }
  }, [status, isOnline])

  // Get status text and color
  const getStatusInfo = () => {
    if (!isOnline) {
      return {
        icon: <WifiOff className="h-4 w-4" />,
        text: "Offline",
        variant: "destructive" as const,
        tooltip: "You're offline. Changes will be saved locally.",
      }
    }

    if (status === "connected") {
      if (syncing) {
        return {
          icon: <RefreshCw className="h-4 w-4 animate-spin" />,
          text: "Syncing...",
          variant: "outline" as const,
          tooltip: "Synchronizing data with the server.",
        }
      }
      return {
        icon: <Wifi className="h-4 w-4" />,
        text: "Online",
        variant: "default" as const,
        tooltip: lastSynced ? `Last synced: ${lastSynced.toLocaleTimeString()}` : "Connected to the server.",
      }
    }

    if (status === "connecting" || status === "reconnecting") {
      return {
        icon: <RefreshCw className="h-4 w-4 animate-spin" />,
        text: "Connecting...",
        variant: "outline" as const,
        tooltip: "Attempting to connect to the server.",
      }
    }

    return {
      icon: <WifiOff className="h-4 w-4" />,
      text: "Disconnected",
      variant: "secondary" as const,
      tooltip: "Not connected to the server. Some features may be limited.",
    }
  }

  const { icon, text, variant, tooltip } = getStatusInfo()

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant={variant} className="gap-1">
            {icon}
            <span className="hidden sm:inline">{text}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

