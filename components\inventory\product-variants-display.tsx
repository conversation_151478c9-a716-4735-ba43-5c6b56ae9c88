"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, ChevronUp } from "lucide-react"

interface Variant {
  id: string
  sku: string
  price: number | null
  stockQuantity: number
  isDefault: boolean
  options: {
    optionGroup: {
      name: string
      displayName: string
    }
    optionValue: string
  }[]
}

interface ProductVariantsDisplayProps {
  variants: Variant[]
  productPrice: number
}

export function ProductVariantsDisplay({ variants, productPrice }: ProductVariantsDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (!variants || variants.length === 0) {
    return null
  }

  const formatVariantName = (variant: Variant) => {
    return variant.options.map((option) => `${option.optionGroup.displayName}: ${option.optionValue}`).join(", ")
  }

  const defaultVariant = variants.find((v) => v.isDefault) || variants[0]
  const totalVariants = variants.length

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          {totalVariants} Variants
        </Badge>
        <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsExpanded(!isExpanded)}>
          {isExpanded ? (
            <>
              <ChevronUp className="h-3 w-3 mr-1" />
              Hide
            </>
          ) : (
            <>
              <ChevronDown className="h-3 w-3 mr-1" />
              Show
            </>
          )}
        </Button>
      </div>

      {isExpanded && (
        <div className="pl-4 border-l-2 border-muted space-y-2 text-sm">
          {variants.map((variant) => (
            <div key={variant.id} className="flex justify-between items-center py-1">
              <div className="flex items-center gap-2">
                {variant.isDefault && (
                  <Badge variant="secondary" className="h-5 text-xs">
                    Default
                  </Badge>
                )}
                <span>{formatVariantName(variant)}</span>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-muted-foreground">SKU: {variant.sku}</span>
                <span className={variant.stockQuantity <= 0 ? "text-destructive font-medium" : ""}>
                  Stock: {variant.stockQuantity}
                </span>
                <span className="font-medium">
                  ${variant.price !== null ? variant.price.toFixed(2) : productPrice.toFixed(2)}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

