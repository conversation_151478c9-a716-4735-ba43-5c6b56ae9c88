import type { Server as HTTPServer } from "http"
import { Server as SocketIOServer } from "socket.io"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

// Define the types of events we'll handle
export type SocketEvent =
  | "inventory_update"
  | "order_created"
  | "order_updated"
  | "order_completed"
  | "low_stock_alert"
  | "payment_received"
  | "user_activity"
  | "sync_request"
  | "sync_response"

// Define the payload structure for different event types
export interface SocketPayload {
  id: string
  timestamp: number
  data: any
  source?: string
}

// Connected clients map
const connectedClients = new Map<
  string,
  {
    userId?: string
    storeId?: string
    role?: string
  }
>()

// Initialize Socket.IO server
export function initSocketServer(httpServer: HTTPServer) {
  const io = new SocketIOServer(httpServer, {
    cors: {
      origin: process.env.NEXTAUTH_URL || "*",
      methods: ["GET", "POST"],
      credentials: true,
    },
    path: "/api/socket",
  })

  // Middleware for authentication
  io.use(async (socket, next) => {
    try {
      // Get session token from handshake auth
      const sessionToken = socket.handshake.auth.token

      if (!sessionToken) {
        // Allow connection without authentication for public access
        // but with limited capabilities
        return next()
      }

      // Verify session token (simplified - in a real app, you'd verify against your session store)
      // This is a placeholder for actual session verification
      const session = await getServerSession(authOptions)

      if (session && session.user) {
        // Store user info in the socket
        socket.data.userId = session.user.id
        socket.data.role = session.user.role

        // Store in our connected clients map
        connectedClients.set(socket.id, {
          userId: session.user.id,
          role: session.user.role,
          storeId: session.user.storeId,
        })

        console.log(`Authenticated user connected: ${session.user.email}`)
        return next()
      }

      return next(new Error("Authentication failed"))
    } catch (error) {
      console.error("Socket authentication error:", error)
      return next(new Error("Authentication error"))
    }
  })

  // Connection handler
  io.on("connection", (socket) => {
    console.log(`Client connected: ${socket.id}`)

    // Store connection info if not already stored
    if (!connectedClients.has(socket.id)) {
      connectedClients.set(socket.id, {
        userId: socket.data.userId,
        role: socket.data.role,
        storeId: socket.data.storeId,
      })
    }

    // Handle inventory updates
    socket.on("inventory_update", async (payload: SocketPayload) => {
      try {
        const { data } = payload

        // Process inventory update
        if (data.action === "increment" || data.action === "decrement") {
          const changeAmount =
            data.action === "decrement" ? -Math.abs(data.stockQuantity) : Math.abs(data.stockQuantity)

          // Update product stock in database
          const product = await prisma.product.update({
            where: { id: data.id },
            data: {
              stockQuantity: {
                increment: changeAmount,
              },
              // Record the stock movement
              stockMovements: {
                create: {
                  type: data.action === "decrement" ? "sale" : "restock",
                  quantity: Math.abs(data.stockQuantity),
                  reason: data.reason || (data.action === "decrement" ? "Sale" : "Restock"),
                  reference: data.reference,
                },
              },
            },
            include: {
              category: true,
            },
          })

          // Check if stock is below reorder point
          if (product.stockQuantity <= (product.reorderPoint || 10)) {
            // Emit low stock alert
            io.emit("low_stock_alert", {
              id: `alert-${Date.now()}`,
              timestamp: Date.now(),
              data: {
                id: product.id,
                name: product.name,
                stockQuantity: product.stockQuantity,
                reorderPoint: product.reorderPoint,
                category: product.category.name,
              },
            })
          }

          // Broadcast the update to all clients except sender
          socket.broadcast.emit("inventory_update", {
            ...payload,
            data: {
              id: product.id,
              name: product.name,
              stockQuantity: product.stockQuantity,
              category: product.category.name,
            },
          })

          // Check for low stock and send notifications
          if (product.stockQuantity <= (product.reorderPoint || 10)) {
            // Get all admin and manager users
            const users = await prisma.user.findMany({
              where: {
                role: {
                  in: ["ADMIN", "MANAGER"],
                },
                notificationSettings: {
                  path: ["lowStockAlerts"],
                  equals: true,
                },
              },
            })

            // Send notification to each user
            for (const user of users) {
              await sendNotification(io, user.id, {
                title: "Low Stock Alert",
                message: `${product.name} is running low (${product.stockQuantity} remaining)`,
                type: "warning",
                actionUrl: `/inventory/products/${product.id}`,
              })
            }
          }
        }
      } catch (error) {
        console.error("Error processing inventory update:", error)
      }
    })

    // Handle order creation
    socket.on("order_created", async (payload: SocketPayload) => {
      try {
        const { data: order } = payload

        // Store the order in the database
        const newOrder = await prisma.order.create({
          data: {
            orderNumber: order.orderNumber,
            status: order.status,
            total: order.total,
            tax: order.tax,
            discount: order.discount || 0,
            userId: socket.data.userId || "anonymous",
            storeId: socket.data.storeId || "default",
            customerId: order.customerId,
            items: {
              create: order.items.map((item: any) => ({
                quantity: item.quantity,
                price: item.price,
                productId: item.id,
              })),
            },
            payments: {
              create: {
                amount: order.total,
                method: order.payment,
              },
            },
          },
          include: {
            items: {
              include: {
                product: true,
              },
            },
            customer: true,
          },
        })

        // Broadcast the new order to all clients
        io.emit("order_created", {
          ...payload,
          data: {
            id: newOrder.id,
            orderNumber: newOrder.orderNumber,
            status: newOrder.status,
            total: newOrder.total,
            items: newOrder.items.length,
            customer: newOrder.customer?.name || "Guest",
          },
        })

        // Send notification to admins and managers
        const users = await prisma.user.findMany({
          where: {
            role: {
              in: ["ADMIN", "MANAGER"],
            },
            notificationSettings: {
              path: ["newOrderAlerts"],
              equals: true,
            },
          },
        })

        for (const user of users) {
          await sendNotification(io, user.id, {
            title: "New Order",
            message: `Order #${order.orderNumber} has been created for $${order.total.toFixed(2)}`,
            type: "info",
            actionUrl: `/dashboard/orders/${newOrder.id}`,
          })
        }
      } catch (error) {
        console.error("Error processing order creation:", error)
      }
    })

    // Handle order updates
    socket.on("order_updated", async (payload: SocketPayload) => {
      try {
        const { data } = payload

        // Update the order in the database
        const updatedOrder = await prisma.order.update({
          where: { id: data.id },
          data: {
            status: data.status,
          },
          include: {
            customer: true,
          },
        })

        // If order is completed, emit order_completed event
        if (updatedOrder.status === "Completed") {
          io.emit("order_completed", {
            id: `complete-${updatedOrder.id}`,
            timestamp: Date.now(),
            data: {
              id: updatedOrder.id,
              orderNumber: updatedOrder.orderNumber,
              status: updatedOrder.status,
              total: updatedOrder.total,
              customer: updatedOrder.customer?.name || "Guest",
            },
          })
        } else {
          // Otherwise broadcast the update
          io.emit("order_updated", {
            ...payload,
            data: {
              id: updatedOrder.id,
              orderNumber: updatedOrder.orderNumber,
              status: updatedOrder.status,
              total: updatedOrder.total,
              customer: updatedOrder.customer?.name || "Guest",
            },
          })
        }
      } catch (error) {
        console.error("Error processing order update:", error)
      }
    })

    // Handle user activity
    socket.on("user_activity", (payload: SocketPayload) => {
      // Broadcast user activity to all clients except sender
      socket.broadcast.emit("user_activity", payload)
    })

    // Handle sync requests
    socket.on("sync_request", async (payload: SocketPayload) => {
      try {
        const { data } = payload
        const clientId = socket.id

        // Get the requested data
        let responseData: any = {}

        if (data.type === "inventory") {
          // Get inventory data
          const products = await prisma.product.findMany({
            where: {
              storeId: socket.data.storeId || "default",
            },
            include: {
              category: true,
              supplier: true,
            },
            take: data.limit || 100,
            skip: data.offset || 0,
          })

          responseData = { products }
        } else if (data.type === "orders") {
          // Get orders data
          const orders = await prisma.order.findMany({
            where: {
              storeId: socket.data.storeId || "default",
            },
            include: {
              customer: true,
              items: {
                include: {
                  product: true,
                },
              },
            },
            orderBy: {
              createdAt: "desc",
            },
            take: data.limit || 50,
            skip: data.offset || 0,
          })

          responseData = { orders }
        }

        // Send response directly to the requesting client
        io.to(clientId).emit("sync_response", {
          id: `sync-${Date.now()}`,
          timestamp: Date.now(),
          data: {
            type: data.type,
            ...responseData,
          },
          requestId: payload.id,
        })
      } catch (error) {
        console.error("Error processing sync request:", error)
      }
    })

    // Handle disconnection
    socket.on("disconnect", () => {
      console.log(`Client disconnected: ${socket.id}`)
      connectedClients.delete(socket.id)
    })
  })

  console.log("Socket.IO server initialized")
  return io
}

// Add this function to your existing socket-server.ts file

// Function to send a notification to a specific user
async function sendNotification(io, userId, notification) {
  try {
    // Save notification to database
    const savedNotification = await prisma.notification.create({
      data: {
        userId,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        actionUrl: notification.actionUrl,
        expiresAt: notification.expiresAt,
      },
    })

    // Find all socket connections for this user
    const userSockets = Array.from(connectedClients.entries())
      .filter(([_, client]) => client.userId === userId)
      .map(([socketId, _]) => socketId)

    // Emit notification to all user's connected devices
    userSockets.forEach((socketId) => {
      io.to(socketId).emit("notification", {
        ...savedNotification,
        id: savedNotification.id,
      })
    })

    // If email notifications are enabled for this user, send email
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
        name: true,
        notificationSettings: true,
      },
    })

    if (
      user?.notificationSettings?.emailNotifications &&
      user.email &&
      (notification.type === "warning" || notification.type === "error")
    ) {
      // Send email notification (implementation depends on your email service)
      await sendEmail({
        to: user.email,
        subject: notification.title,
        text: notification.message,
        html: `<p>${notification.message}</p>${notification.actionUrl ? `<p><a href="${notification.actionUrl}">View details</a></p>` : ""}`,
      })
    }

    return savedNotification
  } catch (error) {
    console.error("Error sending notification:", error)
    return null
  }
}

// Add email sending function (implementation depends on your email service)
async function sendEmail({ to, subject, text, html }) {
  // This is a placeholder - implement with your preferred email service
  console.log(`Sending email to ${to}: ${subject}`)

  // Example with nodemailer
  // const transporter = nodemailer.createTransport({...})
  // await transporter.sendMail({ from: '<EMAIL>', to, subject, text, html })
}

