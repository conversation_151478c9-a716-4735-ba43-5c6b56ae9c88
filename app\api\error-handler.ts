import type { NextRequest } from "next/server"
import { handleApiError } from "@/lib/api-error"
import { ZodError } from "zod"

export async function with<PERSON>rror<PERSON>andler(
  req: NextRequest,
  handler: (req: NextRequest) => Promise<Response>,
): Promise<Response> {
  try {
    return await handler(req)
  } catch (error) {
    if (error instanceof ZodError) {
      return Response.json(
        {
          error: {
            message: "Validation error",
            errors: error.errors,
          },
        },
        { status: 400 },
      )
    }

    return handleApiError(error)
  }
}

