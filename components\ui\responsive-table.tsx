"use client"

import type React from "react"

import { useMobile } from "@/hooks/use-mobile"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card } from "@/components/ui/card"

interface Column {
  key: string
  header: string
  cell?: (item: any) => React.ReactNode
}

interface ResponsiveTableProps {
  data: any[]
  columns: Column[]
  className?: string
  emptyMessage?: string
}

export function ResponsiveTable({
  data,
  columns,
  className,
  emptyMessage = "No data available",
}: ResponsiveTableProps) {
  const isMobile = useMobile()

  if (!data || data.length === 0) {
    return <div className="text-center p-4 text-muted-foreground">{emptyMessage}</div>
  }

  if (isMobile) {
    return (
      <div className={`space-y-4 ${className}`}>
        {data.map((item, index) => (
          <Card key={index} className="p-4">
            {columns.map((column) => (
              <div key={column.key} className="flex justify-between py-2 border-b last:border-0">
                <div className="font-medium">{column.header}</div>
                <div className="text-right">{column.cell ? column.cell(item) : item[column.key]}</div>
              </div>
            ))}
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className={`rounded-md border ${className}`}>
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column.key}>{column.header}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item, index) => (
            <TableRow key={index}>
              {columns.map((column) => (
                <TableCell key={column.key}>{column.cell ? column.cell(item) : item[column.key]}</TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

