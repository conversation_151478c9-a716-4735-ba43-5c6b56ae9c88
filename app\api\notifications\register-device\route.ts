import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"

export async function POST(req: NextRequest) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get the token from the request body
    const { token } = await req.json()

    if (!token) {
      return NextResponse.json({ error: "Token is required" }, { status: 400 })
    }

    // Save or update the device token in the database
    await prisma.deviceToken.upsert({
      where: {
        token_userId: {
          token,
          userId: session.user.id,
        },
      },
      update: {
        lastUpdated: new Date(),
      },
      create: {
        token,
        userId: session.user.id,
        createdAt: new Date(),
        lastUpdated: new Date(),
      },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error registering device:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

