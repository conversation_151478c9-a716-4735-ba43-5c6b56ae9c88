import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getSalesTrends, getInventoryAnalytics, getCustomerAnalytics } from "@/lib/services/analytics-service"
import { getPredictiveAnalytics } from "@/lib/services/predictive-service"

export async function GET(request: Request, { params }: { params: { type: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const analyticsType = params.type

    // Check if we should refresh the data
    const { searchParams } = new URL(request.url)
    const refresh = searchParams.get("refresh") === "true"

    // Get analytics data based on type
    let data

    switch (analyticsType) {
      case "sales":
        data = await getSalesTrends()
        break
      case "inventory":
        data = await getInventoryAnalytics()
        break
      case "customers":
        data = await getCustomerAnalytics()
        break
      case "predictive":
        data = await getPredictiveAnalytics()
        break
      default:
        return NextResponse.json({ error: "Invalid analytics type" }, { status: 400 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error(`Error fetching ${params.type} analytics:`, error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

