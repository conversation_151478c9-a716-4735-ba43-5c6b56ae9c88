"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { DateRangePicker } from "@/components/reports/date-range-picker"
import { SalesReportChart } from "@/components/reports/sales-report-chart"
import { InventoryReportChart } from "@/components/reports/inventory-report-chart"
import { CustomerReportChart } from "@/components/reports/customer-report-chart"
import { ReportFilters } from "@/components/reports/report-filters"
import { ReportExport } from "@/components/reports/report-export"
import { useReportData } from "@/hooks/use-report-data"
import { ReportMetrics } from "@/components/reports/report-metrics"
import { ReportDataTable } from "@/components/reports/report-data-table"
import type { DateRange } from "react-day-picker"
import { subDays } from "date-fns"

export default function ReportsPage() {
  // Default date range (last 30 days)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 30),
    to: new Date(),
  })

  // Report type
  const [reportType, setReportType] = useState<"sales" | "inventory" | "customers">("sales")

  // Filters
  const [filters, setFilters] = useState({
    category: "all",
    store: "all",
    product: "all",
    groupBy: "day",
  })

  // Fetch report data based on selected parameters
  const { data, isLoading, error } = useReportData(reportType, dateRange, filters)

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
          <p className="text-muted-foreground">Analyze your business performance and generate custom reports</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} />
          <ReportExport data={data} reportType={reportType} dateRange={dateRange} />
        </div>
      </div>

      <Tabs defaultValue="sales" onValueChange={(value) => setReportType(value as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sales">Sales Reports</TabsTrigger>
          <TabsTrigger value="inventory">Inventory Reports</TabsTrigger>
          <TabsTrigger value="customers">Customer Reports</TabsTrigger>
        </TabsList>

        <div className="mt-4">
          <ReportFilters reportType={reportType} filters={filters} onFiltersChange={setFilters} />
        </div>

        <ReportMetrics data={data} reportType={reportType} isLoading={isLoading} />

        <TabsContent value="sales" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sales Performance</CardTitle>
              <CardDescription>View your sales performance over time</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <SalesReportChart data={data} isLoading={isLoading} groupBy={filters.groupBy} />
            </CardContent>
          </Card>

          <ReportDataTable data={data?.tableData} reportType={reportType} isLoading={isLoading} />
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Analysis</CardTitle>
              <CardDescription>Track inventory levels and movement</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <InventoryReportChart data={data} isLoading={isLoading} />
            </CardContent>
          </Card>

          <ReportDataTable data={data?.tableData} reportType={reportType} isLoading={isLoading} />
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Insights</CardTitle>
              <CardDescription>Analyze customer behavior and purchasing patterns</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <CustomerReportChart data={data} isLoading={isLoading} />
            </CardContent>
          </Card>

          <ReportDataTable data={data?.tableData} reportType={reportType} isLoading={isLoading} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

