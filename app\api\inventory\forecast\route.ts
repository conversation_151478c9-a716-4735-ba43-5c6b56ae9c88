import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { ApiError } from "@/lib/api-error"
import { generateInventoryForecast } from "@/lib/services/inventory-forecast"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    const searchParams = req.nextUrl.searchParams
    const productId = searchParams.get("productId") || undefined
    const categoryId = searchParams.get("categoryId") || undefined
    const startDate = searchParams.get("startDate") || undefined
    const endDate = searchParams.get("endDate") || undefined
    const forecastDays = searchParams.get("forecastDays")
      ? Number.parseInt(searchParams.get("forecastDays")!, 10)
      : undefined

    const forecast = await generateInventoryForecast({
      productId,
      categoryId,
      startDate,
      endDate,
      forecastDays,
    })

    return NextResponse.json(forecast)
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Forecast generation error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

