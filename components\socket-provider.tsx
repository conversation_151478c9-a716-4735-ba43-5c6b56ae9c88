"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState, useCallback, useRef } from "react"
import { io, type Socket } from "socket.io-client"
import { useToast } from "@/hooks/use-toast"
import { useOfflineStore } from "@/lib/store"

type SocketStatus = "connecting" | "connected" | "disconnected"

interface SocketContextType {
  socket: Socket | null
  status: SocketStatus
  emit: (event: string, data: any) => void
  reconnect: () => void
  isOnline: boolean
  isReconnecting: boolean
  reconnectAttempts: number
  subscribe: (event: string, callback: (data: any) => void) => () => void
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  status: "disconnected",
  emit: () => {},
  reconnect: () => {},
  isOnline: false,
  isReconnecting: false,
  reconnectAttempts: 0,
  subscribe: () => () => {},
})

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [status, setStatus] = useState<SocketStatus>("disconnected")
  const { toast } = useToast()
  const { setIsOffline, offlineTransactions, markTransactionSynced } = useOfflineStore()

  // Track reconnection attempts
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Add these state variables after the existing useState declarations
  const [reconnectAttemptsState, setReconnectAttempts] = useState(0)
  const [reconnectInterval, setReconnectInterval] = useState(1000) // Start with 1 second
  const [isReconnecting, setIsReconnecting] = useState(false)
  const maxReconnectAttemptsState = 10 // Maximum number of reconnection attempts
  const maxReconnectInterval = 60000 // Maximum interval of 1 minute
  const reconnectTimeoutRefState = useRef<NodeJS.Timeout | null>(null)

  // Create a function to initialize the socket
  const initializeSocket = useCallback(() => {
    try {
      const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin

      const socketInstance = io(socketUrl, {
        reconnectionAttempts: maxReconnectAttempts,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        auth: {
          token: localStorage.getItem("token") || "anonymous",
          deviceId: localStorage.getItem("deviceId") || `device_${Math.random().toString(36).substring(2, 9)}`,
        },
      })

      setSocket(socketInstance)
      setStatus("connecting")

      // Store device ID if not already stored
      if (!localStorage.getItem("deviceId")) {
        localStorage.setItem("deviceId", `device_${Math.random().toString(36).substring(2, 9)}`)
      }

      return socketInstance
    } catch (error) {
      console.error("Socket initialization error:", error)
      setStatus("disconnected")
      setIsOffline(true)
      return null
    }
  }, [setIsOffline])

  // Function to manually reconnect
  const reconnect = useCallback(() => {
    if (socket) {
      // Reset reconnection state
      setReconnectAttempts(0)
      setReconnectInterval(1000)
      setIsReconnecting(true)

      // Clear any existing timeout
      if (reconnectTimeoutRefState.current) {
        clearTimeout(reconnectTimeoutRefState.current)
        reconnectTimeoutRefState.current = null
      }

      // Disconnect and reconnect
      socket.disconnect()
      socket.connect()

      toast({
        title: "Reconnecting",
        description: "Attempting to reconnect to the server...",
      })
    } else {
      initializeSocket()
    }
  }, [socket, initializeSocket, toast])

  // Initialize socket on component mount
  useEffect(() => {
    const socketInstance = initializeSocket()

    // Cleanup on unmount
    return () => {
      if (socketInstance) {
        socketInstance.off("connect", onConnect)
        socketInstance.off("disconnect", onDisconnect)
        socketInstance.off("connect_error", onConnectError)
      }

      if (reconnectTimeoutRefState.current) {
        clearTimeout(reconnectTimeoutRefState.current)
        reconnectTimeoutRefState.current = null
      }
    }
  }, [initializeSocket])

  // Set up event listeners
  useEffect(() => {
    if (!socket) return

    const onConnect = () => {
      console.log("Socket connected")
      setStatus("connected")
      setIsOffline(false)
      setIsReconnecting(false)
      setReconnectAttempts(0)
      setReconnectInterval(1000) // Reset to initial interval

      // Clear any pending reconnection timeout
      if (reconnectTimeoutRefState.current) {
        clearTimeout(reconnectTimeoutRefState.current)
        reconnectTimeoutRefState.current = null
      }

      // Sync offline transactions
      if (offlineTransactions.length > 0) {
        toast({
          title: "Syncing offline data",
          description: `Syncing ${offlineTransactions.length} offline transactions`,
        })

        offlineTransactions.forEach((transaction) => {
          if (!transaction.synced) {
            socket.emit(
              transaction.type === "order"
                ? "order_created"
                : transaction.type === "inventory"
                  ? "inventory_update"
                  : "customer_created",
              {
                id: transaction.id,
                timestamp: transaction.timestamp,
                data: transaction.data,
              },
            )

            markTransactionSynced(transaction.id)
          }
        })
      }
    }

    const onDisconnect = (reason: string) => {
      console.log("Socket disconnected:", reason)
      setStatus("disconnected")

      // Only set offline mode if the disconnect wasn't intentional
      if (reason !== "io client disconnect") {
        setIsOffline(true)

        // Implement exponential backoff for reconnection
        if (reconnectAttemptsState < maxReconnectAttemptsState) {
          setIsReconnecting(true)

          // Calculate backoff time with jitter
          const jitter = Math.random() * 0.3 + 0.85 // Random value between 0.85 and 1.15
          const nextInterval = Math.min(reconnectInterval * 1.5 * jitter, maxReconnectInterval)

          console.log(`Reconnect attempt ${reconnectAttemptsState + 1} in ${nextInterval / 1000}s`)

          toast({
            title: "Connection lost",
            description: `Attempting to reconnect in ${Math.ceil(nextInterval / 1000)} seconds...`,
          })

          // Clear any existing timeout
          if (reconnectTimeoutRefState.current) {
            clearTimeout(reconnectTimeoutRefState.current)
          }

          reconnectTimeoutRefState.current = setTimeout(() => {
            setReconnectAttempts((prev) => prev + 1)
            setReconnectInterval(nextInterval)
            socket.connect()
          }, nextInterval)
        } else {
          setIsReconnecting(false)
          toast({
            title: "Connection failed",
            description: "Please check your internet connection and try again",
            variant: "destructive",
          })
        }
      }
    }

    const onConnectError = (error: Error) => {
      console.error("Connection error:", error)
      setStatus("disconnected")
      setIsOffline(true)
    }

    socket.on("connect", onConnect)
    socket.on("disconnect", onDisconnect)
    socket.on("connect_error", onConnectError)

    return () => {
      socket.off("connect", onConnect)
      socket.off("disconnect", onDisconnect)
      socket.off("connect_error", onConnectError)
    }
  }, [
    socket,
    toast,
    setIsOffline,
    offlineTransactions,
    markTransactionSynced,
    reconnectInterval,
    reconnectAttemptsState,
    setIsReconnecting,
  ])

  // Wrapper for socket.emit that handles offline mode
  const emit = useCallback(
    (event: string, data: any) => {
      if (socket && status === "connected") {
        socket.emit(event, data)
        return true
      } else {
        console.warn(`Socket emit failed (${status}):`, event, data)
        return false
      }
    },
    [socket, status],
  )

  // Add this useEffect after the socket event listeners
  useEffect(() => {
    // Handle browser online/offline events
    const handleOnline = () => {
      console.log("Browser online event")
      if (status !== "connected" && socket) {
        toast({
          title: "Network connection restored",
          description: "Reconnecting to server...",
        })
        reconnect()
      }
    }

    const handleOffline = () => {
      console.log("Browser offline event")
      setStatus("disconnected")
      setIsOffline(true)
      toast({
        title: "Network connection lost",
        description: "Operating in offline mode",
        variant: "destructive",
      })
    }

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [status, socket, reconnect, setIsOffline, toast])

  return (
    <SocketContext.Provider
      value={{
        socket,
        status,
        emit,
        reconnect,
        isOnline: status === "connected",
        isReconnecting,
        reconnectAttempts: reconnectAttemptsState,
        subscribe: (event, callback) => {
          if (!socket) return () => {}

          socket.on(event, callback)
          return () => {
            socket.off(event, callback)
          }
        },
      }}
    >
      {children}
    </SocketContext.Provider>
  )
}

export const useSocket = () => useContext(SocketContext)

