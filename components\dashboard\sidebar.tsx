"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { LayoutDashboard, ShoppingCart, Package, Tags, Users, Truck, FileText, Settings, BarChart3 } from "lucide-react"

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items: {
    href: string
    title: string
    icon: React.ReactNode
    submenu?: {
      href: string
      title: string
    }[]
  }[]
}

export function Sidebar({ className, items, ...props }: SidebarNavProps) {
  const pathname = usePathname()

  return (
    <nav className={cn("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1", className)} {...props}>
      {items.map((item) => {
        const isActive = pathname === item.href
        const hasSubmenu = item.submenu && item.submenu.length > 0
        const isSubmenuActive = hasSubmenu && item.submenu?.some((subitem) => pathname === subitem.href)

        return (
          <div key={item.href} className="space-y-1">
            <Link
              href={item.href}
              className={cn(
                "group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                isActive || isSubmenuActive ? "bg-accent text-accent-foreground" : "transparent",
              )}
            >
              {item.icon}
              <span className="ml-3">{item.title}</span>
            </Link>
            {hasSubmenu && (
              <div className="ml-6 space-y-1">
                {item.submenu?.map((subitem) => (
                  <Link
                    key={subitem.href}
                    href={subitem.href}
                    className={cn(
                      "group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                      pathname === subitem.href ? "bg-accent/50 text-accent-foreground" : "transparent",
                    )}
                  >
                    <span>{subitem.title}</span>
                  </Link>
                ))}
              </div>
            )}
          </div>
        )
      })}
    </nav>
  )
}

export function DashboardSidebar() {
  const sidebarItems = [
    {
      href: "/dashboard",
      title: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      href: "/dashboard/pos",
      title: "Point of Sale",
      icon: <ShoppingCart className="h-5 w-5" />,
    },
    {
      href: "/dashboard/products",
      title: "Inventory",
      icon: <Package className="h-5 w-5" />,
      submenu: [
        {
          href: "/dashboard/products",
          title: "Products",
        },
        {
          href: "/dashboard/inventory/serial-numbers",
          title: "Serial Numbers",
        },
        {
          href: "/dashboard/inventory/forecast",
          title: "Forecasting",
        },
      ],
    },
    {
      href: "/dashboard/categories",
      title: "Categories",
      icon: <Tags className="h-5 w-5" />,
    },
    {
      href: "/dashboard/suppliers",
      title: "Suppliers",
      icon: <Truck className="h-5 w-5" />,
    },
    {
      href: "/dashboard/orders",
      title: "Sales",
      icon: <FileText className="h-5 w-5" />,
      submenu: [
        {
          href: "/dashboard/orders",
          title: "Orders",
        },
        {
          href: "/dashboard/returns",
          title: "Returns",
        },
        {
          href: "/dashboard/layaways",
          title: "Layaways",
        },
        {
          href: "/dashboard/recurring-orders",
          title: "Recurring Orders",
        },
      ],
    },
    {
      href: "/dashboard/customers",
      title: "Customers",
      icon: <Users className="h-5 w-5" />,
    },
    {
      href: "/dashboard/reports",
      title: "Reports",
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      href: "/dashboard/settings",
      title: "Settings",
      icon: <Settings className="h-5 w-5" />,
    },
  ]

  return <Sidebar items={sidebarItems} />
}

