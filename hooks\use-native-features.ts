"use client"

import { useState, useEffect } from "react"
import { Capacitor } from "@capacitor/core"
import { NativeStorage, NativeCamera } from "@/lib/native/native-features"

export function useNativeFeatures() {
  const [isNative, setIsNative] = useState(false)
  const [hasCamera, setHasCamera] = useState(false)
  const [hasPushNotifications, setHasPushNotifications] = useState(false)

  useEffect(() => {
    // Check if running in a native app
    const isNativePlatform = Capacitor.isNativePlatform()
    setIsNative(isNativePlatform)

    // Check for camera availability
    const checkCamera = async () => {
      if (!isNativePlatform) return

      try {
        const camera = (Capacitor as any).Plugins.Camera
        if (camera) {
          const permission = await camera.checkPermissions()
          setHasCamera(permission.camera === "granted")
        }
      } catch (error) {
        console.error("Error checking camera:", error)
      }
    }

    // Check for push notification availability
    const checkPushNotifications = async () => {
      if (!isNativePlatform) return

      try {
        const pushNotifications = (Capacitor as any).Plugins.PushNotifications
        if (pushNotifications) {
          const permission = await pushNotifications.checkPermissions()
          setHasPushNotifications(permission.receive === "granted")
        }
      } catch (error) {
        console.error("Error checking push notifications:", error)
      }
    }

    checkCamera()
    checkPushNotifications()
  }, [])

  // Take a picture using the native camera
  const takePicture = async (): Promise<string | null> => {
    try {
      return await NativeCamera.takePicture()
    } catch (error) {
      console.error("Error taking picture:", error)
      return null
    }
  }

  // Scan a barcode using the native camera
  const scanBarcode = async (): Promise<string | null> => {
    try {
      return await NativeCamera.scanBarcode()
    } catch (error) {
      console.error("Error scanning barcode:", error)
      return null
    }
  }

  // Store data in native storage
  const storeData = async (key: string, value: any): Promise<boolean> => {
    try {
      await NativeStorage.set(key, value)
      return true
    } catch (error) {
      console.error("Error storing data:", error)
      return false
    }
  }

  // Retrieve data from native storage
  const getData = async (key: string): Promise<any> => {
    try {
      return await NativeStorage.get(key)
    } catch (error) {
      console.error("Error getting data:", error)
      return null
    }
  }

  return {
    isNative,
    hasCamera,
    hasPushNotifications,
    takePicture,
    scanBarcode,
    storeData,
    getData,
  }
}

