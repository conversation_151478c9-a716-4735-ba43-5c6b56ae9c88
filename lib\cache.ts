import { Redis } from "@upstash/redis"
import { auth } from "@/lib/auth"

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL || "",
  token: process.env.UPSTASH_REDIS_REST_TOKEN || "",
})

// Cache TTL defaults
const DEFAULT_TTL = 60 * 60 // 1 hour
const SHORT_TTL = 60 * 5 // 5 minutes
const LONG_TTL = 60 * 60 * 24 // 24 hours

// Helper to generate user-specific cache keys
async function getUserCacheKey(baseKey: string): Promise<string> {
  const session = await auth()
  const userId = session?.user?.id || "anonymous"
  return `user:${userId}:${baseKey}`
}

// Generic cache get function
export async function getCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl: number = DEFAULT_TTL,
  userSpecific = true,
): Promise<T> {
  const cacheKey = userSpecific ? await getUserCacheKey(key) : key

  try {
    // Try to get from cache first
    const cachedData = await redis.get<T>(cacheKey)

    if (cachedData) {
      return cachedData
    }

    // If not in cache, fetch fresh data
    const freshData = await fetchFn()

    // Store in cache for future requests
    await redis.set(cacheKey, freshData, { ex: ttl })

    return freshData
  } catch (error) {
    console.error("Cache error:", error)
    // Fallback to direct fetch on cache error
    return fetchFn()
  }
}

// Cache invalidation function
export async function invalidateCache(key: string, userSpecific = true): Promise<void> {
  try {
    const cacheKey = userSpecific ? await getUserCacheKey(key) : key
    await redis.del(cacheKey)
  } catch (error) {
    console.error("Cache invalidation error:", error)
  }
}

// Invalidate multiple cache keys with pattern matching
export async function invalidateCachePattern(pattern: string, userSpecific = true): Promise<void> {
  try {
    const basePattern = userSpecific ? `user:${(await auth())?.user?.id || "anonymous"}:${pattern}` : pattern

    const keys = await redis.keys(basePattern)

    if (keys.length > 0) {
      await redis.del(...keys)
    }
  } catch (error) {
    console.error("Cache pattern invalidation error:", error)
  }
}

