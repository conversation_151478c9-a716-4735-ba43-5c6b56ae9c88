import crypto from "crypto"
import { EncryptionService } from "./encryption-service"

export class SearchableEncryption {
  /**
   * Create a searchable index for encrypted data
   * This generates deterministic tokens that can be used for equality searches
   */
  static createSearchableIndex(value: string): string {
    // Create a deterministic hash of the value
    // This is NOT encryption, just a way to search for exact matches
    return crypto.createHash("sha256").update(value.toLowerCase().trim()).digest("hex")
  }

  /**
   * Encrypt a value and create a searchable index
   */
  static async encryptWithIndex(value: string): Promise<{
    encryptedData: string
    iv: string
    authTag: string
    keyId: string
    searchIndex: string
  }> {
    // Encrypt the value
    const { encryptedData, iv, authTag, keyId } = await EncryptionService.encrypt(value)

    // Create a searchable index
    const searchIndex = this.createSearchableIndex(value)

    return {
      encryptedData,
      iv,
      authTag,
      keyId,
      searchIndex,
    }
  }

  /**
   * Create a search token for finding encrypted values
   */
  static createSearchToken(searchTerm: string): string {
    return this.createSearchableIndex(searchTerm)
  }
}

