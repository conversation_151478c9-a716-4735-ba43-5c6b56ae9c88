import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const reportId = params.id

    // Fetch the report
    const report = await prisma.customReport.findUnique({
      where: { id: reportId },
    })

    if (!report) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 })
    }

    // Check if user has access to this report
    if (report.userId !== session.user.id && !report.isPublic) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json(report)
  } catch (error) {
    console.error("Error fetching custom report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const reportId = params.id

    // Fetch the report to check ownership
    const report = await prisma.customReport.findUnique({
      where: { id: reportId },
    })

    if (!report) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 })
    }

    // Only the owner can delete a report
    if (report.userId !== session.user.id) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Delete the report
    await prisma.customReport.delete({
      where: { id: reportId },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting custom report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

