"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { Package, Edit, Trash2, Share2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { TouchButton } from "@/components/ui/touch-button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useMobile } from "@/hooks/use-mobile"
import { useMobileData } from "@/hooks/use-mobile-data"
import { useToast } from "@/hooks/use-toast"
import { formatCurrency } from "@/lib/utils"
import Link from "next/link"

interface Product {
  id: string
  name: string
  description: string
  price: number
  cost: number
  sku: string
  barcode: string | null
  categoryId: string
  categoryName: string
  supplierId: string
  supplierName: string
  stock: number
  reorderPoint: number
  image: string | null
  variants: ProductVariant[]
}

interface ProductVariant {
  id: string
  name: string
  sku: string
  price: number
  stock: number
}

export default function MobileProductDetail() {
  const params = useParams()
  const productId = params.id as string
  const isMobile = useMobile()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("details")

  // Fetch product data
  const {
    data: product,
    isLoading,
    error,
    mutate,
  } = useMobileData<Product>({
    fetcher: async () => {
      const res = await fetch(`/api/products/${productId}`)
      if (!res.ok) throw new Error("Failed to fetch product")
      return res.json()
    },
  })

  // Share product
  const shareProduct = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.name || "Product",
          text: `Check out this product: ${product?.name}`,
          url: window.location.href,
        })
      } catch (error) {
        console.error("Error sharing:", error)
      }
    } else {
      // Fallback for browsers that don't support the Web Share API
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link copied",
        description: "Product link copied to clipboard",
      })
    }
  }

  // Delete product
  const deleteProduct = async () => {
    if (!confirm("Are you sure you want to delete this product?")) return

    try {
      const res = await fetch(`/api/products/${productId}`, {
        method: "DELETE",
      })

      if (!res.ok) throw new Error("Failed to delete product")

      toast({
        title: "Product deleted",
        description: "Product has been deleted successfully",
      })

      // Navigate back to products list
      window.history.back()
    } catch (error) {
      toast({
        title: "Delete failed",
        description: "Failed to delete the product",
        variant: "destructive",
      })
    }
  }

  // If not on mobile, redirect to regular product detail
  if (!isMobile && typeof window !== "undefined") {
    window.location.href = `/products/${productId}`
    return null
  }

  if (isLoading) {
    return (
      <div className="space-y-4 animate-pulse">
        <div className="h-48 bg-gray-200 rounded-lg"></div>
        <div className="h-8 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        <div className="h-24 bg-gray-200 rounded"></div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Package className="h-12 w-12 text-muted-foreground mb-4" />
        <h2 className="text-xl font-bold mb-2">Product Not Found</h2>
        <p className="text-muted-foreground mb-6">The product you're looking for doesn't exist or has been removed.</p>
        <Button asChild>
          <Link href="/products">Back to Products</Link>
        </Button>
      </div>
    )
  }

  const stockStatus =
    product.stock <= 0
      ? { label: "Out of Stock", color: "text-red-500 bg-red-50" }
      : product.stock <= product.reorderPoint
        ? { label: "Low Stock", color: "text-amber-500 bg-amber-50" }
        : { label: "In Stock", color: "text-green-500 bg-green-50" }

  return (
    <div className="pb-20">
      {/* Product Image */}
      <div className="relative mb-4 bg-gray-100 rounded-lg overflow-hidden">
        {product.image ? (
          <img src={product.image || "/placeholder.svg"} alt={product.name} className="w-full h-48 object-contain" />
        ) : (
          <div className="w-full h-48 flex items-center justify-center">
            <Package className="h-16 w-16 text-gray-400" />
          </div>
        )}
      </div>

      {/* Product Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-1">{product.name}</h1>
        <div className="flex items-center justify-between">
          <div className="text-xl font-bold text-primary">{formatCurrency(product.price)}</div>
          <div className={`text-sm px-2 py-1 rounded-full ${stockStatus.color}`}>{stockStatus.label}</div>
        </div>
      </div>

      {/* Product Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="details" className="text-sm">
            Details
          </TabsTrigger>
          <TabsTrigger value="inventory" className="text-sm">
            Inventory
          </TabsTrigger>
          <TabsTrigger value="variants" className="text-sm">
            Variants
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="pt-4">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
              <p className="text-sm">{product.description || "No description available"}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">SKU</h3>
                <p className="text-sm">{product.sku}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Barcode</h3>
                <p className="text-sm">{product.barcode || "N/A"}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Category</h3>
                <p className="text-sm">{product.categoryName}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Supplier</h3>
                <p className="text-sm">{product.supplierName}</p>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="inventory" className="pt-4">
          <div className="space-y-4">
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Current Stock</h3>
                    <p className="text-2xl font-bold">{product.stock}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Reorder Point</h3>
                    <p className="text-2xl font-bold">{product.reorderPoint}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Cost</h3>
                <p className="text-sm">{formatCurrency(product.cost)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Profit Margin</h3>
                <p className="text-sm">{Math.round(((product.price - product.cost) / product.price) * 100)}%</p>
              </div>
            </div>

            <TouchButton className="w-full" variant="outline">
              Adjust Inventory
            </TouchButton>
          </div>
        </TabsContent>

        <TabsContent value="variants" className="pt-4">
          {product.variants && product.variants.length > 0 ? (
            <div className="space-y-3">
              {product.variants.map((variant) => (
                <Card key={variant.id}>
                  <CardContent className="p-3">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium">{variant.name}</h3>
                        <p className="text-sm text-muted-foreground">SKU: {variant.sku}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{formatCurrency(variant.price)}</p>
                        <p className="text-sm">Stock: {variant.stock}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>No variants available for this product</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="fixed bottom-20 right-4 flex flex-col gap-2">
        <TouchButton size="icon" variant="outline" className="h-12 w-12 rounded-full shadow-lg" onClick={shareProduct}>
          <Share2 className="h-5 w-5" />
        </TouchButton>

        <TouchButton size="icon" variant="outline" className="h-12 w-12 rounded-full shadow-lg" asChild>
          <Link href={`/products/${productId}/edit`}>
            <Edit className="h-5 w-5" />
          </Link>
        </TouchButton>

        <TouchButton
          size="icon"
          variant="destructive"
          className="h-12 w-12 rounded-full shadow-lg"
          onClick={deleteProduct}
        >
          <Trash2 className="h-5 w-5" />
        </TouchButton>
      </div>
    </div>
  )
}

