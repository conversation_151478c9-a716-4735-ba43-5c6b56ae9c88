"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Loader2, RefreshCw, Key, AlertTriangle, CheckCircle } from "lucide-react"
import { rotateEncryptionKeys } from "@/app/actions/data-protection"
import { toast } from "@/components/ui/use-toast"

interface EncryptionKey {
  id: string
  createdAt: Date
  active: boolean
}

interface EncryptionKeyRotationProps {
  keys: EncryptionKey[]
}

export function EncryptionKeyRotation({ keys }: EncryptionKeyRotationProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isRotating, setIsRotating] = useState(false)

  const handleRotateKeys = async () => {
    setIsRotating(true)

    try {
      await rotateEncryptionKeys()

      toast({
        title: "Keys Rotated",
        description: "Encryption keys have been rotated successfully.",
      })

      setIsOpen(false)

      // Refresh the page to show the new keys
      window.location.reload()
    } catch (error) {
      console.error("Error rotating encryption keys:", error)
      toast({
        title: "Error",
        description: "Failed to rotate encryption keys. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsRotating(false)
    }
  }

  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleString()
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Encryption Keys</CardTitle>
          <CardDescription>Manage the encryption keys used to protect sensitive data</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Key ID</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {keys.map((key) => (
                <TableRow key={key.id}>
                  <TableCell className="font-mono">{key.id}</TableCell>
                  <TableCell>{formatDate(key.createdAt)}</TableCell>
                  <TableCell>
                    {key.active ? (
                      <div className="flex items-center text-green-500">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Active
                      </div>
                    ) : (
                      <div className="text-muted-foreground">Inactive</div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter>
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <Button className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                Rotate Encryption Keys
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Rotate Encryption Keys</DialogTitle>
                <DialogDescription>
                  This will generate a new encryption key and re-encrypt all sensitive data. This process may take some
                  time depending on the amount of data.
                </DialogDescription>
              </DialogHeader>

              <div className="py-4">
                <div className="flex items-center space-x-2 text-amber-500 mb-4">
                  <AlertTriangle className="h-5 w-5" />
                  <p className="font-medium">Key rotation is a resource-intensive operation.</p>
                </div>
                <p className="text-sm text-muted-foreground">During key rotation, the system will:</p>
                <ul className="list-disc list-inside text-sm text-muted-foreground mt-2 space-y-1">
                  <li>Generate a new encryption key</li>
                  <li>Decrypt all sensitive data with the old key</li>
                  <li>Re-encrypt the data with the new key</li>
                  <li>Update all references to use the new key</li>
                </ul>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isRotating}>
                  Cancel
                </Button>
                <Button variant="default" onClick={handleRotateKeys} disabled={isRotating}>
                  {isRotating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Rotating Keys...
                    </>
                  ) : (
                    <>
                      <Key className="mr-2 h-4 w-4" />
                      Rotate Keys
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>About Encryption</CardTitle>
          <CardDescription>How StockSync protects your sensitive data</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Field-Level Encryption</h3>
            <p className="text-sm text-muted-foreground">
              StockSync uses AES-256-GCM encryption to protect sensitive data fields such as customer email addresses,
              phone numbers, and addresses. Each field is encrypted individually with a unique initialization vector.
            </p>
          </div>

          <div>
            <h3 className="font-medium mb-2">Searchable Encryption</h3>
            <p className="text-sm text-muted-foreground">
              For fields that need to be both encrypted and searchable, StockSync uses a special technique that allows
              searching for exact matches while keeping the data encrypted.
            </p>
          </div>

          <div>
            <h3 className="font-medium mb-2">Key Management</h3>
            <p className="text-sm text-muted-foreground">
              Encryption keys are rotated periodically to enhance security. Old keys are retained to ensure data can be
              decrypted if needed.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

