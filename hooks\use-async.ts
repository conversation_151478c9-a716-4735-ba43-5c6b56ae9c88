"use client"

import { useState, useCallback } from "react"
import { useToast } from "@/hooks/use-toast"

interface UseAsyncOptions<T> {
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
  showSuccessToast?: boolean
  showErrorToast?: boolean
  successMessage?: string
  errorMessage?: string
}

export function useAsync<T = any>(asyncFunction: (...args: any[]) => Promise<T>, options: UseAsyncOptions<T> = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [data, setData] = useState<T | null>(null)
  const { toast } = useToast()

  const {
    onSuccess,
    onError,
    showSuccessToast = false,
    showErrorToast = true,
    successMessage = "Operation completed successfully",
    errorMessage = "An error occurred",
  } = options

  const execute = useCallback(
    async (...args: any[]) => {
      try {
        setIsLoading(true)
        setError(null)

        const result = await asyncFunction(...args)

        setData(result)

        if (onSuccess) {
          onSuccess(result)
        }

        if (showSuccessToast) {
          toast({
            title: "Success",
            description: successMessage,
          })
        }

        return result
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err))

        setError(error)

        if (onError) {
          onError(error)
        }

        if (showErrorToast) {
          toast({
            title: "Error",
            description: error.message || errorMessage,
            variant: "destructive",
          })
        }

        throw error
      } finally {
        setIsLoading(false)
      }
    },
    [asyncFunction, onSuccess, onError, showSuccessToast, showErrorToast, successMessage, errorMessage, toast],
  )

  return {
    isLoading,
    error,
    data,
    execute,
    reset: useCallback(() => {
      setError(null)
      setData(null)
    }, []),
  }
}

