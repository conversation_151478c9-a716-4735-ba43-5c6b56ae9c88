# StockSync - Inventory & POS System Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [System Architecture](#system-architecture)
3. [Installation](#installation)
4. [Database Setup](#database-setup)
5. [Core Features](#core-features)
   - [Authentication](#authentication)
   - [Dashboard](#dashboard)
   - [Inventory Management](#inventory-management)
   - [Point of Sale (POS)](#point-of-sale-pos)
   - [Barcode Scanning](#barcode-scanning)
   - [Order Management](#order-management)
   - [Customer Management](#customer-management)
   - [Real-time Synchronization](#real-time-synchronization)
   - [Offline Mode](#offline-mode)
   - [User Profile Management](#user-profile-management)
   - [Settings](#settings)
6. [Technical Details](#technical-details)
   - [WebSocket Integration](#websocket-integration)
   - [State Management](#state-management)
   - [Server Actions](#server-actions)
   - [Database Schema](#database-schema)
7. [Deployment](#deployment)
8. [Troubleshooting](#troubleshooting)
9. [API Reference](#api-reference)
10. [Contributing](#contributing)

## Introduction

StockSync is a modern inventory and point-of-sale (POS) system built with Next.js, Prisma, and Socket.IO. It provides a comprehensive solution for businesses to manage their inventory, process sales, track orders, and analyze performance in real-time. The system is designed to be user-friendly, responsive, and capable of working both online and offline.

Key features include:
- Real-time inventory tracking and management
- Point of sale with barcode scanning support
- Customer relationship management
- Order processing and history
- Sales analytics and reporting
- Multi-device synchronization
- Offline mode with automatic syncing
- User role-based access control

## System Architecture

StockSync follows a modern web application architecture:

- **Frontend**: Next.js with React components and Tailwind CSS
- **Backend**: Next.js API routes and server actions
- **Database**: PostgreSQL with Prisma ORM
- **Real-time Communication**: Socket.IO for WebSocket connections
- **State Management**: Zustand for client-side state
- **Authentication**: NextAuth.js for user authentication

The application uses the Next.js App Router for routing and leverages React Server Components where appropriate. Client-side components are used for interactive elements that require state management or real-time updates.

### Component Structure

