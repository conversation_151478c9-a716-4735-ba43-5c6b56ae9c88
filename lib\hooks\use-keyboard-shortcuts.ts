"use client"

import { useEffect, useCallback } from "react"

type KeyboardShortcut = {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  action: () => void
  description: string
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[]) {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in input fields
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return
      }

      for (const shortcut of shortcuts) {
        if (
          event.key.toLowerCase() === shortcut.key.toLowerCase() &&
          !!event.ctrlKey === !!shortcut.ctrlKey &&
          !!event.altKey === !!shortcut.altKey &&
          !!event.shiftKey === !!shortcut.shiftKey &&
          !!event.metaKey === !!shortcut.metaKey
        ) {
          event.preventDefault()
          shortcut.action()
          return
        }
      }
    },
    [shortcuts],
  )

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown)

    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [handleKeyDown])

  // Return a function to display available shortcuts
  return {
    getShortcutsHelp: () => {
      return shortcuts.map((shortcut) => {
        const keys = []

        if (shortcut.ctrlKey) keys.push("Ctrl")
        if (shortcut.altKey) keys.push("Alt")
        if (shortcut.shiftKey) keys.push("Shift")
        if (shortcut.metaKey) keys.push("⌘")

        keys.push(shortcut.key.toUpperCase())

        return {
          keys: keys.join(" + "),
          description: shortcut.description,
        }
      })
    },
  }
}

