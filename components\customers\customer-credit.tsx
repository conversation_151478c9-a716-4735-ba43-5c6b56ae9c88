"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"
import { fetchApi } from "@/lib/api-client"
import { Loader2, Plus, CreditCard, Trending<PERSON>p, TrendingDown, Alert<PERSON>riangle } from "lucide-react"
import { format } from "date-fns"

interface CreditTransaction {
  id: string
  amount: number
  balance: number
  type: string
  description: string
  createdAt: string
}

interface CustomerCreditProps {
  customerId: string
}

export function CustomerCredit({ customerId }: CustomerCreditProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [customer, setCustomer] = useState<any>(null)
  const [transactions, setTransactions] = useState<CreditTransaction[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false)
  const [newTransaction, setNewTransaction] = useState({
    amount: 0,
    type: "ADD",
    description: "",
  })
  const [creditSettings, setCreditSettings] = useState({
    creditLimit: 0,
    creditHold: false,
  })

  const fetchCreditData = async () => {
    try {
      setIsLoading(true)
      const response = await fetchApi(`/api/customers/${customerId}/credit`)
      setCustomer(response.customer)
      setTransactions(response.transactions)
      setCreditSettings({
        creditLimit: response.customer.creditLimit || 0,
        creditHold: response.customer.creditHold || false,
      })
    } catch (error) {
      console.error("Failed to fetch credit data:", error)
      toast({
        title: "Error",
        description: "Failed to load credit data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchCreditData()
  }, [customerId, toast])

  const handleAddTransaction = async () => {
    try {
      await fetchApi(`/api/customers/${customerId}/credit`, {
        method: "POST",
        body: JSON.stringify(newTransaction),
      })

      toast({
        title: "Success",
        description: "Credit transaction added successfully",
      })

      setIsAddDialogOpen(false)
      setNewTransaction({
        amount: 0,
        type: "ADD",
        description: "",
      })

      fetchCreditData()
    } catch (error) {
      console.error("Failed to add credit transaction:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add credit transaction",
        variant: "destructive",
      })
    }
  }

  const handleUpdateCreditSettings = async () => {
    try {
      await fetchApi(`/api/customers/${customerId}/credit`, {
        method: "PUT",
        body: JSON.stringify(creditSettings),
      })

      toast({
        title: "Success",
        description: "Credit settings updated successfully",
      })

      setIsSettingsDialogOpen(false)
      fetchCreditData()
    } catch (error) {
      console.error("Failed to update credit settings:", error)
      toast({
        title: "Error",
        description: "Failed to update credit settings",
        variant: "destructive",
      })
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "ADD":
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case "USE":
        return <TrendingDown className="h-4 w-4 text-amber-500" />
      case "EXPIRE":
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case "ADJUST":
        return <CreditCard className="h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Store Credit</CardTitle>
              <CardDescription>Manage customer store credit and payment options</CardDescription>
            </div>
            <div className="flex gap-2">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Credit
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Store Credit</DialogTitle>
                    <DialogDescription>Add or remove store credit for this customer.</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="amount" className="text-right">
                        Amount
                      </Label>
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        className="col-span-3"
                        value={newTransaction.amount}
                        onChange={(e) =>
                          setNewTransaction({ ...newTransaction, amount: Number.parseFloat(e.target.value) })
                        }
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="type" className="text-right">
                        Type
                      </Label>
                      <Select
                        value={newTransaction.type}
                        onValueChange={(value) => setNewTransaction({ ...newTransaction, type: value })}
                      >
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ADD">Add Credit</SelectItem>
                          <SelectItem value="USE">Use Credit</SelectItem>
                          <SelectItem value="EXPIRE">Expire Credit</SelectItem>
                          <SelectItem value="ADJUST">Adjust Balance</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="description" className="text-right">
                        Description
                      </Label>
                      <Textarea
                        id="description"
                        className="col-span-3"
                        value={newTransaction.description}
                        onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" onClick={handleAddTransaction}>
                      Add Transaction
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Dialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">Credit Settings</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Credit Settings</DialogTitle>
                    <DialogDescription>Configure credit limits and payment options.</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="creditLimit" className="text-right">
                        Credit Limit
                      </Label>
                      <Input
                        id="creditLimit"
                        type="number"
                        step="0.01"
                        className="col-span-3"
                        value={creditSettings.creditLimit}
                        onChange={(e) =>
                          setCreditSettings({ ...creditSettings, creditLimit: Number.parseFloat(e.target.value) })
                        }
                      />
                    </div>
                    <div className="flex items-center space-x-2 py-2">
                      <Switch
                        id="creditHold"
                        checked={creditSettings.creditHold}
                        onCheckedChange={(checked) => setCreditSettings({ ...creditSettings, creditHold: checked })}
                      />
                      <Label htmlFor="creditHold">Credit Hold</Label>
                    </div>
                    {creditSettings.creditHold && (
                      <div className="rounded-md bg-yellow-50 p-4 text-sm text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-200">
                        <div className="flex items-center">
                          <AlertTriangle className="mr-2 h-4 w-4" />
                          <p>This customer is on credit hold and will not be able to make purchases on credit.</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <DialogFooter>
                    <Button type="button" onClick={handleUpdateCreditSettings}>
                      Save Settings
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Current Balance</div>
              <div className="mt-2 text-3xl font-bold">${customer?.storeCredit.toFixed(2) || "0.00"}</div>
            </div>

            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Credit Limit</div>
              <div className="mt-2 text-3xl font-bold">
                {customer?.creditLimit ? `$${customer.creditLimit.toFixed(2)}` : "No limit"}
              </div>
            </div>

            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Status</div>
              <div className="mt-2 flex items-center">
                <Badge variant={customer?.creditHold ? "destructive" : "default"}>
                  {customer?.creditHold ? "On Hold" : "Active"}
                </Badge>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-medium mb-4">Transaction History</h3>
            {transactions.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Balance</TableHead>
                      <TableHead>Description</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>{format(new Date(transaction.createdAt), "MMM d, yyyy")}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {getTransactionIcon(transaction.type)}
                            <Badge
                              variant={
                                transaction.type === "ADD"
                                  ? "default"
                                  : transaction.type === "USE"
                                    ? "secondary"
                                    : transaction.type === "EXPIRE"
                                      ? "destructive"
                                      : "outline"
                              }
                              className="ml-2"
                            >
                              {transaction.type}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span
                            className={
                              transaction.type === "ADD"
                                ? "text-green-600"
                                : transaction.type === "USE" || transaction.type === "EXPIRE"
                                  ? "text-red-600"
                                  : ""
                            }
                          >
                            {transaction.type === "ADD"
                              ? "+"
                              : transaction.type === "USE" || transaction.type === "EXPIRE"
                                ? "-"
                                : ""}
                            ${transaction.amount.toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell>${transaction.balance.toFixed(2)}</TableCell>
                        <TableCell>{transaction.description}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">No transactions found</div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

