import type { Permission } from "./permissions"

export interface Role {
  id: string
  name: string
  description: string
  permissions: Permission[]
  isCustom: boolean
  createdAt: Date
  updatedAt: Date
}

export interface UserRole {
  userId: string
  roleId: string
  assignedAt: Date
  assignedBy: string
}

// Database schema for roles (for reference)
export const roleSchema = {
  id: "string", // UUID
  name: "string",
  description: "string",
  permissions: "string[]", // Array of permission strings
  isCustom: "boolean",
  createdAt: "date",
  updatedAt: "date",
}

// Database schema for user roles (for reference)
export const userRoleSchema = {
  userId: "string", // References user.id
  roleId: "string", // References role.id
  assignedAt: "date",
  assignedBy: "string", // References user.id of the assigner
}

