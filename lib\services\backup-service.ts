import { prisma } from "@/lib/prisma"
import fs from "fs"
import path from "path"
import { mkdir, readFile } from "fs/promises"
import { v4 as uuidv4 } from "uuid"
import { format } from "date-fns"
import { createGzip } from "zlib"
import { pipeline } from "stream/promises"
import { createReadStream, createWriteStream } from "fs"

// Define backup directory - in production this should be configurable
// and potentially use cloud storage instead of local filesystem
const BACKUP_DIR = process.env.BACKUP_DIR || path.join(process.cwd(), "backups")

// Ensure backup directory exists
async function ensureBackupDir() {
  try {
    await mkdir(BACKUP_DIR, { recursive: true })
  } catch (error) {
    console.error("Failed to create backup directory:", error)
    throw new Error("Failed to create backup directory")
  }
}

// Get all tables that should be backed up
async function getTablesToBackup() {
  // This could be made configurable, but for now we'll back up all main tables
  return [
    "User",
    "Product",
    "Category",
    "Supplier",
    "Customer",
    "Order",
    "OrderItem",
    "Payment",
    "StockMovement",
    "Notification",
    "Store",
  ]
}

// Create a backup of the database
export async function createBackup(description = "") {
  await ensureBackupDir()

  const backupId = uuidv4()
  const timestamp = new Date()
  const formattedDate = format(timestamp, "yyyy-MM-dd-HH-mm-ss")
  const backupName = `backup-${formattedDate}-${backupId.slice(0, 8)}`

  // Create backup metadata
  const metadata = {
    id: backupId,
    name: backupName,
    description: description || `Automatic backup created on ${format(timestamp, "PPpp")}`,
    createdAt: timestamp,
    size: 0,
    tables: [],
    version: "1.0",
  }

  try {
    const tables = await getTablesToBackup()
    const backupData = {}

    // For each table, fetch all records
    for (const table of tables) {
      // @ts-ignore - Prisma client has dynamic properties
      const records = await prisma[table.toLowerCase()].findMany()
      backupData[table] = records
      metadata.tables.push({
        name: table,
        count: records.length,
      })
    }

    // Create backup file path
    const backupFilePath = path.join(BACKUP_DIR, `${backupName}.json.gz`)

    // Write backup data to file with compression
    const jsonData = JSON.stringify(backupData, null, 2)

    // Create a gzip compressed file
    const gzip = createGzip()
    const source = Buffer.from(jsonData)
    const destination = createWriteStream(backupFilePath)

    // Use pipeline for better error handling
    await pipeline(createReadStream(null, { read: () => source }), gzip, destination)

    // Update metadata with file size
    const stats = fs.statSync(backupFilePath)
    metadata.size = stats.size

    // Save backup metadata to database
    await prisma.backup.create({
      data: {
        id: backupId,
        name: backupName,
        description: metadata.description,
        filePath: backupFilePath,
        size: metadata.size,
        tables: JSON.stringify(metadata.tables),
        createdAt: timestamp,
      },
    })

    return {
      success: true,
      backupId,
      metadata,
    }
  } catch (error) {
    console.error("Backup failed:", error)
    return {
      success: false,
      error: "Backup failed: " + error.message,
    }
  }
}

// Restore from a backup
export async function restoreBackup(backupId: string) {
  try {
    // Get backup metadata from database
    const backup = await prisma.backup.findUnique({
      where: { id: backupId },
    })

    if (!backup) {
      throw new Error(`Backup with ID ${backupId} not found`)
    }

    // Read and decompress the backup file
    const compressedData = await readFile(backup.filePath)
    const decompressedData = await new Promise<string>((resolve, reject) => {
      const gunzip = require("zlib").createGunzip()
      let data = ""

      gunzip.on("data", (chunk) => {
        data += chunk
      })

      gunzip.on("end", () => {
        resolve(data)
      })

      gunzip.on("error", reject)

      gunzip.end(compressedData)
    })

    const backupData = JSON.parse(decompressedData)

    // Start a transaction for the restore process
    return await prisma.$transaction(async (tx) => {
      // For each table in the backup, restore the data
      for (const [tableName, records] of Object.entries(backupData)) {
        // Skip if no records to restore
        if (!records || !Array.isArray(records) || records.length === 0) continue

        // Clear existing data from the table
        // @ts-ignore - Prisma client has dynamic properties
        await tx[tableName.toLowerCase()].deleteMany({})

        // Insert the backup records
        // We need to handle records in batches to avoid hitting limits
        const batchSize = 100
        const recordsArray = records as any[]

        for (let i = 0; i < recordsArray.length; i += batchSize) {
          const batch = recordsArray.slice(i, i + batchSize)

          // Use createMany for efficient bulk insert
          // @ts-ignore - Prisma client has dynamic properties
          await tx[tableName.toLowerCase()].createMany({
            data: batch,
            skipDuplicates: true,
          })
        }
      }

      // Log the restore operation
      await tx.backup.update({
        where: { id: backupId },
        data: {
          lastRestoredAt: new Date(),
        },
      })

      return {
        success: true,
        message: `Successfully restored backup ${backup.name}`,
      }
    })
  } catch (error) {
    console.error("Restore failed:", error)
    return {
      success: false,
      error: "Restore failed: " + error.message,
    }
  }
}

// Get all backups
export async function getBackups() {
  try {
    const backups = await prisma.backup.findMany({
      orderBy: { createdAt: "desc" },
    })

    return {
      success: true,
      backups,
    }
  } catch (error) {
    console.error("Failed to get backups:", error)
    return {
      success: false,
      error: "Failed to get backups: " + error.message,
    }
  }
}

// Delete a backup
export async function deleteBackup(backupId: string) {
  try {
    const backup = await prisma.backup.findUnique({
      where: { id: backupId },
    })

    if (!backup) {
      throw new Error(`Backup with ID ${backupId} not found`)
    }

    // Delete the backup file
    if (fs.existsSync(backup.filePath)) {
      fs.unlinkSync(backup.filePath)
    }

    // Delete the backup metadata
    await prisma.backup.delete({
      where: { id: backupId },
    })

    return {
      success: true,
      message: `Successfully deleted backup ${backup.name}`,
    }
  } catch (error) {
    console.error("Failed to delete backup:", error)
    return {
      success: false,
      error: "Failed to delete backup: " + error.message,
    }
  }
}

// Schedule automatic backups
export async function scheduleBackups(settings: {
  enabled: boolean
  frequency: "daily" | "weekly" | "monthly"
  time: string
  retentionCount: number
}) {
  // This would typically be implemented with a cron job or similar
  // For this example, we'll just save the settings
  try {
    await prisma.setting.upsert({
      where: { key: "backup_schedule" },
      update: { value: JSON.stringify(settings) },
      create: {
        key: "backup_schedule",
        value: JSON.stringify(settings),
      },
    })

    return {
      success: true,
      message: "Backup schedule updated",
    }
  } catch (error) {
    console.error("Failed to schedule backups:", error)
    return {
      success: false,
      error: "Failed to schedule backups: " + error.message,
    }
  }
}

// Clean up old backups based on retention policy
export async function cleanupOldBackups(retentionCount: number) {
  try {
    const backups = await prisma.backup.findMany({
      orderBy: { createdAt: "desc" },
    })

    // Keep the most recent backups based on retention count
    const backupsToDelete = backups.slice(retentionCount)

    for (const backup of backupsToDelete) {
      await deleteBackup(backup.id)
    }

    return {
      success: true,
      message: `Cleaned up ${backupsToDelete.length} old backups`,
    }
  } catch (error) {
    console.error("Failed to clean up old backups:", error)
    return {
      success: false,
      error: "Failed to clean up old backups: " + error.message,
    }
  }
}

