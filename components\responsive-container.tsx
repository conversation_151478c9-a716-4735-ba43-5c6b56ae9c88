"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  breakpoints?: {
    sm?: string
    md?: string
    lg?: string
    xl?: string
  }
}

export function ResponsiveContainer({
  children,
  className,
  breakpoints = {
    sm: "max-w-screen-sm",
    md: "max-w-screen-md",
    lg: "max-w-screen-lg",
    xl: "max-w-screen-xl",
  },
}: ResponsiveContainerProps) {
  const [windowWidth, setWindowWidth] = useState<number>(0)

  useEffect(() => {
    // Set initial width
    setWindowWidth(window.innerWidth)

    // Update width on resize
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  // Determine which breakpoint class to use
  let breakpointClass = ""

  if (windowWidth < 640 && breakpoints.sm) {
    breakpointClass = breakpoints.sm
  } else if (windowWidth < 768 && breakpoints.md) {
    breakpointClass = breakpoints.md
  } else if (windowWidth < 1024 && breakpoints.lg) {
    breakpointClass = breakpoints.lg
  } else if (breakpoints.xl) {
    breakpointClass = breakpoints.xl
  }

  return <div className={cn("mx-auto px-4", breakpointClass, className)}>{children}</div>
}

