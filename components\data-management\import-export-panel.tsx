"use client"

import type React from "react"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, Upload, Download, FileText, AlertCircle, CheckCircle } from "lucide-react"
import { toast } from "@/hooks/use-toast"

const importFormSchema = z.object({
  entityType: z.string().min(1, "Entity type is required"),
  updateExisting: z.boolean().default(true),
  skipErrors: z.boolean().default(false),
  validateOnly: z.boolean().default(false),
})

const exportFormSchema = z.object({
  entityType: z.string().min(1, "Entity type is required"),
  format: z.string().min(1, "Format is required"),
})

type ImportFormValues = z.infer<typeof importFormSchema>
type ExportFormValues = z.infer<typeof exportFormSchema>

export function ImportExportPanel() {
  const [activeTab, setActiveTab] = useState("import")
  const [file, setFile] = useState<File | null>(null)
  const [importing, setImporting] = useState(false)
  const [importResult, setImportResult] = useState<any>(null)
  const [exporting, setExporting] = useState(false)

  const importForm = useForm<ImportFormValues>({
    resolver: zodResolver(importFormSchema),
    defaultValues: {
      entityType: "",
      updateExisting: true,
      skipErrors: false,
      validateOnly: false,
    },
  })

  const exportForm = useForm<ExportFormValues>({
    resolver: zodResolver(exportFormSchema),
    defaultValues: {
      entityType: "",
      format: "csv",
    },
  })

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0])
    }
  }

  const handleImport = async (values: ImportFormValues) => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select a file to import",
        variant: "destructive",
      })
      return
    }

    setImporting(true)
    setImportResult(null)

    try {
      const formData = new FormData()
      formData.append("file", file)
      formData.append("entityType", values.entityType)
      formData.append("updateExisting", values.updateExisting.toString())
      formData.append("skipErrors", values.skipErrors.toString())
      formData.append("validateOnly", values.validateOnly.toString())

      const response = await fetch("/api/db/migration/import", {
        method: "POST",
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error?.message || "Failed to import data")
      }

      setImportResult(result)

      toast({
        title: "Import successful",
        description: `Imported ${result.successCount} records with ${result.errorCount} errors`,
        variant: "default",
      })
    } catch (error) {
      console.error("Import error:", error)

      toast({
        title: "Import failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setImporting(false)
    }
  }

  const handleExport = async (values: ExportFormValues) => {
    setExporting(true)

    try {
      // Create URL with query parameters
      const url = `/api/db/migration/export?entityType=${values.entityType}&format=${values.format}`

      // Create a link and trigger download
      const link = document.createElement("a")
      link.href = url
      link.download = `${values.entityType}-export.${values.format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Export initiated",
        description: "Your export has been initiated. The file will download shortly.",
        variant: "default",
      })
    } catch (error) {
      console.error("Export error:", error)

      toast({
        title: "Export failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setExporting(false)
    }
  }

  const handleDownloadTemplate = (entityType: string, format = "csv") => {
    if (!entityType) {
      toast({
        title: "No entity type selected",
        description: "Please select an entity type to download a template",
        variant: "destructive",
      })
      return
    }

    // Create URL with query parameters
    const url = `/api/db/migration/template?entityType=${entityType}&format=${format}`

    // Create a link and trigger download
    const link = document.createElement("a")
    link.href = url
    link.download = `${entityType}-template.${format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Data Migration</CardTitle>
        <CardDescription>Import and export data to and from the system</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="import">Import</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
          </TabsList>

          <TabsContent value="import" className="space-y-4 pt-4">
            <form onSubmit={importForm.handleSubmit(handleImport)}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="entityType">Entity Type</Label>
                  <Select
                    onValueChange={(value) => importForm.setValue("entityType", value)}
                    defaultValue={importForm.watch("entityType")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select entity type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="products">Products</SelectItem>
                      <SelectItem value="customers">Customers</SelectItem>
                      <SelectItem value="suppliers">Suppliers</SelectItem>
                      <SelectItem value="categories">Categories</SelectItem>
                      <SelectItem value="orders">Orders</SelectItem>
                    </SelectContent>
                  </Select>
                  {importForm.formState.errors.entityType && (
                    <p className="text-sm text-red-500">{importForm.formState.errors.entityType.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="file">File</Label>
                  <Input id="file" type="file" accept=".csv,.xlsx,.json" onChange={handleFileChange} />
                  {file && (
                    <p className="text-sm text-gray-500">
                      Selected file: {file.name} ({(file.size / 1024).toFixed(2)} KB)
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="updateExisting"
                      checked={importForm.watch("updateExisting")}
                      onCheckedChange={(checked) => importForm.setValue("updateExisting", checked as boolean)}
                    />
                    <Label htmlFor="updateExisting">Update existing records</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="skipErrors"
                      checked={importForm.watch("skipErrors")}
                      onCheckedChange={(checked) => importForm.setValue("skipErrors", checked as boolean)}
                    />
                    <Label htmlFor="skipErrors">Skip errors and continue</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="validateOnly"
                      checked={importForm.watch("validateOnly")}
                      onCheckedChange={(checked) => importForm.setValue("validateOnly", checked as boolean)}
                    />
                    <Label htmlFor="validateOnly">Validate only (no import)</Label>
                  </div>
                </div>

                {importResult && (
                  <Alert variant={importResult.success ? "default" : "destructive"}>
                    <div className="flex items-center">
                      {importResult.success ? (
                        <CheckCircle className="h-4 w-4 mr-2" />
                      ) : (
                        <AlertCircle className="h-4 w-4 mr-2" />
                      )}
                      <AlertTitle>{importResult.success ? "Import Successful" : "Import Failed"}</AlertTitle>
                    </div>
                    <AlertDescription>
                      {importResult.success ? (
                        <div className="mt-2">
                          <p>Successfully imported {importResult.successCount} records.</p>
                          {importResult.errorCount > 0 && <p>Failed to import {importResult.errorCount} records.</p>}
                        </div>
                      ) : (
                        <p>{importResult.error?.message || "An unexpected error occurred"}</p>
                      )}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex space-x-2">
                  <Button type="submit" disabled={importing}>
                    {importing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        Import
                      </>
                    )}
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => handleDownloadTemplate(importForm.watch("entityType"))}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Download Template
                  </Button>
                </div>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="export" className="space-y-4 pt-4">
            <form onSubmit={exportForm.handleSubmit(handleExport)}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="entityType">Entity Type</Label>
                  <Select
                    onValueChange={(value) => exportForm.setValue("entityType", value)}
                    defaultValue={exportForm.watch("entityType")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select entity type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="products">Products</SelectItem>
                      <SelectItem value="customers">Customers</SelectItem>
                      <SelectItem value="suppliers">Suppliers</SelectItem>
                      <SelectItem value="categories">Categories</SelectItem>
                      <SelectItem value="orders">Orders</SelectItem>
                    </SelectContent>
                  </Select>
                  {exportForm.formState.errors.entityType && (
                    <p className="text-sm text-red-500">{exportForm.formState.errors.entityType.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="format">Format</Label>
                  <Select
                    onValueChange={(value) => exportForm.setValue("format", value)}
                    defaultValue={exportForm.watch("format")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                      <SelectItem value="pdf">PDF</SelectItem>
                    </SelectContent>
                  </Select>
                  {exportForm.formState.errors.format && (
                    <p className="text-sm text-red-500">{exportForm.formState.errors.format.message}</p>
                  )}
                </div>

                <Button type="submit" disabled={exporting}>
                  {exporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Exporting...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Export
                    </>
                  )}
                </Button>
              </div>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

