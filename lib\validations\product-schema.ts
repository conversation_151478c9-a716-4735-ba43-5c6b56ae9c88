import { z } from "zod"

export const productSchema = z.object({
  name: z.string().min(1, "Product name is required").max(100),
  sku: z.string().min(1, "SKU is required").max(50),
  barcode: z.string().optional(),
  description: z.string().optional(),
  price: z.coerce.number().min(0, "Price must be a positive number"),
  stockQuantity: z.coerce
    .number()
    .int("Stock quantity must be an integer")
    .min(0, "Stock quantity must be a positive number"),
  reorderPoint: z.coerce
    .number()
    .int("Reorder point must be an integer")
    .min(0, "Reorder point must be a positive number"),
  categoryId: z.string().min(1, "Category is required"),
  image: z.string().optional(),
})

export type ProductFormValues = z.infer<typeof productSchema>

