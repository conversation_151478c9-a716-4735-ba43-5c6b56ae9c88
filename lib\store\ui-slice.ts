import type { StateCreator } from "zustand"
import type { StoreState } from "./index"

export type ModalType =
  | "product-form"
  | "category-form"
  | "supplier-form"
  | "customer-form"
  | "order-details"
  | "settings"
  | "confirm-delete"
  | null

export interface UISlice {
  // Sidebar state
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void

  // Theme state
  currentTheme: "light" | "dark" | "system"
  setCurrentTheme: (theme: "light" | "dark" | "system") => void

  // Modal state
  activeModal: ModalType
  setActiveModal: (modal: ModalType) => void
}

export const createUISlice: StateCreator<StoreState, [], [], UISlice> = (set) => ({
  // Sidebar state
  sidebarOpen: false,
  setSidebarOpen: (open) => set({ sidebarOpen: open }),

  // Theme state
  currentTheme: "system",
  setCurrentTheme: (theme) => set({ currentTheme: theme }),

  // Modal state
  activeModal: null,
  setActiveModal: (modal) => set({ activeModal: modal }),
})

