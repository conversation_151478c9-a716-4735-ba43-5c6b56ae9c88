import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { ApiError } from "@/lib/api-error"
import { CsvProcessor } from "@/lib/utils/csv-processor"
import { productExportHeaders } from "@/lib/validations/product-import-schema"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      throw ApiError.unauthorized("You must be logged in to perform this action")
    }

    // Check if user has permission to export products
    if (session.user.role !== "ADMIN" && session.user.role !== "MANAGER") {
      throw ApiError.forbidden("You do not have permission to export products")
    }

    const searchParams = req.nextUrl.searchParams
    const categoryId = searchParams.get("categoryId")
    const supplierId = searchParams.get("supplierId")
    const query = searchParams.get("query")

    // Build filter conditions
    const where: any = {}

    if (categoryId) {
      where.categoryId = categoryId
    }

    if (supplierId) {
      where.supplierId = supplierId
    }

    if (query) {
      where.OR = [
        { name: { contains: query, mode: "insensitive" } },
        { sku: { contains: query, mode: "insensitive" } },
        { barcode: { contains: query, mode: "insensitive" } },
      ]
    }

    // Fetch products with related data
    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        supplier: true,
      },
    })

    // Transform products to export format
    const exportData = products.map((product) => ({
      name: product.name,
      sku: product.sku,
      barcode: product.barcode || "",
      description: product.description || "",
      price: product.price,
      cost: product.cost || 0,
      taxRate: product.taxRate || 0,
      stockQuantity: product.stockQuantity,
      reorderPoint: product.reorderPoint || 0,
      categoryName: product.category.name,
      supplierName: product.supplier?.name || "",
    }))

    // Generate CSV
    const csv = CsvProcessor.exportData(exportData, productExportHeaders)

    // Create a batch operation record
    await prisma.batchOperation.create({
      data: {
        type: "export",
        status: "completed",
        fileName: "products-export.csv",
        totalItems: products.length,
        processedItems: products.length,
        successItems: products.length,
        errorItems: 0,
        userId: session.user.id,
        completedAt: new Date(),
      },
    })

    // Return CSV file
    return new NextResponse(csv, {
      headers: {
        "Content-Type": "text/csv",
        "Content-Disposition": 'attachment; filename="products-export.csv"',
      },
    })
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json({ error: { message: error.message, code: error.code } }, { status: error.statusCode })
    }

    console.error("Batch export error:", error)
    return NextResponse.json(
      { error: { message: "An unexpected error occurred", code: "INTERNAL_SERVER_ERROR" } },
      { status: 500 },
    )
  }
}

