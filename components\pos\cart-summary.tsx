"use client"

import { useCart } from "@/lib/store"
import { useMemo } from "react"

export function CartSummary() {
  // Only subscribe to the cart items
  const cartItems = useCart()

  // Memoize calculations to prevent unnecessary recalculations
  const { itemCount, subtotal } = useMemo(() => {
    const count = cartItems.reduce((total, item) => total + item.quantity, 0)
    const total = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
    return { itemCount: count, subtotal: total }
  }, [cartItems])

  return (
    <div className="p-4 border rounded-lg bg-card">
      <h3 className="text-lg font-semibold mb-2">Cart Summary</h3>
      <div className="flex justify-between mb-1">
        <span>Items:</span>
        <span>{itemCount}</span>
      </div>
      <div className="flex justify-between font-medium text-lg">
        <span>Subtotal:</span>
        <span>${subtotal.toFixed(2)}</span>
      </div>
    </div>
  )
}

