"use client"

import type React from "react"
import { useState } from "react"
import { Card } from "@/components/ui/card"
import { SwipeContainer } from "@/components/gestures/swipe-container"
import { cn } from "@/lib/utils"
import type { GestureDirection } from "@/lib/gestures/gesture-system"
import { NativeHaptics } from "@/lib/native/native-features"

interface SwipeAction {
  direction: GestureDirection
  label: string
  color: string
  icon?: React.ReactNode
  onAction: () => void
}

interface SwipeableCardProps {
  children: React.ReactNode
  actions: SwipeAction[]
  className?: string
  contentClassName?: string
  onSwipe?: (direction: GestureDirection) => void
  threshold?: number
}

export function SwipeableCard({
  children,
  actions,
  className,
  contentClassName,
  onSwipe,
  threshold = 100,
}: SwipeableCardProps) {
  const [activeDirection, setActiveDirection] = useState<GestureDirection | null>(null)

  const handleSwipe = (direction: GestureDirection) => {
    setActiveDirection(direction)

    // Find the action for this direction
    const action = actions.find((a) => a.direction === direction)

    if (action) {
      NativeHaptics.impact("medium")
      action.onAction()
    }

    if (onSwipe) {
      onSwipe(direction)
    }

    // Reset after animation
    setTimeout(() => {
      setActiveDirection(null)
    }, 300)
  }

  // Get action for a specific direction
  const getAction = (direction: GestureDirection) => {
    return actions.find((a) => a.direction === direction)
  }

  // Render action indicators
  const renderActionIndicator = (direction: GestureDirection) => {
    const action = getAction(direction)
    if (!action) return null

    const isActive = activeDirection === direction

    // Position based on direction
    const positionClasses = {
      left: "right-0 top-0 bottom-0 w-16 flex items-center justify-center",
      right: "left-0 top-0 bottom-0 w-16 flex items-center justify-center",
      up: "bottom-0 left-0 right-0 h-16 flex items-center justify-center",
      down: "top-0 left-0 right-0 h-16 flex items-center justify-center",
    }

    return (
      <div
        className={cn("absolute transition-opacity", positionClasses[direction], {
          "opacity-100": isActive,
          "opacity-0": !isActive,
        })}
        style={{ backgroundColor: action.color }}
      >
        <div className="text-white flex flex-col items-center">
          {action.icon && <div className="mb-1">{action.icon}</div>}
          <span className="text-sm font-medium">{action.label}</span>
        </div>
      </div>
    )
  }

  return (
    <Card
      className={cn(
        "overflow-hidden relative",
        {
          "transform transition-transform duration-300": activeDirection !== null,
          "translate-x-full": activeDirection === "left",
          "-translate-x-full": activeDirection === "right",
          "translate-y-full": activeDirection === "up",
          "-translate-y-full": activeDirection === "down",
        },
        className,
      )}
    >
      {/* Action indicators */}
      {actions.map((action) => renderActionIndicator(action.direction))}

      <SwipeContainer
        onSwipe={handleSwipe}
        threshold={threshold}
        enableHaptics={true}
        className={cn("relative z-10 bg-card", contentClassName)}
      >
        {children}
      </SwipeContainer>
    </Card>
  )
}

